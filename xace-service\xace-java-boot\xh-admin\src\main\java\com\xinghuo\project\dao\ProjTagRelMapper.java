package com.xinghuo.project.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.entity.ProjTagRelEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目-标签关联表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface ProjTagRelMapper extends XHBaseMapper<ProjTagRelEntity> {

    /**
     * 根据项目ID查询关联的标签列表
     *
     * @param projectId 项目ID
     * @return 关联记录列表
     */
    List<ProjTagRelEntity> selectByProjectId(@Param("projectId") String projectId);

    /**
     * 根据标签ID查询关联的项目列表
     *
     * @param tagId 标签ID
     * @return 关联记录列表
     */
    List<ProjTagRelEntity> selectByTagId(@Param("tagId") String tagId);

    /**
     * 检查项目和标签的关联关系是否存在
     *
     * @param projectId 项目ID
     * @param tagId 标签ID
     * @return 数量
     */
    int checkRelationExists(@Param("projectId") String projectId, @Param("tagId") String tagId);

    /**
     * 删除项目的所有标签关联
     *
     * @param projectId 项目ID
     * @return 删除数量
     */
    int deleteByProjectId(@Param("projectId") String projectId);

    /**
     * 删除标签的所有项目关联
     *
     * @param tagId 标签ID
     * @return 删除数量
     */
    int deleteByTagId(@Param("tagId") String tagId);

    /**
     * 批量插入项目-标签关联关系
     *
     * @param relations 关联关系列表
     * @return 插入数量
     */
    int batchInsert(@Param("relations") List<ProjTagRelEntity> relations);

    /**
     * 根据项目ID列表查询标签关联
     *
     * @param projectIds 项目ID列表
     * @return 关联记录列表
     */
    List<ProjTagRelEntity> selectByProjectIds(@Param("projectIds") List<String> projectIds);

    /**
     * 根据标签ID列表查询项目关联
     *
     * @param tagIds 标签ID列表
     * @return 关联记录列表
     */
    List<ProjTagRelEntity> selectByTagIds(@Param("tagIds") List<String> tagIds);
}
