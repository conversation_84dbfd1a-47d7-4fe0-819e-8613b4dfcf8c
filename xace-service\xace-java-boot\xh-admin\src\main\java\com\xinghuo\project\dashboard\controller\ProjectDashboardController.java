package com.xinghuo.project.dashboard.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.project.biz.service.ContractService;
import com.xinghuo.project.biz.service.OpportunityService;
import com.xinghuo.project.core.service.ProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目管理仪表板控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "项目管理仪表板", description = "项目管理仪表板相关接口")
@RestController
@RequestMapping("/api/project/dashboard")
public class ProjectDashboardController {

    @Resource
    private ProjectService projectService;

    @Resource
    private OpportunityService opportunityService;

    @Resource
    private ContractService contractService;

    /**
     * 获取项目概览数据
     */
    @PostMapping("/getProjectOverview")
    @Operation(summary = "获取项目概览数据")
    public ActionResult<Map<String, Object>> getProjectOverview(@RequestBody Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取项目统计数据
        List<Map<String, Object>> projectStats = projectService.getProjectStatistics(params);
        result.put("projectStatistics", projectStats);
        
        // 获取项目健康度统计
        List<Map<String, Object>> healthStats = projectService.getProjectHealthStatistics(params);
        result.put("healthStatistics", healthStats);
        
        return ActionResult.success(result);
    }

    /**
     * 获取商机概览数据
     */
    @PostMapping("/getOpportunityOverview")
    @Operation(summary = "获取商机概览数据")
    public ActionResult<Map<String, Object>> getOpportunityOverview(@RequestBody Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取销售漏斗数据
        List<Map<String, Object>> funnelData = opportunityService.getSalesFunnelData(params);
        result.put("salesFunnel", funnelData);
        
        // 获取商机预测数据
        List<Map<String, Object>> forecastData = opportunityService.getBusinessForecastData(params);
        result.put("businessForecast", forecastData);
        
        // 获取赢单/输单分析数据
        List<Map<String, Object>> winLoseData = opportunityService.getWinLoseAnalysisData(params);
        result.put("winLoseAnalysis", winLoseData);
        
        return ActionResult.success(result);
    }

    /**
     * 获取合同概览数据
     */
    @PostMapping("/getContractOverview")
    @Operation(summary = "获取合同概览数据")
    public ActionResult<Map<String, Object>> getContractOverview(@RequestBody Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取合同统计数据
        List<Map<String, Object>> contractStats = contractService.getContractStatistics(params);
        result.put("contractStatistics", contractStats);
        
        return ActionResult.success(result);
    }

    /**
     * 获取综合仪表板数据
     */
    @PostMapping("/getDashboardData")
    @Operation(summary = "获取综合仪表板数据")
    public ActionResult<Map<String, Object>> getDashboardData(@RequestBody Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取项目概览数据
            List<Map<String, Object>> projectStats = projectService.getProjectStatistics(params);
            result.put("projectStatistics", projectStats);
            
            List<Map<String, Object>> healthStats = projectService.getProjectHealthStatistics(params);
            result.put("healthStatistics", healthStats);
            
            // 获取商机概览数据
            List<Map<String, Object>> funnelData = opportunityService.getSalesFunnelData(params);
            result.put("salesFunnel", funnelData);
            
            List<Map<String, Object>> forecastData = opportunityService.getBusinessForecastData(params);
            result.put("businessForecast", forecastData);
            
            // 获取合同概览数据
            List<Map<String, Object>> contractStats = contractService.getContractStatistics(params);
            result.put("contractStatistics", contractStats);
            
            return ActionResult.success(result);
        } catch (Exception e) {
            log.error("获取仪表板数据失败", e);
            return ActionResult.fail("获取仪表板数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取我的工作台数据
     */
    @GetMapping("/getMyWorkspace/{userId}")
    @Operation(summary = "获取我的工作台数据")
    public ActionResult<Map<String, Object>> getMyWorkspace(@PathVariable String userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            params.put("userId", userId);
            
            // 获取我负责的项目统计
            params.put("managerId", userId);
            List<Map<String, Object>> myProjects = projectService.getProjectStatistics(params);
            result.put("myProjects", myProjects);
            
            // 获取我的商机统计
            params.put("projectLeader", userId);
            List<Map<String, Object>> myOpportunities = opportunityService.getSalesFunnelData(params);
            result.put("myOpportunities", myOpportunities);
            
            // 获取我的合同统计
            params.put("ownId", userId);
            List<Map<String, Object>> myContracts = contractService.getContractStatistics(params);
            result.put("myContracts", myContracts);
            
            return ActionResult.success(result);
        } catch (Exception e) {
            log.error("获取我的工作台数据失败", e);
            return ActionResult.fail("获取我的工作台数据失败：" + e.getMessage());
        }
    }
}
