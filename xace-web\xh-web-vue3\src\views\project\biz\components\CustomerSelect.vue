<template>
  <xh-select
    v-model:value="innerValue"
    v-bind="getSelectBindValue"
    showSearch
    :filter-option="false"
    :not-found-content="fetching ? undefined : null"
    :options="data"
    @change="customerSelectChangeEvent"
    @search="fetchCustomer"
    @popupScroll="handlePopupScroll"
  >
    <template v-if="fetching" #notFoundContent>
      <a-spin size="small" />
    </template>

    <!-- 加载更多选项 -->
    <template v-if="hasMore && data.length > 0" #dropdownRender="{ menuNode }">
      <div>
        <component :is="menuNode" />
        <a-divider style="margin: 4px 0;" />
        <div style="padding: 4px 8px; cursor: pointer; text-align: center;" @click="loadMoreCustomers">
          <a-spin v-if="loadingMore" size="small" />
          <span v-else style="color: #1890ff;">加载更多</span>
        </div>
      </div>
    </template>
  </xh-select>
</template>

<script lang="ts" setup>
  import { ref, computed, watch, unref } from 'vue';
  import { debounce, pick } from 'lodash-es';
  import { Spin } from 'ant-design-vue';
  import { getCustomerList } from '/@/api/project/customer/index';
  const ASpin = Spin;
  const props = defineProps({
    value: {
      type: [String, Array],
      default: '',
    },
    placeholder: {
      type: String,
      default: '请选择客户单位',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    allowClear: {
      type: Boolean,
      default: true,
    },
    custType: {
      type: String,
      default: 'JIAFANG', // 默认为甲方单位
    },
    showSearch: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:value', 'change']);

  // 内部值
  const innerValue = ref(props.value);
  // 数据列表
  const data = ref<any[]>([]);
  // 是否正在加载
  const fetching = ref(false);

  // 监听value变化
  watch(
    () => props.value,
    (val) => {
      innerValue.value = val;
    },
  );

  // 获取Select组件的绑定值
  const getSelectBindValue = computed(() => {
    return {
      ...pick(props, ['disabled', 'placeholder', 'allowClear', 'showSearch', 'multiple']),
    };
  });

  // 当前页码
  const currentPage = ref(1);
  // 是否还有更多数据
  const hasMore = ref(true);
  // 是否正在加载更多
  const loadingMore = ref(false);
  // 搜索关键词
  const searchKeyword = ref('');

  // 加载客户单位列表
  const fetchCustomer = debounce(async (value: string = '') => {
    try {
      console.log('开始获取客户单位列表:', { value, custType: props.custType, currentPage: currentPage.value });

      // 重置搜索时，重置页码和加载状态
      if (searchKeyword.value !== value) {
        currentPage.value = 1;
        hasMore.value = true;
        data.value = [];
      }

      searchKeyword.value = value;
      fetching.value = true;

      const params = {
        name: value || undefined, // 空字符串转为undefined
        custType: props.custType,
        pageSize: 20,
        currentPage: currentPage.value,
      };

      console.log('API请求参数:', params);
      const result = await getCustomerList(params);
      console.log('API返回结果:', result);

      // 根据实际的数据结构处理
      let items: any[] = [];
      let total = 0;

      if (result && result.code === 200 && result.data) {
        items = result.data.list || [];
        total = result.data.pagination?.total || 0;
      } else {
        console.warn('API返回格式不正确:', result);
        items = [];
      }

      console.log('处理后的items:', items, 'total:', total);

      // 判断是否还有更多数据
      const totalPages = Math.ceil(total / params.pageSize);
      hasMore.value = currentPage.value < totalPages;

      // 转换数据格式
      const newData = items.map((item: any) => ({
        label: item.name,
        value: item.id,
        key: item.id,
        fullName: item.name,
        id: item.id,
        custType: item.custType,
        custLine: item.custLine,
        leader: item.leader,
        ...item,
      }));

      console.log('转换后的数据:', newData);

      // 如果是第一页，直接替换数据，否则追加数据
      if (currentPage.value === 1) {
        data.value = newData;
      } else {
        data.value = [...data.value, ...newData];
      }

      console.log('最终data.value:', data.value);
    } catch (error) {
      console.error('获取客户单位列表失败:', error);
      // 添加一些测试数据以便调试
      if (currentPage.value === 1) {
        data.value = [
          { label: '测试客户单位1', value: 'test1', key: 'test1', id: 'test1' },
          { label: '测试客户单位2', value: 'test2', key: 'test2', id: 'test2' },
          { label: '测试客户单位3', value: 'test3', key: 'test3', id: 'test3' },
        ];
      }
    } finally {
      fetching.value = false;
      loadingMore.value = false;
    }
  }, 300);

  // 加载更多数据
  const loadMoreCustomers = async () => {
    if (loadingMore.value || !hasMore.value) return;

    loadingMore.value = true;
    currentPage.value += 1;
    await fetchCustomer(searchKeyword.value);
  };

  // 处理下拉框滚动事件
  const handlePopupScroll = (e: Event) => {
    const { target } = e;
    if (target) {
      const { scrollTop, scrollHeight, clientHeight } = target as HTMLElement;
      // 当滚动到底部时，自动加载更多
      if (scrollTop + clientHeight >= scrollHeight - 10 && hasMore.value && !loadingMore.value) {
        loadMoreCustomers();
      }
    }
  };

  // 选择变更事件
  function customerSelectChangeEvent(value: string | string[]) {
    emit('update:value', value);

    // 查找选中的客户单位数据
    let selectedData: any = null;
    if (props.multiple) {
      selectedData = data.value.filter((item) => (value as string[]).includes(item.value));
    } else {
      selectedData = data.value.find((item) => item.id === value);
    }

    emit('change', value, selectedData);
  }

  // 初始加载
  fetchCustomer('');
</script>
