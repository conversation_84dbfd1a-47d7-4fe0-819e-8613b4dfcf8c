package com.xinghuo.project.portfolio.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目群实体类
 * 对应数据库表：zz_proj_program
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_program")
public class ProgramEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 项目群编码，可自动生成
     */
    @TableField("code")
    private String code;

    /**
     * 项目群名称
     */
    @TableField("name")
    private String name;

    /**
     * 项目群类型ID (关联字典表)
     */
    @TableField("type_id")
    private String typeId;

    /**
     * 项目群经理ID (关联用户表)
     */
    @TableField("manager_id")
    private String managerId;

    /**
     * 项目群描述
     */
    @TableField("description")
    private String description;

    /**
     * 状态 (如: active, planning, closed)
     */
    @TableField("status")
    private String status;
}
