<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="登记开票" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { registerContractMoneyInvoice } from '/@/api/project/contractMoney';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDate } from '/@/utils/dateUtil';

  import type { FormSchema } from '/@/components/Table';
  import type { ContractMoneyModel } from '/@/api/project/contractMoney';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const rowId = ref('');
  const contractInfo = ref<ContractMoneyModel>();

  const formSchema: FormSchema[] = [
    {
      field: 'contractInfo',
      label: '合同信息',
      component: 'Input',
      render: ({ model }) => {
        const info = unref(contractInfo);
        if (!info) return '';
        return `${info.contractNo || ''} - ${info.contractName || ''}`;
      },
      componentProps: {
        disabled: true,
      },
      colProps: { span: 24 },
    },
    {
      field: 'cmMoney',
      label: '收款金额',
      component: 'InputNumber',
      componentProps: {
        disabled: true,
        precision: 2,
        style: 'width: 100%',
        formatter: (value: number) => {
          return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY',
          }).format(value || 0);
        },
      },
      colProps: { span: 12 },
    },
    {
      field: 'currentStatus',
      label: '当前状态',
      component: 'Input',
      componentProps: {
        disabled: true,
      },
      colProps: { span: 12 },
    },
    {
      field: 'kaipiaoDate',
      label: '开票日期',
      component: 'DatePicker',
      required: true,
      componentProps: {
        placeholder: '请选择开票日期',
        style: 'width: 100%',
      },
      colProps: { span: 12 },
    },
    {
      field: 'lastNote',
      label: '备注',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入备注',
        rows: 3,
        maxlength: 500,
      },
      colProps: { span: 24 },
    },
  ];

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    baseColProps: { span: 24 },
    schemas: formSchema,
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });

    if (data?.record) {
      rowId.value = data.record.cmId;
      contractInfo.value = data.record;

      // 获取收款状态文本
      const getPayStatusText = (status: string | number) => {
        const statusStr = String(status);
        const textMap = {
          '0': '未收款',
          '1': '已收款',
        };
        return textMap[statusStr] || status;
      };

      setFieldsValue({
        contractInfo: `${data.record.contractNo || ''} - ${data.record.contractName || ''}`,
        cmMoney: data.record.cmMoney,
        currentStatus: getPayStatusText(data.record.payStatus),
        kaipiaoDate: data.record.kaipiaoDate ? new Date(data.record.kaipiaoDate) : undefined,
        lastNote: data.record.lastNote,
      });
    }
  });

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      await registerContractMoneyInvoice(
        rowId.value,
        formatToDate(values.kaipiaoDate),
        values.lastNote
      );

      createMessage.success('开票登记成功');
      closeModal();
      emit('success');
    } catch (error) {
      createMessage.error('开票登记失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
