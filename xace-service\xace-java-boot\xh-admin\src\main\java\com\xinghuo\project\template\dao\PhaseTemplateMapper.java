package com.xinghuo.project.template.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.template.entity.PhaseTemplateEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 阶段模板Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface PhaseTemplateMapper extends XHBaseMapper<PhaseTemplateEntity> {

    /**
     * 检查阶段编码是否存在
     *
     * @param code 阶段编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkCodeExists(@Param("code") String code, @Param("excludeId") String excludeId);

    /**
     * 根据状态查询阶段模板列表
     *
     * @param status 状态
     * @return 阶段模板列表
     */
    List<PhaseTemplateEntity> selectByStatus(@Param("status") Integer status);

    /**
     * 获取阶段模板选择列表
     *
     * @param keyword 关键字
     * @return 阶段模板列表
     */
    List<PhaseTemplateEntity> selectForSelect(@Param("keyword") String keyword);

    /**
     * 更新阶段模板状态
     *
     * @param id 阶段模板ID
     * @param status 状态
     * @return 更新数量
     */
    int updateStatus(@Param("id") String id, @Param("status") Integer status);

    /**
     * 批量更新状态
     *
     * @param ids ID列表
     * @param status 状态
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("status") Integer status);

    /**
     * 根据编码查询阶段模板
     *
     * @param code 阶段编码
     * @return 阶段模板
     */
    PhaseTemplateEntity selectByCode(@Param("code") String code);

    /**
     * 获取下一个阶段编码序号
     *
     * @return 序号
     */
    Integer getNextCodeSequence();
}
