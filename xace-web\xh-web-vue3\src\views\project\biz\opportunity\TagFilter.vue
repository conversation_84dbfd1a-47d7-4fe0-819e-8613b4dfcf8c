<template>
  <div class="tag-filter">
    <a-radio-group v-model:value="selectedType" button-style="solid" @change="handleTypeChange">
      <a-radio-button value="all">全部</a-radio-button>
      <a-radio-button v-for="type in tagTypes" :key="type.value" :value="type.value">
        {{ type.label }}
      </a-radio-button>
    </a-radio-group>

    <a-select v-if="showSearch" v-model:value="searchField" style="width: 120px; margin-left: 16px" @change="handleSearchFieldChange">
      <a-select-option value="content">内容</a-select-option>
      <a-select-option value="createUserName">操作人</a-select-option>
      <a-select-option value="fieldName">字段名</a-select-option>
    </a-select>

    <a-input-search
      v-if="showSearch"
      v-model:value="searchValue"
      placeholder="搜索..."
      style="width: 200px; margin-left: 8px"
      @search="handleSearch"
      @change="handleSearchChange" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';

  // 标签类型定义
  const tagTypes = [
    { label: '状态变更', value: '状态变更', color: 'orange' },
    { label: '负责人变更', value: '负责人变更', color: 'purple' },
    { label: '金额变更', value: '金额变更', color: 'green' },
    { label: '客户变更', value: '客户变更', color: 'blue' },
    { label: '其他变更', value: '其他变更', color: 'default' },
  ];

  const props = defineProps({
    showSearch: {
      type: Boolean,
      default: true,
    },
  });

  const emit = defineEmits(['filter', 'search']);

  const selectedType = ref('all');
  const searchField = ref('content');
  const searchValue = ref('');

  // 处理类型变更
  function handleTypeChange() {
    emitFilter();
  }

  // 处理搜索字段变更
  function handleSearchFieldChange() {
    if (searchValue.value) {
      emitSearch();
    }
  }

  // 处理搜索值变更
  function handleSearchChange() {
    if (searchValue.value) {
      emitSearch();
    } else {
      // 如果搜索值为空，则清除搜索条件
      emit('search', { field: null, value: null });
    }
  }

  // 处理搜索按钮点击
  function handleSearch() {
    emitSearch();
  }

  // 发送筛选事件
  function emitFilter() {
    emit('filter', selectedType.value === 'all' ? null : selectedType.value);
  }

  // 发送搜索事件
  function emitSearch() {
    emit('search', {
      field: searchField.value,
      value: searchValue.value,
    });
  }

  // 监听搜索值变化
  watch(searchValue, newVal => {
    if (!newVal) {
      // 如果搜索值为空，则清除搜索条件
      emit('search', { field: null, value: null });
    }
  });

  // 暴露方法
  defineExpose({
    reset: () => {
      selectedType.value = 'all';
      searchField.value = 'content';
      searchValue.value = '';
      emitFilter();
      emit('search', { field: null, value: null });
    },
    getTagTypes: () => tagTypes,
  });
</script>

<style lang="less" scoped>
  .tag-filter {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      .ant-input-search {
        margin-top: 8px;
        margin-left: 0 !important;
      }
    }
  }
</style>
