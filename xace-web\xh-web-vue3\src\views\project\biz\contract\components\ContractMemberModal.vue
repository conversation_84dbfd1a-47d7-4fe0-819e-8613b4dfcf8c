<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    width="600px"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    createContractMember,
    updateContractMember,
    ContractMemberFormModel,
    ContractMemberModel,
    RoleTypeEnum,
    RoleTypeTextMap,
    DefaultPermissions,
  } from '/@/api/project/contractMember';
  import { getUserSelector } from '/@/api/permission/user';

  const emit = defineEmits(['register', 'reload']);
  const { createMessage } = useMessage();

  const isUpdate = ref(false);
  const recordId = ref('');
  const contractId = ref('');

  // 表单配置
  const formSchemas: FormSchema[] = [
    {
      field: 'userId',
      label: '选择用户',
      component: 'ApiSelect',
      required: true,
      componentProps: {
        api: getUserSelector,
        labelField: 'fullName',
        valueField: 'id',
        placeholder: '请选择用户',
        showSearch: true,
        filterOption: false,
        resultField: 'data',
        searchParams: (keyword: string) => ({ keyword }),
      },
      colProps: { span: 24 },
    },
    {
      field: 'roleType',
      label: '角色类型',
      component: 'Select',
      required: true,
      componentProps: {
        placeholder: '请选择角色类型',
        options: [
          { label: RoleTypeTextMap[RoleTypeEnum.CONTRACT_ADMIN], value: RoleTypeEnum.CONTRACT_ADMIN },
          { label: RoleTypeTextMap[RoleTypeEnum.PMO], value: RoleTypeEnum.PMO },
          { label: RoleTypeTextMap[RoleTypeEnum.PROJECT_MANAGER], value: RoleTypeEnum.PROJECT_MANAGER },
          { label: RoleTypeTextMap[RoleTypeEnum.TEAM_MEMBER], value: RoleTypeEnum.TEAM_MEMBER },
        ],
        onChange: (value: string) => {
          // 根据角色类型自动设置默认权限
          const permissions = DefaultPermissions[value];
          if (permissions) {
            setFieldsValue({ permissions: JSON.stringify(permissions) });
          }
        },
      },
      colProps: { span: 24 },
    },
    {
      field: 'isPrimary',
      label: '是否主要负责人',
      component: 'RadioButtonGroup',
      defaultValue: 0,
      componentProps: {
        options: [
          { label: '否', value: 0 },
          { label: '是', value: 1 },
        ],
      },
      colProps: { span: 24 },
    },
    {
      field: 'status',
      label: '状态',
      component: 'RadioButtonGroup',
      defaultValue: 1,
      componentProps: {
        options: [
          { label: '禁用', value: 0 },
          { label: '启用', value: 1 },
        ],
      },
      colProps: { span: 24 },
    },
    {
      field: 'permissions',
      label: '权限配置',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '权限配置（JSON格式）',
        rows: 4,
        disabled: true,
      },
      colProps: { span: 24 },
      helpMessage: '权限配置会根据角色类型自动生成，如需自定义请联系管理员',
    },
    {
      field: 'remark',
      label: '备注',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入备注信息',
        rows: 3,
        maxlength: 500,
        showCount: true,
      },
      colProps: { span: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: formSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });

    isUpdate.value = !!data?.isUpdate;
    contractId.value = data?.contractId || '';

    if (unref(isUpdate)) {
      const record: ContractMemberModel = data.record;
      recordId.value = record.id;
      setFieldsValue({
        userId: record.userId,
        roleType: record.roleType,
        isPrimary: record.isPrimary,
        status: record.status,
        permissions: record.permissions,
        remark: record.remark,
      });
    }
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '添加项目成员' : '编辑项目成员'));

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      const formData: ContractMemberFormModel = {
        contractId: contractId.value,
        userId: values.userId,
        roleType: values.roleType,
        isPrimary: values.isPrimary,
        status: values.status,
        permissions: values.permissions,
        remark: values.remark,
      };

      if (unref(isUpdate)) {
        await updateContractMember(recordId.value, formData);
        createMessage.success('更新成员成功');
      } else {
        await createContractMember(formData);
        createMessage.success('添加成员成功');
      }

      closeModal();
      emit('reload');
    } catch (error) {
      console.error('提交失败:', error);
      createMessage.error(unref(isUpdate) ? '更新成员失败' : '添加成员失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  function handleCancel() {
    closeModal();
  }
</script>
