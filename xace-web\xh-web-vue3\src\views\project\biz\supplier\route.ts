import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

// 供应商管理路由配置
export default {
  path: '/project/supplier',
  name: 'ProjectSupplier',
  component: LAYOUT,
  redirect: '/project/supplier/index',
  meta: {
    orderNo: 30,
    icon: 'icon-ym icon-ym-supplier',
    title: t('供应商管理'),
    defaultTitle: '供应商管理',
  },
  children: [
    {
      path: 'index',
      name: 'ProjectSupplierIndex',
      component: () => import('/@/views/project/supplier/index.vue'),
      meta: {
        title: t('供应商列表'),
        defaultTitle: '供应商列表',
        icon: 'icon-ym icon-ym-supplier',
      },
    },
  ],
};
