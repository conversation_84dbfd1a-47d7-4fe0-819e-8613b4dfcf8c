package com.xinghuo.project.biz.model;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "客户分页查询参数")
public class CustomerPagination extends Pagination {

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String name;

    /**
     * 客户类型
     */
    @Schema(description = "客户类型")
    private String custType;

    /**
     * 业务线
     */
    @Schema(description = "业务线")
    private String custLine;

    /**
     * 负责人
     */
    @Schema(description = "负责人")
    private String leader;

    /**
     * 关键字搜索（客户名称）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}
