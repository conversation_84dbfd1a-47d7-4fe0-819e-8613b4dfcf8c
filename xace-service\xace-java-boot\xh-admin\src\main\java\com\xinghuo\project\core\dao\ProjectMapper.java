package com.xinghuo.project.core.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.core.entity.ProjectEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface ProjectMapper extends XHBaseMapper<ProjectEntity> {

    /**
     * 根据项目类型查询项目列表
     *
     * @param projectType 项目类型
     * @return 项目列表
     */
    List<ProjectEntity> selectByProjectType(@Param("projectType") String projectType);

    /**
     * 根据项目状态查询项目列表
     *
     * @param status 项目状态
     * @return 项目列表
     */
    List<ProjectEntity> selectByStatus(@Param("status") String status);

    /**
     * 根据项目经理ID查询项目列表
     *
     * @param managerId 项目经理ID
     * @return 项目列表
     */
    List<ProjectEntity> selectByManagerId(@Param("managerId") String managerId);

    /**
     * 根据部门ID查询项目列表
     *
     * @param deptId 部门ID
     * @return 项目列表
     */
    List<ProjectEntity> selectByDeptId(@Param("deptId") String deptId);

    /**
     * 检查项目编码是否存在
     *
     * @param code 项目编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkCodeExists(@Param("code") String code, @Param("excludeId") String excludeId);

    /**
     * 获取项目统计数据
     *
     * @param params 查询参数
     * @return 统计数据
     */
    List<Map<String, Object>> getProjectStatistics(@Param("params") Map<String, Object> params);

    /**
     * 获取项目健康度统计
     *
     * @param params 查询参数
     * @return 健康度统计
     */
    List<Map<String, Object>> getProjectHealthStatistics(@Param("params") Map<String, Object> params);
}
