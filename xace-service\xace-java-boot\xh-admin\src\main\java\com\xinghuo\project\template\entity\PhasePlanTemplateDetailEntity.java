package com.xinghuo.project.template.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 阶段计划模板明细表实体类
 * 对应数据库表：zz_proj_tpl_phase_plan_detail
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_tpl_phase_plan_detail")
public class PhasePlanTemplateDetailEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 所属模板主表ID
     */
    @TableField("phase_plan_tpl_id")
    private String phasePlanTplId;

    /**
     * 阶段编码 (可从标准阶段库带入或自动生成)
     */
    @TableField("code")
    private String code;

    /**
     * 阶段名称
     */
    @TableField("name")
    private String name;

    /**
     * 阶段描述 (核心目标, 关键工作等)
     */
    @TableField("description")
    private String description;

    /**
     * 序号 (用于排序)
     */
    @TableField("seq_no")
    private Integer seqNo;

    /**
     * 工期 (天数)
     */
    @TableField("duration")
    private Integer duration;

    /**
     * 是否可裁剪 (1:可裁剪, 0:不可裁剪)
     */
    @TableField("can_cut")
    private Integer canCut;

    /**
     * 阶段完成审批流程模板ID
     */
    @TableField("approval_schema_id")
    private String approvalSchemaId;

    /**
     * 关联的检查单模板ID
     */
    @TableField("check_template_id")
    private String checkTemplateId;
}
