<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="复制阶段模板" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { copyFormSchema } from './phaseTemplate.data';
  import { copyPhaseTemplate } from '/@/api/project/phaseTemplate';
  import { useMessage } from '/@/hooks/web/useMessage';

  defineOptions({ name: 'PhaseTemplateCopyModal' });

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  const sourceRecord = ref<Recordable>({});

  const [registerForm, { resetFields, validate }] = useForm({
    labelWidth: 100,
    baseColProps: { span: 24 },
    schemas: copyFormSchema,
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    sourceRecord.value = data.record;
  });

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      await copyPhaseTemplate(sourceRecord.value.id, values.newName);
      createMessage.success('复制成功');

      closeModal();
      emit('success');
    } catch (error) {
      createMessage.error('复制失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
