package com.xinghuo.project.biz.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.biz.entity.BizContractEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 合同Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface BizContractMapper extends XHBaseMapper<BizContractEntity> {

    /**
     * 根据客户ID查询合同列表
     *
     * @param custId 客户ID
     * @return 合同列表
     */
    List<BizContractEntity> selectByCustId(@Param("custId") String custId);

    /**
     * 根据负责人ID查询合同列表
     *
     * @param ownId 负责人ID
     * @return 合同列表
     */
    List<BizContractEntity> selectByOwnId(@Param("ownId") String ownId);

    /**
     * 根据合同状态查询合同列表
     *
     * @param contractStatus 合同状态
     * @return 合同列表
     */
    List<BizContractEntity> selectByContractStatus(@Param("contractStatus") String contractStatus);

    /**
     * 根据收款状态查询合同列表
     *
     * @param moneyStatus 收款状态
     * @return 合同列表
     */
    List<BizContractEntity> selectByMoneyStatus(@Param("moneyStatus") String moneyStatus);

    /**
     * 检查合同编号是否存在
     *
     * @param cno 合同编号
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkCnoExists(@Param("cno") String cno, @Param("excludeId") String excludeId);

    /**
     * 获取合同统计数据
     *
     * @param params 查询参数
     * @return 统计数据
     */
    List<Map<String, Object>> getContractStatistics(@Param("params") Map<String, Object> params);
}
