<template>
  <div>
    <BasicTable @register="registerTable" :searchInfo="searchInfo">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate" v-auth="'project:core:project:create'">
          <Icon icon="ant-design:plus-outlined" />
          新增项目
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:info-standard-line',
                tooltip: '查看详情',
                onClick: handleView.bind(null, record),
                auth: 'project:core:project:view',
              },
              {
                icon: 'clarity:note-edit-line',
                tooltip: '编辑',
                onClick: handleEdit.bind(null, record),
                auth: 'project:core:project:edit',
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                tooltip: '删除',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
                auth: 'project:core:project:delete',
              },
            ]"
            :dropDownActions="[
              {
                label: '更新状态',
                onClick: handleUpdateStatus.bind(null, record),
                auth: 'project:core:project:updateStatus',
              },
              {
                label: '更新健康度',
                onClick: handleUpdateHealth.bind(null, record),
                auth: 'project:core:project:updateHealth',
              },
              {
                label: record.status === 'archived' ? '激活项目' : '归档项目',
                onClick: record.status === 'archived' ? handleActivate.bind(null, record) : handleArchive.bind(null, record),
                auth: 'project:core:project:archive',
              },
            ]"
          />
        </template>
        <template v-else-if="column.key === 'status'">
          <Tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </Tag>
        </template>
        <template v-else-if="column.key === 'health'">
          <Tag :color="getHealthColor(record.health)">
            {{ getHealthText(record.health) }}
          </Tag>
        </template>
        <template v-else-if="column.key === 'priority'">
          <Tag :color="getPriorityColor(record.priority)">
            {{ getPriorityText(record.priority) }}
          </Tag>
        </template>
      </template>
    </BasicTable>
    <ProjectBaseModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { reactive } from 'vue';
  import { Tag } from 'ant-design-vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';
  
  import ProjectBaseModal from './ProjectBaseModal.vue';
  import { columns, searchFormSchema } from './projectBase.data';
  import {
    getProjectList,
    deleteProject,
    updateProjectStatus,
    updateProjectHealth,
    archiveProject,
    activateProject,
  } from '/@/api/project/projectBase';

  defineOptions({ name: 'ProjectBase' });

  const { createMessage } = useMessage();
  const { hasPermission } = usePermission();
  const [registerModal, { openModal }] = useModal();
  const searchInfo = reactive<Recordable>({});

  const [registerTable, { reload }] = useTable({
    title: '项目列表',
    api: getProjectList,
    rowKey: 'id',
    columns,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      fixed: undefined,
    },
  });

  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  function handleView(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: false,
      isView: true,
    });
  }

  async function handleDelete(record: Recordable) {
    try {
      await deleteProject(record.id);
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      createMessage.error('删除失败');
    }
  }

  function handleUpdateStatus(record: Recordable) {
    // 这里可以打开状态更新弹窗
    createMessage.info('状态更新功能待实现');
  }

  function handleUpdateHealth(record: Recordable) {
    // 这里可以打开健康度更新弹窗
    createMessage.info('健康度更新功能待实现');
  }

  async function handleArchive(record: Recordable) {
    try {
      await archiveProject(record.id);
      createMessage.success('项目归档成功');
      reload();
    } catch (error) {
      createMessage.error('项目归档失败');
    }
  }

  async function handleActivate(record: Recordable) {
    try {
      await activateProject(record.id);
      createMessage.success('项目激活成功');
      reload();
    } catch (error) {
      createMessage.error('项目激活失败');
    }
  }

  function handleSuccess() {
    reload();
  }

  // 状态相关方法
  function getStatusColor(status: string) {
    const colorMap = {
      planning: 'blue',
      executing: 'green',
      completed: 'cyan',
      closed: 'gray',
      cancelled: 'red',
      archived: 'orange',
    };
    return colorMap[status] || 'default';
  }

  function getStatusText(status: string) {
    const textMap = {
      planning: '规划中',
      executing: '执行中',
      completed: '已完成',
      closed: '已关闭',
      cancelled: '已取消',
      archived: '已归档',
    };
    return textMap[status] || status;
  }

  function getHealthColor(health: string) {
    const colorMap = {
      normal: 'green',
      warning: 'orange',
      risk: 'red',
    };
    return colorMap[health] || 'default';
  }

  function getHealthText(health: string) {
    const textMap = {
      normal: '正常',
      warning: '预警',
      risk: '风险',
    };
    return textMap[health] || health;
  }

  function getPriorityColor(priority: string) {
    const colorMap = {
      high: 'red',
      medium: 'orange',
      low: 'green',
    };
    return colorMap[priority] || 'default';
  }

  function getPriorityText(priority: string) {
    const textMap = {
      high: '高',
      medium: '中',
      low: '低',
    };
    return textMap[priority] || priority;
  }
</script>
