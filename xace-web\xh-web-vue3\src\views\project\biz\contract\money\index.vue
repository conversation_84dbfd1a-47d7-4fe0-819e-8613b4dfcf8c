<template>
  <div>
    <!-- 统计卡片 -->
    <div class="statistics-section mb-4">
      <div class="statistics-header" @click="statisticsExpanded = !statisticsExpanded">
        <h3 class="statistics-title">
          <BarChartOutlined class="title-icon" />
          收款统计概览
        </h3>
        <div class="statistics-toggle">
          <UpOutlined v-if="statisticsExpanded" />
          <DownOutlined v-else />
        </div>
      </div>

      <a-collapse-transition>
        <div v-show="statisticsExpanded" class="statistics-content">
          <!-- 紧凑统计卡片 -->
          <a-row :gutter="[8, 8]">
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <div class="stat-card-mini pending">
                <div class="stat-mini-content">
                  <div class="stat-mini-value">{{ formatCurrency(statistics.pendingAmount) }}</div>
                  <div class="stat-mini-title">待收款总额</div>
                </div>
              </div>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <div class="stat-card-mini success">
                <div class="stat-mini-content">
                  <div class="stat-mini-value">{{ formatCurrency(statistics.yearPaidAmount) }}</div>
                  <div class="stat-mini-title">本年已收款</div>
                </div>
              </div>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <div class="stat-card-mini warning">
                <div class="stat-mini-content">
                  <div class="stat-mini-value">{{ formatCurrency(statistics.yearPendingAmount) }}</div>
                  <div class="stat-mini-title">本年待收款</div>
                </div>
              </div>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <div class="stat-card-mini info">
                <div class="stat-mini-content">
                  <div class="stat-mini-value">{{ completionRate.toFixed(1) }}%</div>
                  <div class="stat-mini-title">收款完成率</div>
                </div>
              </div>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <div class="stat-card-mini invoice">
                <div class="stat-mini-content">
                  <div class="stat-mini-value">{{ formatCurrency(statistics.yearInvoicedAmount) }}</div>
                  <div class="stat-mini-title">本年开票额</div>
                </div>
              </div>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <div class="stat-card-mini invoice-unpaid">
                <div class="stat-mini-content">
                  <div class="stat-mini-value">{{ formatCurrency(statistics.invoicedUnpaidAmount) }}</div>
                  <div class="stat-mini-title">开票未收款</div>
                </div>
              </div>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <div class="stat-card-mini time-month">
                <div class="stat-mini-content">
                  <div class="stat-mini-value">{{ formatCurrency(statistics.oneMonthPendingAmount) }}</div>
                  <div class="stat-mini-title">近月待收款</div>
                </div>
              </div>
            </a-col>
            <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="3">
              <div class="stat-card-mini time-quarter">
                <div class="stat-mini-content">
                  <div class="stat-mini-value">{{ formatCurrency(statistics.threeMonthPendingAmount) }}</div>
                  <div class="stat-mini-title">近季待收款</div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-collapse-transition>
    </div>

    <BasicTable @register="registerTable" :searchInfo="searchInfo">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate">新增收款</a-button>
      </template>

      <!-- 日期查询插槽 -->
      <template #form-external2="{ model }">
        <a-input-group compact>
          <a-form-item-rest>
            <a-select v-model:value="model.dateType" class="form-external2-select" placeholder="请选择" style="width: 120px; text-align: center">
              <a-select-option v-for="item in dataTypeOption" :value="item.id" :key="item.id">{{ item.fullName }}</a-select-option>
            </a-select>
            <RangePicker style="width: calc(100% - 120px)" v-model:value="model.timeRange" />
          </a-form-item-rest>
        </a-input-group>
      </template>

      <!-- 表格单元格自定义渲染 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'cmMoney'">
          {{ formatAmount(record.cmMoney) }}
        </template>
        <template v-if="column.key === 'ybAmount'">
          {{ formatAmount(record.ybAmount) }}
        </template>
        <template v-if="column.key === 'ebAmount'">
          {{ formatAmount(record.ebAmount) }}
        </template>
        <template v-if="column.key === 'otherAmount'">
          {{ formatAmount(record.otherAmount) }}
        </template>
        <template v-if="column.key === 'payStatus'">
          <a-tag :color="getPayStatusColor(record.payStatus)">
            {{ getPayStatusText(record.payStatus) }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:info-standard-line',
                label: '查看',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'clarity:note-edit-line',
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:file-text-outlined',
                label: '开票',
                onClick: handleInvoice.bind(null, record),
                ifShow: String(record.payStatus) !== '1',
              },
              {
                icon: 'ant-design:dollar-circle-outlined',
                label: '收款',
                onClick: handlePayment.bind(null, record),
                ifShow: String(record.payStatus) !== '1',
              },
            ]" />
        </template>
      </template>
    </BasicTable>

    <!-- 新增/编辑抽屉 -->
    <ContractMoneyDrawer @register="registerDrawer" @success="handleSuccess" />

    <!-- 开票登记弹窗 -->
    <InvoiceModal @register="registerInvoiceModal" @success="handleSuccess" />

    <!-- 收款登记弹窗 -->
    <PaymentModal @register="registerPaymentModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, computed, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useDrawer } from '/@/components/Drawer';
  import { getContractMoneyList, deleteContractMoney, getContractMoneyStatistics } from '/@/api/project/contractMoney';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDate } from '/@/utils/dateUtil';
  import { RangePicker } from 'ant-design-vue';
  import {
    ExclamationCircleOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    CalendarOutlined,
    ScheduleOutlined,
    PieChartOutlined,
    DollarCircleOutlined,
    FileTextOutlined,
    FileSyncOutlined,
    FileExclamationOutlined,
    PercentageOutlined,
    BarChartOutlined,
    UpOutlined,
    DownOutlined,
  } from '@ant-design/icons-vue';

  import ContractSelect from '/@/views/project/components/ContractSelect.vue';
  import ContractMoneyDrawer from './ContractMoneyDrawer.vue';
  import InvoiceModal from './InvoiceModal.vue';
  import PaymentModal from './PaymentModal.vue';

  import type { FormSchema } from '/@/components/Table';
  import type { ContractMoneyModel, ContractMoneyStatistics } from '/@/api/project/contractMoney';

  const { createMessage } = useMessage();
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerInvoiceModal, { openModal: openInvoiceModal }] = useModal();
  const [registerPaymentModal, { openModal: openPaymentModal }] = useModal();

  const searchInfo = reactive<Recordable>({});

  // 统计区域展开状态
  const statisticsExpanded = ref(true);

  // 统计数据
  const statistics = ref<ContractMoneyStatistics>({
    pendingAmount: 0,
    yearPaidAmount: 0,
    yearPendingAmount: 0,
    oneMonthPendingAmount: 0,
    threeMonthPendingAmount: 0,
    invoicedUnpaidAmount: 0,
    yearInvoicedAmount: 0,
    yearInvoicedUnpaidAmount: 0,
    totalCount: 0,
    paidCount: 0,
    pendingCount: 0,
    invoicedCount: 0,
  });

  // 计算收款完成率
  const completionRate = computed(() => {
    const total = statistics.value.totalCount;
    const paid = statistics.value.paidCount;
    return total > 0 ? (paid / total) * 100 : 0;
  });

  // 计算开票完成率
  const invoiceRate = computed(() => {
    const total = statistics.value.totalCount;
    const invoiced = statistics.value.invoicedCount;
    return total > 0 ? (invoiced / total) * 100 : 0;
  });

  // 格式化货币
  function formatCurrency(amount: number | undefined | null) {
    if (amount === undefined || amount === null || isNaN(Number(amount))) {
      return '¥0.00';
    }

    const numAmount = Number(amount);

    // 对于大金额，显示万元或亿元
    if (numAmount >= 100000000) {
      // 大于等于1亿，显示亿元
      return `¥${(numAmount / 100000000).toFixed(2)}亿`;
    } else if (numAmount >= 10000) {
      // 大于等于1万，显示万元
      return `¥${(numAmount / 10000).toFixed(2)}万`;
    } else {
      // 小于1万，显示元
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(numAmount);
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '合同',
      dataIndex: 'contractName',
      width: 200,
      customRender: ({ record }) => {
        if (record.contractNo && record.contractName) {
          return `${record.contractNo} - ${record.contractName}`;
        } else if (record.contractName) {
          return record.contractName;
        } else if (record.contractNo) {
          return record.contractNo;
        } else {
          return '-';
        }
      },
    },
    {
      title: '支付条件',
      dataIndex: 'fktj',
      width: 150,
    },
    {
      title: '收款比例',
      dataIndex: 'ratio',
      width: 100,
    },
    {
      title: '收款金额',
      key: 'cmMoney',
      dataIndex: 'cmMoney',
      width: 120,
      sorter: true,
    },
    {
      title: '收款状态',
      key: 'payStatus',
      dataIndex: 'payStatus',
      width: 100,
    },
    {
      title: '应收日期',
      dataIndex: 'yingshouDate',
      width: 120,
      customRender: ({ record }) => {
        return record.yingshouDate ? formatToDate(record.yingshouDate) : '-';
      },
    },
    {
      title: '预收日期',
      dataIndex: 'yushouDate',
      width: 120,
      customRender: ({ record }) => {
        return record.yushouDate ? formatToDate(record.yushouDate) : '-';
      },
    },
    {
      title: '开票日期',
      dataIndex: 'kaipiaoDate',
      width: 120,
      customRender: ({ record }) => {
        return record.kaipiaoDate ? formatToDate(record.kaipiaoDate) : '-';
      },
    },
    {
      title: '收款日期',
      dataIndex: 'shoukuanDate',
      width: 120,
      customRender: ({ record }) => {
        return record.shoukuanDate ? formatToDate(record.shoukuanDate) : '-';
      },
    },
  ];

  // 搜索表单配置
  const searchFormSchema: FormSchema[] = [
    {
      field: 'contractName',
      label: '合同名称',
      component: 'Input',
      colProps: { span: 4 },
    },
    {
      field: 'payStatus',
      label: '收款状态',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '未收款', id: '0' },
          { fullName: '已收款', id: '1' },
        ],
        placeholder: '请选择收款状态',
        allowClear: true,
      },
      colProps: { span: 3 },
    },
    {
      field: 'dateType',
      label: '',
      component: 'Input',
      colProps: { span: 4 },
      componentProps: { placeholder: '请输入' },
      show: false,
    },
    {
      field: 'external2',
      label: '',
      component: 'Input',
      slot: 'external2',
      colProps: { span: 6 },
      componentProps: { placeholder: '' },
    },
  ];

  // 日期类型选项
  const dataTypeOption = [
    { fullName: '应收日期', id: 'yingshou_date' },
    { fullName: '预收日期', id: 'yushou_date' },
    { fullName: '开票日期', id: 'kaipiao_date' },
    { fullName: '收款日期', id: 'shoukuan_date' },
    { fullName: '创建时间', id: 'create_time' },
    { fullName: '更新时间', id: 'update_time' },
  ];

  // 表格配置
  const [registerTable, { reload, getForm }] = useTable({
    title: '收款管理列表',
    api: getContractMoneyList,
    rowKey: 'cmId',
    columns,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      fieldMapToTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
      autoAdvancedLine: 2,
      showAdvancedButton: true,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    showSummary: true,
    summaryFunc: calculateSummary,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
    },
  });

  // 计算合计行数据
  function calculateSummary(dataSource: ContractMoneyModel[]) {
    const summaryData = {
      contractName: '合计',
      fktj: '',
      ratio: '',
      cmMoney: 0,
      payStatus: '',
      yingshouDate: '',
      yushouDate: '',
      kaipiaoDate: '',
      shoukuanDate: '',
      ybAmount: 0,
      ebAmount: 0,
      otherAmount: 0,
    };

    // 计算各项金额的合计
    dataSource.forEach(item => {
      summaryData.cmMoney += Number(item.cmMoney) || 0;
      summaryData.ybAmount += Number(item.ybAmount) || 0;
      summaryData.ebAmount += Number(item.ebAmount) || 0;
      summaryData.otherAmount += Number(item.otherAmount) || 0;
    });

    return [summaryData];
  }

  // 格式化金额
  function formatAmount(amount: number | undefined) {
    if (amount === undefined || amount === null) return '-';
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(amount);
  }

  // 获取收款状态颜色
  function getPayStatusColor(status: string | number) {
    const statusStr = String(status);
    const colorMap = {
      '0': 'red', // 未收款
      '1': 'green', // 已收款
    };
    return colorMap[statusStr] || 'default';
  }

  // 获取收款状态文本
  function getPayStatusText(status: string | number) {
    const statusStr = String(status);
    const textMap = {
      '0': '未收款',
      '1': '已收款',
    };
    return textMap[statusStr] || status;
  }

  // 新增收款
  function handleCreate() {
    openDrawer(true, {
      isUpdate: false,
    });
  }

  // 查看详情
  function handleView(record: ContractMoneyModel) {
    openDrawer(true, {
      record,
      isUpdate: false,
      readonly: true,
    });
  }

  // 编辑收款
  function handleEdit(record: ContractMoneyModel) {
    openDrawer(true, {
      record,
      isUpdate: true,
    });
  }

  // 登记开票
  function handleInvoice(record: ContractMoneyModel) {
    openInvoiceModal(true, {
      record,
    });
  }

  // 登记收款
  function handlePayment(record: ContractMoneyModel) {
    openPaymentModal(true, {
      record,
    });
  }

  // 删除收款
  async function handleDelete(record: ContractMoneyModel) {
    try {
      await deleteContractMoney(record.cmId);
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      createMessage.error('删除失败');
    }
  }

  // 加载统计数据
  async function loadStatistics() {
    try {
      const result: any = await getContractMoneyStatistics();
      console.log('统计数据API返回:', result);

      // 处理API返回的数据格式
      if (result && typeof result === 'object') {
        if (result.data) {
          // 如果API返回的是 { data: {...} } 格式
          statistics.value = result.data;
        } else {
          // 如果API直接返回数据对象
          statistics.value = result;
        }
      }

      console.log('处理后的统计数据:', statistics.value);
    } catch (error) {
      console.error('获取统计数据失败:', error);
      createMessage.error('获取统计数据失败');
    }
  }

  // 操作成功回调
  function handleSuccess() {
    reload();
    // 重新加载统计数据
    loadStatistics();
  }

  // 重置表单并设置默认值
  async function resetForm() {
    // 设置默认的日期类型
    const { setFieldsValue } = registerTable[1];
    await setFieldsValue({
      dateType: dataTypeOption[0].id,
    });
    reload();
  }

  // 组件挂载时加载统计数据
  onMounted(() => {
    loadStatistics();
  });
</script>

<style scoped>
  :deep(.form-external2-select .ant-select-selector) {
    background-color: #fafafa;
  }

  .statistics-section {
    background: white;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    margin-bottom: 16px;
    overflow: hidden;
  }

  .statistics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .statistics-header:hover {
    background: #f0f0f0;
  }

  .statistics-title {
    display: flex;
    align-items: center;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
  }

  .statistics-title .title-icon {
    margin-right: 8px;
    font-size: 18px;
    color: #1890ff;
  }

  .statistics-toggle {
    color: #666;
    font-size: 14px;
    transition: transform 0.3s ease;
  }

  .statistics-content {
    padding: 12px;
    background: #fafafa;
  }

  /* 紧凑统计卡片样式 */
  .stat-card-mini {
    background: white;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e8e8e8;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .stat-card-mini:hover {
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  .stat-mini-content {
    text-align: center;
    width: 100%;
  }

  .stat-mini-value {
    font-size: 14px;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 3px;
    word-break: break-all;
  }

  .stat-mini-title {
    font-size: 11px;
    color: #666;
    font-weight: 500;
    line-height: 1.2;
  }

  /* 紧凑卡片颜色主题 */
  .stat-card-mini.pending .stat-mini-value {
    color: #dc2626;
  }

  .stat-card-mini.success .stat-mini-value {
    color: #16a34a;
  }

  .stat-card-mini.warning .stat-mini-value {
    color: #d97706;
  }

  .stat-card-mini.info .stat-mini-value {
    color: #2563eb;
  }

  .stat-card-mini.invoice .stat-mini-value {
    color: #4f46e5;
  }

  .stat-card-mini.invoice-unpaid .stat-mini-value {
    color: #ca8a04;
  }

  .stat-card-mini.time-month .stat-mini-value {
    color: #8b5cf6;
  }

  .stat-card-mini.time-quarter .stat-mini-value {
    color: #059669;
  }

  .stat-group {
    margin-bottom: 24px;
  }

  .stat-group:last-child {
    margin-bottom: 0;
  }

  .stat-group-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 12px;
  }

  .stat-group-title .title-icon {
    margin-right: 6px;
    font-size: 16px;
    color: #1890ff;
  }

  .stat-card {
    background: white;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    height: 80px;
  }

  .stat-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  .stat-card .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-right: 12px;
    flex-shrink: 0;
  }

  .stat-card .stat-content {
    flex: 1;
    min-width: 0;
  }

  .stat-card .stat-value {
    font-size: 16px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 2px;
    word-break: break-all;
  }

  .stat-card .stat-title {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    margin-bottom: 1px;
  }

  .stat-card .stat-desc {
    font-size: 11px;
    color: #9ca3af;
  }

  /* 收款统计样式 */
  .stat-card.pending .stat-icon {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #dc2626;
  }
  .stat-card.pending .stat-value {
    color: #dc2626;
  }

  .stat-card.success .stat-icon {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #16a34a;
  }
  .stat-card.success .stat-value {
    color: #16a34a;
  }

  .stat-card.warning .stat-icon {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #d97706;
  }
  .stat-card.warning .stat-value {
    color: #d97706;
  }

  .stat-card.info .stat-icon {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #2563eb;
  }
  .stat-card.info .stat-value {
    color: #2563eb;
  }

  /* 开票统计样式 */
  .stat-card.invoice-total .stat-icon {
    background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
    color: #4f46e5;
  }
  .stat-card.invoice-total .stat-value {
    color: #4f46e5;
  }

  .stat-card.invoice-unpaid .stat-icon {
    background: linear-gradient(135deg, #fef7cd 0%, #fef08a 100%);
    color: #ca8a04;
  }
  .stat-card.invoice-unpaid .stat-value {
    color: #ca8a04;
  }

  .stat-card.invoice-year-unpaid .stat-icon {
    background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
    color: #e53e3e;
  }
  .stat-card.invoice-year-unpaid .stat-value {
    color: #e53e3e;
  }

  .stat-card.invoice-rate .stat-icon {
    background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
    color: #319795;
  }
  .stat-card.invoice-rate .stat-value {
    color: #319795;
  }

  /* 时间维度统计样式 */
  .stat-card.time-month .stat-icon {
    background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
    color: #8b5cf6;
  }
  .stat-card.time-month .stat-value {
    color: #8b5cf6;
  }

  .stat-card.time-quarter .stat-icon {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    color: #059669;
  }
  .stat-card.time-quarter .stat-value {
    color: #059669;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .statistics-content {
      padding: 8px;
    }

    .stat-card-mini {
      height: 55px;
      padding: 8px;
    }

    .stat-mini-value {
      font-size: 12px;
    }

    .stat-mini-title {
      font-size: 10px;
    }
  }
</style>
