import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

// 标签管理路由配置
export default {
  path: '/project/tag',
  name: 'ProjectTag',
  component: LAYOUT,
  redirect: '/project/tag/index',
  meta: {
    orderNo: 10,
    icon: 'icon-ym icon-ym-tag',
    title: t('标签管理'),
    defaultTitle: '标签管理',
  },
  children: [
    {
      path: 'index',
      name: 'ProjectTagIndex',
      component: () => import('/@/views/project/tag/index.vue'),
      meta: {
        title: t('标签管理'),
        defaultTitle: '标签管理',
        icon: 'icon-ym icon-ym-tag',
      },
    },
  ],
};
