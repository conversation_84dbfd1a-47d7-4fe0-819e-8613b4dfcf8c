package com.xinghuo.project.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.biz.dao.PaymentContractMapper;
import com.xinghuo.project.biz.entity.PaymentContractEntity;
import com.xinghuo.project.biz.model.PaymentContractPagination;
import com.xinghuo.project.biz.service.PaymentContractService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 付款合同服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class PaymentContractServiceImpl extends BaseServiceImpl<PaymentContractMapper, PaymentContractEntity> implements PaymentContractService {

    @Resource
    private PaymentContractMapper paymentContractMapper;

    @Override
    public List<PaymentContractEntity> getList(PaymentContractPagination pagination) {
        QueryWrapper<PaymentContractEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<PaymentContractEntity> lambda = queryWrapper.lambda();

        // 根据合同编号模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getCno())) {
            lambda.like(PaymentContractEntity::getCno, pagination.getCno());
        }

        // 根据合同名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(PaymentContractEntity::getName, pagination.getName());
        }

        // 根据收款合同ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getContractId())) {
            lambda.eq(PaymentContractEntity::getCid,pagination.getContractId());
        }

        // 根据供应商ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getSupplierId())) {
            lambda.eq(PaymentContractEntity::getSuppilerId, pagination.getSupplierId());
        }

        // 根据合同状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getStatus())) {
            lambda.eq(PaymentContractEntity::getStatus, pagination.getStatus());
        }

        // 签订日期范围查询
        if (pagination.getSignDateStart() != null) {
            lambda.ge(PaymentContractEntity::getSignDate, pagination.getSignDateStart());
        }
        if (pagination.getSignDateEnd() != null) {
            lambda.le(PaymentContractEntity::getSignDate, pagination.getSignDateEnd());
        }

        // 创建时间范围查询
//        if (pagination.getCreateTimeStart() != null) {
//            lambda.ge(PaymentContractEntity::getCreatedAt, pagination.getCreateTimeStart());
//        }
//        if (pagination.getCreateTimeEnd() != null) {
//            lambda.le(PaymentContractEntity::getCreatedAt, pagination.getCreateTimeEnd());
//        }

        // 根据关键字搜索合同名称或编号
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(PaymentContractEntity::getName, keyword)
                    .or()
                    .like(PaymentContractEntity::getCno, keyword)
            );
        }

        // 排序
//        lambda.orderByDesc(PaymentContractEntity::getCreatedAt);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public List<PaymentContractEntity> getListByContractId(String contractId) {
        return paymentContractMapper.selectByContractId(contractId);
    }

    @Override
    public List<PaymentContractEntity> getListBySupplierId(String supplierId) {
        return paymentContractMapper.selectBySupplierId(supplierId);
    }

    @Override
    public PaymentContractEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(PaymentContractEntity entity) {
        String id = RandomUtil.snowId();
        entity.setPcId(id);
        this.save(entity);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, PaymentContractEntity entity) {
        entity.setPcId(id);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String id, String status) {
        paymentContractMapper.updateStatus(id, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sign(String id, String cNo, Date signDate) {
        paymentContractMapper.signContract(id, cNo, signDate);
    }

    @Override
    public boolean isExistByCNo(String cNo, String excludeId) {
        int count = paymentContractMapper.checkCNoExists(cNo, excludeId);
        return count > 0;
    }

    @Override
    public List<PaymentContractEntity> getSelectList(String keyword) {
        return paymentContractMapper.selectForSelect(keyword);
    }
}
