package com.xinghuo.project.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.entity.ProjTagRelEntity;

import java.util.List;

/**
 * 项目-标签关联表服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ProjTagRelService extends BaseService<ProjTagRelEntity> {

    /**
     * 根据项目ID查询关联的标签ID列表
     *
     * @param projectId 项目ID
     * @return 标签ID列表
     */
    List<String> getTagIdsByProjectId(String projectId);

    /**
     * 根据标签ID查询关联的项目ID列表
     *
     * @param tagId 标签ID
     * @return 项目ID列表
     */
    List<String> getProjectIdsByTagId(String tagId);

    /**
     * 检查项目和标签的关联关系是否存在
     *
     * @param projectId 项目ID
     * @param tagId 标签ID
     * @return 是否存在关联关系
     */
    boolean isRelationExists(String projectId, String tagId);

    /**
     * 创建项目-标签关联关系
     *
     * @param projectId 项目ID
     * @param tagId 标签ID
     * @return 创建结果
     */
    boolean createRelation(String projectId, String tagId);

    /**
     * 删除项目-标签关联关系
     *
     * @param projectId 项目ID
     * @param tagId 标签ID
     * @return 删除结果
     */
    boolean deleteRelation(String projectId, String tagId);

    /**
     * 批量设置项目的标签关联
     *
     * @param projectId 项目ID
     * @param tagIds 标签ID列表
     * @return 设置结果
     */
    boolean setProjectTagRelations(String projectId, List<String> tagIds);

    /**
     * 批量设置标签的项目关联
     *
     * @param tagId 标签ID
     * @param projectIds 项目ID列表
     * @return 设置结果
     */
    boolean setTagProjectRelations(String tagId, List<String> projectIds);

    /**
     * 删除项目的所有标签关联
     *
     * @param projectId 项目ID
     * @return 删除结果
     */
    boolean deleteByProjectId(String projectId);

    /**
     * 删除标签的所有项目关联
     *
     * @param tagId 标签ID
     * @return 删除结果
     */
    boolean deleteByTagId(String tagId);

    /**
     * 根据项目ID列表批量查询标签关联
     *
     * @param projectIds 项目ID列表
     * @return 关联记录列表
     */
    List<ProjTagRelEntity> getRelationsByProjectIds(List<String> projectIds);

    /**
     * 根据标签ID列表批量查询项目关联
     *
     * @param tagIds 标签ID列表
     * @return 关联记录列表
     */
    List<ProjTagRelEntity> getRelationsByTagIds(List<String> tagIds);
}
