<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h2>项目管理仪表板</h2>
      <a-space>
        <a-range-picker v-model:value="dateRange" @change="handleDateChange" />
        <a-button @click="handleRefresh">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </a-space>
    </div>

    <!-- 统计卡片 -->
    <div class="dashboard-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="项目总数"
              :value="statistics.totalProjects"
              :value-style="{ color: '#3f8600' }"
            >
              <template #prefix>
                <ProjectOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="进行中项目"
              :value="statistics.activeProjects"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <PlayCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="商机总数"
              :value="statistics.totalOpportunities"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <BulbOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="合同总额"
              :value="statistics.totalContractAmount"
              :precision="2"
              suffix="万元"
              :value-style="{ color: '#cf1322' }"
            >
              <template #prefix>
                <DollarOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表区域 -->
    <div class="dashboard-charts">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="项目健康度分布" :loading="loading">
            <div ref="healthChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="销售漏斗" :loading="loading">
            <div ref="funnelChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <div class="dashboard-charts" style="margin-top: 16px;">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="项目状态趋势" :loading="loading">
            <div ref="trendChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="合同金额分析" :loading="loading">
            <div ref="contractChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 最近活动 -->
    <div class="dashboard-activities" style="margin-top: 16px;">
      <a-card title="最近活动" :loading="loading">
        <a-list
          :data-source="recentActivities"
          :pagination="false"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta
                :title="item.title"
                :description="item.description"
              >
                <template #avatar>
                  <a-avatar :style="{ backgroundColor: item.color }">
                    {{ item.type }}
                  </a-avatar>
                </template>
              </a-list-item-meta>
              <div>{{ item.time }}</div>
            </a-list-item>
          </template>
        </a-list>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import {
  ReloadOutlined,
  ProjectOutlined,
  PlayCircleOutlined,
  BulbOutlined,
  DollarOutlined,
} from '@ant-design/icons-vue';
import * as echarts from 'echarts';
import { getDashboardData } from '/@/api/project/dashboard';
import dayjs from 'dayjs';

// 响应式数据
const loading = ref(false);
const dateRange = ref([dayjs().subtract(30, 'day'), dayjs()]);

// 图表引用
const healthChartRef = ref();
const funnelChartRef = ref();
const trendChartRef = ref();
const contractChartRef = ref();

// 统计数据
const statistics = reactive({
  totalProjects: 0,
  activeProjects: 0,
  totalOpportunities: 0,
  totalContractAmount: 0,
});

// 最近活动
const recentActivities = ref([
  {
    title: '新增商机：某大型企业数字化转型项目',
    description: '预计金额：500万元',
    time: '2小时前',
    type: '商',
    color: '#1890ff',
  },
  {
    title: '项目状态更新：智慧城市建设项目进入实施阶段',
    description: '项目进度：60%',
    time: '4小时前',
    type: '项',
    color: '#52c41a',
  },
  {
    title: '合同签署：云平台建设合同正式签署',
    description: '合同金额：300万元',
    time: '1天前',
    type: '合',
    color: '#722ed1',
  },
]);

// 方法
const loadDashboardData = async () => {
  loading.value = true;
  try {
    const params = {
      startDate: dateRange.value[0]?.format('YYYY-MM-DD'),
      endDate: dateRange.value[1]?.format('YYYY-MM-DD'),
    };
    
    const result = await getDashboardData(params);
    
    // 更新统计数据
    statistics.totalProjects = result.data.projectStatistics?.totalProjects || 0;
    statistics.activeProjects = result.data.projectStatistics?.activeProjects || 0;
    statistics.totalOpportunities = result.data.salesFunnel?.totalOpportunities || 0;
    statistics.totalContractAmount = result.data.contractStatistics?.totalAmount || 0;
    
    // 渲染图表
    await nextTick();
    renderCharts(result.data);
  } catch (error) {
    message.error('加载仪表板数据失败');
  } finally {
    loading.value = false;
  }
};

const renderCharts = (data) => {
  // 项目健康度分布图
  if (healthChartRef.value) {
    const healthChart = echarts.init(healthChartRef.value);
    const healthOption = {
      tooltip: {
        trigger: 'item',
      },
      legend: {
        orient: 'vertical',
        left: 'left',
      },
      series: [
        {
          name: '项目健康度',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 35, name: '健康' },
            { value: 25, name: '风险' },
            { value: 15, name: '延期' },
            { value: 10, name: '暂停' },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    };
    healthChart.setOption(healthOption);
  }

  // 销售漏斗图
  if (funnelChartRef.value) {
    const funnelChart = echarts.init(funnelChartRef.value);
    const funnelOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c}%',
      },
      series: [
        {
          name: '销售漏斗',
          type: 'funnel',
          left: '10%',
          top: 60,
          bottom: 60,
          width: '80%',
          min: 0,
          max: 100,
          minSize: '0%',
          maxSize: '100%',
          sort: 'descending',
          gap: 2,
          label: {
            show: true,
            position: 'inside',
          },
          data: [
            { value: 100, name: '潜在客户' },
            { value: 80, name: '意向客户' },
            { value: 60, name: '商机跟进' },
            { value: 40, name: '方案提交' },
            { value: 20, name: '合同签署' },
          ],
        },
      ],
    };
    funnelChart.setOption(funnelOption);
  }
};

const handleDateChange = () => {
  loadDashboardData();
};

const handleRefresh = () => {
  loadDashboardData();
};

// 生命周期
onMounted(() => {
  loadDashboardData();
});
</script>

<style scoped>
.dashboard-container {
  padding: 16px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.dashboard-cards {
  margin-bottom: 16px;
}

.dashboard-charts {
  margin-bottom: 16px;
}

.dashboard-activities {
  margin-bottom: 16px;
}
</style>
