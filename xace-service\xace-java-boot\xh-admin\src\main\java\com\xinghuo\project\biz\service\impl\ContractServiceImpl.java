package com.xinghuo.project.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.biz.dao.ContractMapper;
import com.xinghuo.project.biz.entity.ContractEntity;
import com.xinghuo.project.biz.model.ContractPagination;
import com.xinghuo.project.biz.service.ContractService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

/**
 * 合同服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class ContractServiceImpl extends BaseServiceImpl<ContractMapper, ContractEntity> implements ContractService {

    @Resource
    private ContractMapper contractMapper;

    @Override
    public List<ContractEntity> getList(ContractPagination pagination) {
        QueryWrapper<ContractEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ContractEntity> lambda = queryWrapper.lambda();

        // 根据合同编号模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getCno())) {
            lambda.like(ContractEntity::getCno, pagination.getCno());
        }

        // 根据合同名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(ContractEntity::getName, pagination.getName());
        }

        // 根据客户单位ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getCustId())) {
            lambda.eq(ContractEntity::getCustId, pagination.getCustId());
        }

        // 根据最终用户ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getFinalUserId())) {
            lambda.eq(ContractEntity::getFinalUserId, pagination.getFinalUserId());
        }

        // 根据负责人ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getOwnId())) {
            lambda.eq(ContractEntity::getOwnId, pagination.getOwnId());
        }

        // 根据合同状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getContractStatus())) {
            lambda.eq(ContractEntity::getContractStatus, pagination.getContractStatus());
        }

        // 根据收款状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getMoneyStatus())) {
            lambda.eq(ContractEntity::getMoneyStatus, pagination.getMoneyStatus());
        }

        // 根据所属部门ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getDeptId())) {
            lambda.eq(ContractEntity::getDeptId, pagination.getDeptId());
        }

        // 根据合同年度精确查询
        if (pagination.getSignYear() != null) {
            lambda.eq(ContractEntity::getSignYear, pagination.getSignYear());
        }

        // 根据签订日期范围查询
        if (pagination.getSignDateStart() != null) {
            lambda.ge(ContractEntity::getSignDate, pagination.getSignDateStart());
        }
        if (pagination.getSignDateEnd() != null) {
            lambda.le(ContractEntity::getSignDate, pagination.getSignDateEnd());
        }

        // 根据合同开始日期范围查询
        if (pagination.getCstartDateStart() != null) {
            lambda.ge(ContractEntity::getCstartDate, pagination.getCstartDateStart());
        }
        if (pagination.getCstartDateEnd() != null) {
            lambda.le(ContractEntity::getCstartDate, pagination.getCstartDateEnd());
        }

        // 根据合同结束日期范围查询
        if (pagination.getCendDateStart() != null) {
            lambda.ge(ContractEntity::getCendDate, pagination.getCendDateStart());
        }
        if (pagination.getCendDateEnd() != null) {
            lambda.le(ContractEntity::getCendDate, pagination.getCendDateEnd());
        }

        // 根据是否续签精确查询
        if (pagination.getIsContinue() != null) {
            lambda.eq(ContractEntity::getIsContinue, pagination.getIsContinue());
        }

        // 根据是否归档精确查询
        if (pagination.getIsArchive() != null) {
            lambda.eq(ContractEntity::getIsArchive, pagination.getIsArchive());
        }

        // 根据类型状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getTypeStatus())) {
            lambda.eq(ContractEntity::getTypeStatus, pagination.getTypeStatus());
        }

        // 根据外采状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getExternalStatus())) {
            lambda.eq(ContractEntity::getExternalStatus, pagination.getExternalStatus());
        }

        // 根据分配状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getAssignStatus())) {
            lambda.eq(ContractEntity::getAssignStatus, pagination.getAssignStatus());
        }

        // 根据工时填写状态精确查询
        if (pagination.getWorkStatus() != null) {
            lambda.eq(ContractEntity::getWorkStatus, pagination.getWorkStatus());
        }

        // 根据关键字搜索合同名称或编号
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(ContractEntity::getName, keyword)
                    .or()
                    .like(ContractEntity::getCno, keyword)
            );
        }

        // 排除已删除的记录
        lambda.eq(ContractEntity::getDeleteMark, 0);

        // 排序
        lambda.orderByDesc(ContractEntity::getCreateTime);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public List<ContractEntity> getListByCustId(String custId) {
        LambdaQueryWrapper<ContractEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractEntity::getCustId, custId);
        queryWrapper.eq(ContractEntity::getDeleteMark, 0);
        queryWrapper.orderByDesc(ContractEntity::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public List<ContractEntity> getListByOwnId(String ownId) {
        LambdaQueryWrapper<ContractEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractEntity::getOwnId, ownId);
        queryWrapper.eq(ContractEntity::getDeleteMark, 0);
        queryWrapper.orderByDesc(ContractEntity::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public ContractEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(ContractEntity entity) {
        // 生成合同ID
        String id = RandomUtil.snowId();
        entity.setId(id);

        // 设置初始状态
        if (StrXhUtil.isEmpty(entity.getContractStatus())) {
            entity.setContractStatus("执行中");
        }
        if (StrXhUtil.isEmpty(entity.getMoneyStatus())) {
            entity.setMoneyStatus("未收款");
        }

        // 设置删除标记
        entity.setDeleteMark(0);

        // 初始化金额字段
        if (entity.getYsAmount() == null) {
            entity.setYsAmount(BigDecimal.ZERO);
        }
        if (entity.getYearYsAmount() == null) {
            entity.setYearYsAmount(BigDecimal.ZERO);
        }
        if (entity.getAutoManhours() == null) {
            entity.setAutoManhours(BigDecimal.ZERO);
        }

        // 保存合同
        this.save(entity);

        // 计算毛利率
        calculateProfitRatio(id);

        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, ContractEntity entity) {
        ContractEntity oldEntity = this.getById(id);
        if (oldEntity == null) {
            throw new RuntimeException("合同不存在");
        }

        // 设置ID
        entity.setId(id);

        // 更新合同
        this.updateById(entity);

        // 重新计算毛利率
        calculateProfitRatio(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        ContractEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("合同不存在");
        }

        // 逻辑删除
        entity.setDeleteMark(1);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateContractStatus(String id, String contractStatus) {
        ContractEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("合同不存在");
        }

        entity.setContractStatus(contractStatus);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMoneyStatus(String id, String moneyStatus) {
        ContractEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("合同不存在");
        }

        entity.setMoneyStatus(moneyStatus);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWorkStatus(String id, Integer workStatus) {
        ContractEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("合同不存在");
        }

        entity.setWorkStatus(workStatus);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String renewContract(String id, ContractEntity newContract) {
        ContractEntity oldContract = this.getById(id);
        if (oldContract == null) {
            throw new RuntimeException("原合同不存在");
        }

        // 创建新合同
        String newId = create(newContract);

        // 更新原合同续签状态
        oldContract.setIsContinue(1);
        oldContract.setNewCid(newId);
        this.updateById(oldContract);

        return newId;
    }

    @Override
    public boolean isExistByCno(String cno, String excludeId) {
        int count = contractMapper.checkCnoExists(cno, excludeId);
        return count > 0;
    }

    @Override
    public List<Map<String, Object>> getContractStatistics(Map<String, Object> params) {
        return contractMapper.getContractStatistics(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void calculateProfitRatio(String id) {
        ContractEntity entity = this.getById(id);
        if (entity == null) {
            return;
        }

        // 计算预估毛利率
        if (entity.getAmount() != null && entity.getAmount().compareTo(BigDecimal.ZERO) > 0) {
            if (entity.getEvaExternalAmount() != null) {
                BigDecimal estProfit = entity.getAmount().subtract(entity.getEvaExternalAmount());
                entity.setEstProbit(estProfit);
                
                BigDecimal estRatio = estProfit.divide(entity.getAmount(), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                entity.setEstProbitRatio(estRatio);
            }

            // 计算实际毛利率
            if (entity.getActExternalAmount() != null) {
                BigDecimal actProfit = entity.getAmount().subtract(entity.getActExternalAmount());
                entity.setActProbit(actProfit);
                
                BigDecimal actRatio = actProfit.divide(entity.getAmount(), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                entity.setActProbitRatio(actRatio);
            }
        }

        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateReceivedAmount(String id) {
        // TODO: 根据收款计划计算已收金额
        // 这里需要查询收款计划表来计算实际已收金额
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAutoManhours(String id) {
        // TODO: 根据工时记录计算累计工时
        // 这里需要查询工时记录表来计算累计工时
    }
}
