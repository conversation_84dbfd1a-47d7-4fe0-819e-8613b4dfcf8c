package com.xinghuo.project.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目基础信息实体类
 * 对应数据库表：zz_proj_base
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_base")
public class ProjectBaseEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 项目编码，可自动生成
     */
    @TableField("code")
    private String code;

    /**
     * 项目名称
     */
    @TableField("name")
    private String name;

    /**
     * 项目类型ID (关联字典表, 核心分类字段)
     */
    @TableField("type_id")
    private String typeId;

    /**
     * 项目经理ID (关联用户表)
     */
    @TableField("manager_id")
    private String managerId;

    /**
     * 所属项目群ID (关联 zz_proj_program.f_id)
     */
    @TableField("program_id")
    private String programId;

    /**
     * 项目状态 (字典: 未启动、进行中、已完成、已关闭、已取消)
     */
    @TableField("status")
    private String status;

    /**
     * 项目健康度 (字典: 正常、预警、风险)
     */
    @TableField("health")
    private String health;

    /**
     * 计划开始日期
     */
    @TableField("planned_start_date")
    private Date plannedStartDate;

    /**
     * 计划结束日期
     */
    @TableField("planned_end_date")
    private Date plannedEndDate;

    /**
     * 实际开始日期
     */
    @TableField("actual_start_date")
    private Date actualStartDate;

    /**
     * 实际结束日期
     */
    @TableField("actual_end_date")
    private Date actualEndDate;

    /**
     * 优先级 (高、中、低)
     */
    @TableField("priority")
    private String priority;

    /**
     * 项目描述
     */
    @TableField("description")
    private String description;

    /**
     * 项目所属部门ID
     */
    @TableField("department_id")
    private String departmentId;

    /**
     * 项目预算金额
     */
    @TableField("budget_amount")
    private BigDecimal budgetAmount;

    /**
     * 项目实际花费金额
     */
    @TableField("actual_cost")
    private BigDecimal actualCost;

    /**
     * 客户ID (关联 zz_proj_customer.f_id)
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 相关合同ID (关联 zz_proj_contract.f_id)
     */
    @TableField("contract_id")
    private String contractId;
}
