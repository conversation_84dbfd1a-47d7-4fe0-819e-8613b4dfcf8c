<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import { createCustomerLinkman, updateCustomerLinkman } from '/@/api/project/customerLinkman';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'reload']);
  const isUpdate = ref(false);
  const linkmanId = ref('');
  const customerId = ref('');

  // 表单配置
  const formSchemas: FormSchema[] = [
    {
      field: 'linkman',
      label: '联系人',
      component: 'Input',
      componentProps: { placeholder: '请输入联系人', maxlength: 50 },
      rules: [
        { required: true, trigger: 'blur', message: '请输入联系人' },
        { max: 50, message: '联系人最多为50个字符', trigger: 'blur' },
      ],
    },
    {
      field: 'telephone',
      label: '联系电话',
      component: 'Input',
      componentProps: { placeholder: '请输入联系电话', maxlength: 100 },
      rules: [{ max: 100, message: '联系电话最多为100个字符', trigger: 'blur' }],
    },
    {
      field: 'status',
      label: '状态',
      component: 'RadioGroup',
      defaultValue: 1,
      componentProps: {
        options: [
          { label: '有效', value: 1 },
          { label: '无效', value: 0 },
        ],
      },
    },
    {
      field: 'topic',
      label: '主题',
      component: 'Input',
      componentProps: { placeholder: '请输入主题', maxlength: 50 },
      rules: [{ max: 50, message: '主题最多为50个字符', trigger: 'blur' }],
    },
    {
      field: 'content',
      label: '内容',
      component: 'InputTextArea',
      componentProps: { placeholder: '请输入内容', maxlength: 1000, showCount: true, rows: 4 },
      rules: [{ max: 1000, message: '内容最多为1000个字符', trigger: 'blur' }],
    },
    {
      field: 'note',
      label: '备注',
      component: 'InputTextArea',
      componentProps: { placeholder: '请输入备注', maxlength: 1000, showCount: true, rows: 4 },
      rules: [{ max: 1000, message: '备注最多为1000个字符', trigger: 'blur' }],
    },
  ];

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 80,
    schemas: formSchemas,
    showActionButtonGroup: false,
  });

  // 注册模态框
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });

    isUpdate.value = !!data?.isUpdate;
    customerId.value = data.customerId;
    
    if (unref(isUpdate)) {
      linkmanId.value = data.record.id;
      setFieldsValue({
        ...data.record,
      });
    } else {
      // 新增时设置客户ID
      setFieldsValue({
        cuId: customerId.value,
      });
    }
  });

  // 获取标题
  const getTitle = computed(() => {
    return unref(isUpdate) ? '编辑联系人' : '新增联系人';
  });

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      // 确保设置了客户ID
      if (!values.cuId) {
        values.cuId = customerId.value;
      }

      if (unref(isUpdate)) {
        await updateCustomerLinkman(linkmanId.value, values);
        createMessage.success('更新成功');
      } else {
        await createCustomerLinkman(values);
        createMessage.success('新增成功');
      }

      closeModal();
      emit('reload');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
