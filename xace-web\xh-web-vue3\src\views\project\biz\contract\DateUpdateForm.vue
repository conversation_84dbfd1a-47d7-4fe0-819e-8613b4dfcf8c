<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="更新合同日期" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import { updateContractDate } from '/@/api/project/contract';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'reload']);
  const contractId = ref('');
  const dateType = ref('');

  // 日期类型选项
  const dateTypeOptions = [
    { label: '合同签订日期', value: 'signDate' },
    { label: '中标日期', value: 'bidDate' },
    { label: '开工日期', value: 'commencementDate' },
    { label: '初验日期', value: 'initialCheckDate' },
    { label: '终验日期', value: 'finalCheckDate' },
    { label: '审计日期', value: 'auditDate' },
    { label: '合同开始日期', value: 'cstartDate' },
    { label: '合同结束日期', value: 'cendDate' },
    { label: '维保开始日期', value: 'mstartDate' },
    { label: '维保结束日期', value: 'mendDate' },
  ];

  // 表单配置
  const formSchemas: FormSchema[] = [
    {
      field: 'dateType',
      label: '日期类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择日期类型',
        options: dateTypeOptions,
      },
      rules: [{ required: true, trigger: 'change', message: '请选择日期类型' }],
    },
    {
      field: 'newDate',
      label: '新日期',
      component: 'DatePicker',
      componentProps: { placeholder: '请选择新日期', style: 'width: 100%' },
      rules: [{ required: true, trigger: 'change', message: '请选择新日期' }],
    },
    {
      field: 'note',
      label: '变更原因',
      component: 'InputTextArea',
      componentProps: { placeholder: '请输入变更原因', maxlength: 500, showCount: true, rows: 4 },
      rules: [{ max: 500, message: '变更原因最多为500个字符', trigger: 'blur' }],
    },
  ];

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchemas,
    showActionButtonGroup: false,
  });

  // 注册模态框
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });

    contractId.value = data.contractId;
    
    // 如果指定了日期类型，则预设
    if (data.dateType) {
      dateType.value = data.dateType;
      setFieldsValue({
        dateType: data.dateType,
      });
    }
  });

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      await updateContractDate(contractId.value, values);
      createMessage.success('更新成功');

      closeModal();
      emit('reload');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
