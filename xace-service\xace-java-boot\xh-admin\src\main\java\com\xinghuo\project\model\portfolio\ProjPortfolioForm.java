package com.xinghuo.project.model.portfolio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 项目组合表单对象
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@Schema(description = "项目组合表单对象")
public class ProjPortfolioForm {

    /**
     * 主键ID（更新时使用）
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 组合编码
     */
    @Schema(description = "组合编码")
    @Size(max = 50, message = "组合编码长度不能超过50个字符")
    private String code;

    /**
     * 组合名称
     */
    @Schema(description = "组合名称")
    @NotBlank(message = "组合名称不能为空")
    @Size(max = 255, message = "组合名称长度不能超过255个字符")
    private String name;

    /**
     * 组合类型ID
     */
    @Schema(description = "组合类型ID")
    @NotBlank(message = "组合类型不能为空")
    @Size(max = 50, message = "组合类型ID长度不能超过50个字符")
    private String typeId;

    /**
     * 组合负责人ID
     */
    @Schema(description = "组合负责人ID")
    @NotBlank(message = "组合负责人不能为空")
    @Size(max = 50, message = "组合负责人ID长度不能超过50个字符")
    private String ownerId;

    /**
     * 组合描述
     */
    @Schema(description = "组合描述")
    private String description;

    /**
     * 状态
     */
    @Schema(description = "状态")
    @Size(max = 50, message = "状态长度不能超过50个字符")
    private String status;
}
