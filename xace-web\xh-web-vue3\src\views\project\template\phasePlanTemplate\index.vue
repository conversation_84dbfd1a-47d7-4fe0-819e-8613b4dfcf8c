<template>
  <div>
    <BasicTable @register="registerTable" :searchInfo="searchInfo">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate" v-auth="'project:template:phasePlanTemplate:create'">
          <Icon icon="ant-design:plus-outlined" />
          新建模板
        </a-button>
        <a-button 
          type="primary" 
          ghost 
          @click="handleBatchPublish" 
          :disabled="!hasSelected"
          v-auth="'project:template:phasePlanTemplate:publish'"
        >
          <Icon icon="ant-design:check-outlined" />
          批量发布
        </a-button>
        <a-button 
          type="primary" 
          ghost 
          @click="handleBatchArchive" 
          :disabled="!hasSelected"
          v-auth="'project:template:phasePlanTemplate:archive'"
        >
          <Icon icon="ant-design:inbox-outlined" />
          批量归档
        </a-button>
        <a-button 
          color="error" 
          @click="handleBatchDelete" 
          :disabled="!hasSelected"
          v-auth="'project:template:phasePlanTemplate:delete'"
        >
          <Icon icon="ant-design:delete-outlined" />
          批量删除
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:info-standard-line',
                tooltip: '查看详情',
                onClick: handleView.bind(null, record),
                auth: 'project:template:phasePlanTemplate:view',
              },
              {
                icon: 'clarity:note-edit-line',
                tooltip: '编辑',
                onClick: handleEdit.bind(null, record),
                auth: 'project:template:phasePlanTemplate:edit',
              },
              {
                icon: 'ant-design:copy-outlined',
                tooltip: '复制',
                onClick: handleCopy.bind(null, record),
                auth: 'project:template:phasePlanTemplate:copy',
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                tooltip: '删除',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
                auth: 'project:template:phasePlanTemplate:delete',
              },
            ]"
            :dropDownActions="[
              {
                label: '发布',
                onClick: handlePublish.bind(null, record),
                ifShow: record.knStatusId === 'draft',
                auth: 'project:template:phasePlanTemplate:publish',
              },
              {
                label: '归档',
                onClick: handleArchive.bind(null, record),
                ifShow: record.knStatusId === 'published',
                auth: 'project:template:phasePlanTemplate:archive',
              },
              {
                label: '应用到项目',
                onClick: handleApplyToProject.bind(null, record),
                ifShow: record.knStatusId === 'published',
                auth: 'project:template:phasePlanTemplate:apply',
              },
              {
                label: '查看使用情况',
                onClick: handleViewUsage.bind(null, record),
                auth: 'project:template:phasePlanTemplate:usage',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <PhasePlanTemplateModal @register="registerModal" @success="handleSuccess" />
    <PhasePlanTemplateCopyModal @register="registerCopyModal" @success="handleSuccess" />
    <ApplyToProjectModal @register="registerApplyModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, computed } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';
  
  import PhasePlanTemplateModal from './PhasePlanTemplateModal.vue';
  import PhasePlanTemplateCopyModal from './PhasePlanTemplateCopyModal.vue';
  import ApplyToProjectModal from './ApplyToProjectModal.vue';
  import { columns, searchFormSchema } from './phasePlanTemplate.data';
  import {
    getPhasePlanTemplateList,
    deletePhasePlanTemplate,
    batchDeletePhasePlanTemplate,
    publishPhasePlanTemplate,
    archivePhasePlanTemplate,
    batchUpdatePhasePlanTemplateKnStatus,
    getPhasePlanTemplateUsageInfo,
  } from '/@/api/project/phasePlanTemplate';

  defineOptions({ name: 'PhasePlanTemplate' });

  const { createMessage, createConfirm } = useMessage();
  const { hasPermission } = usePermission();
  const [registerModal, { openModal }] = useModal();
  const [registerCopyModal, { openModal: openCopyModal }] = useModal();
  const [registerApplyModal, { openModal: openApplyModal }] = useModal();
  const searchInfo = reactive<Recordable>({});

  const [registerTable, { reload, getSelectRows }] = useTable({
    title: '阶段计划模板列表',
    api: getPhasePlanTemplateList,
    rowKey: 'id',
    columns,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    rowSelection: {
      type: 'checkbox',
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      fixed: undefined,
    },
  });

  const hasSelected = computed(() => getSelectRows().length > 0);

  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  function handleView(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: false,
      isView: true,
    });
  }

  function handleCopy(record: Recordable) {
    openCopyModal(true, {
      record,
    });
  }

  async function handleDelete(record: Recordable) {
    try {
      await deletePhasePlanTemplate(record.id);
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      createMessage.error('删除失败');
    }
  }

  async function handleBatchDelete() {
    const selectedRows = getSelectRows();
    if (selectedRows.length === 0) {
      createMessage.warning('请选择要删除的记录');
      return;
    }

    createConfirm({
      iconType: 'warning',
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRows.length} 条记录吗？`,
      onOk: async () => {
        try {
          const ids = selectedRows.map(row => row.id);
          await batchDeletePhasePlanTemplate(ids);
          createMessage.success('批量删除成功');
          reload();
        } catch (error) {
          createMessage.error('批量删除失败');
        }
      },
    });
  }

  async function handlePublish(record: Recordable) {
    try {
      await publishPhasePlanTemplate(record.id);
      createMessage.success('发布成功');
      reload();
    } catch (error) {
      createMessage.error('发布失败');
    }
  }

  async function handleArchive(record: Recordable) {
    try {
      await archivePhasePlanTemplate(record.id);
      createMessage.success('归档成功');
      reload();
    } catch (error) {
      createMessage.error('归档失败');
    }
  }

  async function handleBatchPublish() {
    const selectedRows = getSelectRows();
    if (selectedRows.length === 0) {
      createMessage.warning('请选择要发布的记录');
      return;
    }

    try {
      const ids = selectedRows.map(row => row.id);
      await batchUpdatePhasePlanTemplateKnStatus(ids, 'published');
      createMessage.success('批量发布成功');
      reload();
    } catch (error) {
      createMessage.error('批量发布失败');
    }
  }

  async function handleBatchArchive() {
    const selectedRows = getSelectRows();
    if (selectedRows.length === 0) {
      createMessage.warning('请选择要归档的记录');
      return;
    }

    try {
      const ids = selectedRows.map(row => row.id);
      await batchUpdatePhasePlanTemplateKnStatus(ids, 'archived');
      createMessage.success('批量归档成功');
      reload();
    } catch (error) {
      createMessage.error('批量归档失败');
    }
  }

  function handleApplyToProject(record: Recordable) {
    openApplyModal(true, {
      record,
    });
  }

  async function handleViewUsage(record: Recordable) {
    try {
      const usageInfo = await getPhasePlanTemplateUsageInfo(record.id);
      createMessage.info(`模板使用次数：${usageInfo.usageCount || 0}`);
    } catch (error) {
      createMessage.error('获取使用情况失败');
    }
  }

  function handleSuccess() {
    reload();
  }
</script>
