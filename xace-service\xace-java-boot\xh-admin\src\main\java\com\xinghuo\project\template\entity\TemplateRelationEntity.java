package com.xinghuo.project.template.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通用模板关联表实体类
 * 对应数据库表：zz_proj_tpl_relation
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_tpl_relation")
public class TemplateRelationEntity extends BaseEntityV2.BaseEntityV2<String> {

    /**
     * 源模板ID
     */
    @TableField("source_template_id")
    private String sourceTemplateId;

    /**
     * 源模板类型 (如: PHASE_TEMPLATE, WORKPRODUCT_TEMPLATE, RISK_TEMPLATE, COST_TEMPLATE)
     */
    @TableField("source_template_type")
    private String sourceTemplateType;

    /**
     * 目标模板ID (通常是项目模板ID)
     */
    @TableField("target_template_id")
    private String targetTemplateId;

    /**
     * 目标模板类型 (通常是PROJECT_TEMPLATE)
     */
    @TableField("target_template_type")
    private String targetTemplateType;
}
