import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const projectBase: AppRouteModule = {
  path: '/project/core',
  name: 'ProjectCore',
  component: LAYOUT,
  redirect: '/project/core/projectBase',
  meta: {
    orderNo: 1000,
    icon: 'ant-design:project-outlined',
    title: t('routes.project.core.moduleName'),
    hideChildrenInMenu: false,
  },
  children: [
    {
      path: 'projectBase',
      name: 'ProjectBase',
      component: () => import('./index.vue'),
      meta: {
        title: t('routes.project.core.projectBase'),
        icon: 'ant-design:project-outlined',
        hideMenu: false,
      },
    },
  ],
};

export default projectBase;
