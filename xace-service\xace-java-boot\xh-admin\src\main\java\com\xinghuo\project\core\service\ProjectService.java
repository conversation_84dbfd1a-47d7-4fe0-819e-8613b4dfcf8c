package com.xinghuo.project.core.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.core.entity.ProjectEntity;
import com.xinghuo.project.core.model.ProjectPagination;

import java.util.List;
import java.util.Map;

/**
 * 项目服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ProjectService extends BaseService<ProjectEntity> {

    /**
     * 分页查询项目列表
     *
     * @param pagination 查询条件
     * @return 项目列表
     */
    List<ProjectEntity> getList(ProjectPagination pagination);

    /**
     * 根据项目类型查询项目列表
     *
     * @param projectType 项目类型
     * @return 项目列表
     */
    List<ProjectEntity> getListByProjectType(String projectType);

    /**
     * 根据项目状态查询项目列表
     *
     * @param status 项目状态
     * @return 项目列表
     */
    List<ProjectEntity> getListByStatus(String status);

    /**
     * 根据项目经理ID查询项目列表
     *
     * @param managerId 项目经理ID
     * @return 项目列表
     */
    List<ProjectEntity> getListByManagerId(String managerId);

    /**
     * 根据部门ID查询项目列表
     *
     * @param deptId 部门ID
     * @return 项目列表
     */
    List<ProjectEntity> getListByDeptId(String deptId);

    /**
     * 根据ID查询项目信息
     *
     * @param id 项目ID
     * @return 项目信息
     */
    ProjectEntity getInfo(String id);

    /**
     * 创建项目
     *
     * @param entity 项目信息
     * @return 项目ID
     */
    String create(ProjectEntity entity);

    /**
     * 更新项目
     *
     * @param id 项目ID
     * @param entity 更新信息
     */
    void update(String id, ProjectEntity entity);

    /**
     * 删除项目
     *
     * @param id 项目ID
     */
    void delete(String id);

    /**
     * 更新项目状态
     *
     * @param id 项目ID
     * @param status 项目状态
     */
    void updateStatus(String id, String status);

    /**
     * 更新项目健康度
     *
     * @param id 项目ID
     * @param health 项目健康度
     */
    void updateHealth(String id, String health);

    /**
     * 检查项目编码是否存在
     *
     * @param code 项目编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByCode(String code, String excludeId);

    /**
     * 获取项目统计数据
     *
     * @param params 查询参数
     * @return 统计数据
     */
    List<Map<String, Object>> getProjectStatistics(Map<String, Object> params);

    /**
     * 获取项目健康度统计
     *
     * @param params 查询参数
     * @return 健康度统计
     */
    List<Map<String, Object>> getProjectHealthStatistics(Map<String, Object> params);

    /**
     * 项目归档
     *
     * @param id 项目ID
     */
    void archiveProject(String id);

    /**
     * 项目激活
     *
     * @param id 项目ID
     */
    void activateProject(String id);
}
