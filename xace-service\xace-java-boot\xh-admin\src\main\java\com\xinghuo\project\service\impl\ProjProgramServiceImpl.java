package com.xinghuo.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.dao.ProjProgramMapper;
import com.xinghuo.project.entity.ProjProgramEntity;
import com.xinghuo.project.model.program.ProjProgramPagination;
import com.xinghuo.project.service.ProjProgramService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 项目群服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class ProjProgramServiceImpl extends BaseServiceImpl<ProjProgramMapper, ProjProgramEntity> implements ProjProgramService {

    @Resource
    private ProjProgramMapper projProgramMapper;

    @Resource
    private UserProvider userProvider;

    @Override
    public List<ProjProgramEntity> getList(ProjProgramPagination pagination) {
        QueryWrapper<ProjProgramEntity> queryWrapper = new QueryWrapper<>();

        // 构建查询条件
        if (StrXhUtil.isNotBlank(pagination.getCode())) {
            queryWrapper.lambda().like(ProjProgramEntity::getCode, pagination.getCode().trim());
        }

        if (StrXhUtil.isNotBlank(pagination.getName())) {
            queryWrapper.lambda().like(ProjProgramEntity::getName, pagination.getName().trim());
        }

        if (StrXhUtil.isNotBlank(pagination.getTypeId())) {
            queryWrapper.lambda().eq(ProjProgramEntity::getTypeId, pagination.getTypeId());
        }

        if (StrXhUtil.isNotBlank(pagination.getManagerId())) {
            queryWrapper.lambda().eq(ProjProgramEntity::getManagerId, pagination.getManagerId());
        }

        if (StrXhUtil.isNotBlank(pagination.getStatus())) {
            queryWrapper.lambda().eq(ProjProgramEntity::getStatus, pagination.getStatus());
        }

        if (StrXhUtil.isNotBlank(pagination.getKeyword())) {
            queryWrapper.lambda().and(wrapper -> wrapper
                .like(ProjProgramEntity::getName, pagination.getKeyword().trim())
                .or()
                .like(ProjProgramEntity::getCode, pagination.getKeyword().trim())
            );
        }

        // 排序
        if (StrXhUtil.isBlank(pagination.getSidx())) {
            pagination.setSidx("f_created_at");
            pagination.setSort("desc");
            sort(queryWrapper, pagination, new ProjProgramEntity());
        } else {
            sort(queryWrapper, pagination, new ProjProgramEntity());
        }

        // 执行查询并分页
        Page<ProjProgramEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        IPage<ProjProgramEntity> userIpage = this.page(page, queryWrapper);
        return pagination.setDataList(userIpage.getRecords(), userIpage.getTotal());
    }

    @Override
    public ProjProgramEntity getInfo(String id) {
        if (StrXhUtil.isBlank(id)) {
            log.warn("查询项目群信息ID为空");
            return null;
        }
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveInfo(ProjProgramEntity entity) {
        if (entity == null) {
            log.warn("保存项目群信息为空");
            return false;
        }

        try {
            log.info("开始创建项目群: {}", entity.getName());

            // 检查项目群编码是否重复
            if (StrXhUtil.isNotBlank(entity.getCode()) && isExistByCode(entity.getCode(), null)) {
                log.warn("项目群编码已存在: {}", entity.getCode());
                throw new RuntimeException("项目群编码已存在");
            }

            // 设置默认值
            entity.setId(RandomUtil.snowId());
            if (StrXhUtil.isBlank(entity.getStatus())) {
                entity.setStatus("active");
            }

            boolean result = this.save(entity);
            if (result) {
                log.info("创建项目群成功, ID: {}", entity.getId());
            }
            return result;

        } catch (RuntimeException e) {
            log.warn("创建项目群失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建项目群异常", e);
            throw new RuntimeException("创建项目群失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInfo(ProjProgramEntity entity) {
        if (entity == null || StrXhUtil.isBlank(entity.getId())) {
            log.warn("更新项目群信息ID为空");
            return false;
        }

        try {
            log.info("开始更新项目群: {}", entity.getName());

            // 查询原记录是否存在
            ProjProgramEntity dbEntity = this.getById(entity.getId());
            if (dbEntity == null) {
                log.warn("更新的项目群不存在, ID: {}", entity.getId());
                return false;
            }

            // 检查项目群编码是否重复
            if (StrXhUtil.isNotBlank(entity.getCode()) && isExistByCode(entity.getCode(), entity.getId())) {
                log.warn("项目群编码已存在: {}", entity.getCode());
                throw new RuntimeException("项目群编码已存在");
            }

            boolean result = this.updateById(entity);
            if (result) {
                log.info("更新项目群成功, ID: {}", entity.getId());
            }
            return result;

        } catch (RuntimeException e) {
            log.warn("更新项目群失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("更新项目群异常", e);
            throw new RuntimeException("更新项目群失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(String id) {
        if (StrXhUtil.isBlank(id)) {
            log.warn("删除项目群ID为空");
            return false;
        }

        try {
            log.info("开始删除项目群, ID: {}", id);

            // 检查项目群是否存在
            ProjProgramEntity entity = this.getById(id);
            if (entity == null) {
                log.warn("删除的项目群不存在, ID: {}", id);
                return false;
            }

            boolean result = this.removeById(id);
            if (result) {
                log.info("删除项目群成功, ID: {}", id);
            }
            return result;

        } catch (Exception e) {
            log.error("删除项目群异常, ID: {}", id, e);
            throw new RuntimeException("删除项目群失败");
        }
    }

    @Override
    public List<ProjProgramEntity> getListByManagerId(String managerId) {
        if (StrXhUtil.isBlank(managerId)) {
            return null;
        }
        return projProgramMapper.selectByManagerId(managerId);
    }

    @Override
    public List<ProjProgramEntity> getListByTypeId(String typeId) {
        if (StrXhUtil.isBlank(typeId)) {
            return null;
        }
        return projProgramMapper.selectByTypeId(typeId);
    }

    @Override
    public List<ProjProgramEntity> getListByStatus(String status) {
        if (StrXhUtil.isBlank(status)) {
            return null;
        }
        return projProgramMapper.selectByStatus(status);
    }

    @Override
    public boolean isExistByCode(String code, String excludeId) {
        if (StrXhUtil.isBlank(code)) {
            return false;
        }
        int count = projProgramMapper.checkCodeExists(code, excludeId);
        return count > 0;
    }
}
