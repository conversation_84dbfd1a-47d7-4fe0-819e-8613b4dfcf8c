package com.xinghuo.project.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.entity.ProjPortfolioProjectRelEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组合-项目关联表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface ProjPortfolioProjectRelMapper extends XHBaseMapper<ProjPortfolioProjectRelEntity> {

    /**
     * 根据组合ID查询关联的项目列表
     *
     * @param portfolioId 组合ID
     * @return 关联记录列表
     */
    List<ProjPortfolioProjectRelEntity> selectByPortfolioId(@Param("portfolioId") String portfolioId);

    /**
     * 根据项目ID查询关联的组合列表
     *
     * @param projectId 项目ID
     * @return 关联记录列表
     */
    List<ProjPortfolioProjectRelEntity> selectByProjectId(@Param("projectId") String projectId);

    /**
     * 检查组合和项目的关联关系是否存在
     *
     * @param portfolioId 组合ID
     * @param projectId 项目ID
     * @return 数量
     */
    int checkRelationExists(@Param("portfolioId") String portfolioId, @Param("projectId") String projectId);

    /**
     * 删除组合的所有项目关联
     *
     * @param portfolioId 组合ID
     * @return 删除数量
     */
    int deleteByPortfolioId(@Param("portfolioId") String portfolioId);

    /**
     * 删除项目的所有组合关联
     *
     * @param projectId 项目ID
     * @return 删除数量
     */
    int deleteByProjectId(@Param("projectId") String projectId);

    /**
     * 批量插入组合-项目关联关系
     *
     * @param relations 关联关系列表
     * @return 插入数量
     */
    int batchInsert(@Param("relations") List<ProjPortfolioProjectRelEntity> relations);
}
