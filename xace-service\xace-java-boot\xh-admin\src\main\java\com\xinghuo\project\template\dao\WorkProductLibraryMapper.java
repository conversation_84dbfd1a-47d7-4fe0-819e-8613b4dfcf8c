package com.xinghuo.project.template.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.template.entity.WorkProductLibraryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 标准交付物库Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface WorkProductLibraryMapper extends XHBaseMapper<WorkProductLibraryEntity> {

    /**
     * 检查交付物名称是否存在
     *
     * @param name 交付物名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkNameExists(@Param("name") String name, @Param("excludeId") String excludeId);

    /**
     * 检查交付物编码是否存在
     *
     * @param code 交付物编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkCodeExists(@Param("code") String code, @Param("excludeId") String excludeId);

    /**
     * 根据状态查询交付物列表
     *
     * @param statusId 状态ID
     * @return 交付物列表
     */
    List<WorkProductLibraryEntity> selectByStatus(@Param("statusId") String statusId);

    /**
     * 根据类型查询交付物列表
     *
     * @param typeId 类型ID
     * @param subTypeId 子类型ID
     * @return 交付物列表
     */
    List<WorkProductLibraryEntity> selectByType(@Param("typeId") String typeId, @Param("subTypeId") String subTypeId);

    /**
     * 根据默认角色查询交付物列表
     *
     * @param defaultRoleId 默认角色ID
     * @return 交付物列表
     */
    List<WorkProductLibraryEntity> selectByDefaultRole(@Param("defaultRoleId") String defaultRoleId);

    /**
     * 获取交付物选择列表
     *
     * @param keyword 关键字
     * @param statusId 状态ID
     * @param typeId 类型ID
     * @return 交付物列表
     */
    List<WorkProductLibraryEntity> selectForSelect(@Param("keyword") String keyword, 
                                                   @Param("statusId") String statusId,
                                                   @Param("typeId") String typeId);

    /**
     * 更新交付物状态
     *
     * @param id 交付物ID
     * @param statusId 状态ID
     * @return 更新数量
     */
    int updateStatus(@Param("id") String id, @Param("statusId") String statusId);

    /**
     * 批量更新状态
     *
     * @param ids ID列表
     * @param statusId 状态ID
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("statusId") String statusId);

    /**
     * 获取交付物统计信息
     *
     * @param params 查询参数
     * @return 统计信息
     */
    List<Map<String, Object>> getWorkProductStatistics(@Param("params") Map<String, Object> params);

    /**
     * 根据项目模板ID查询关联的交付物
     * 注意：此方法需要联合查询通用关联表 zz_proj_tpl_relation
     *
     * @param projectTplId 项目模板ID
     * @return 交付物列表
     */
    List<WorkProductLibraryEntity> selectByProjectTemplateId(@Param("projectTplId") String projectTplId);

    /**
     * 查询交付物的使用情况
     *
     * @param id 交付物ID
     * @return 使用情况统计
     */
    Map<String, Object> getWorkProductUsageInfo(@Param("id") String id);

    /**
     * 生成下一个交付物编码序号
     *
     * @return 下一个序号
     */
    Integer getNextCodeSequence();

    /**
     * 根据编码查询交付物
     *
     * @param code 交付物编码
     * @return 交付物信息
     */
    WorkProductLibraryEntity selectByCode(@Param("code") String code);

    /**
     * 复制交付物
     *
     * @param sourceId 源交付物ID
     * @param targetId 目标交付物ID
     * @param newName 新名称
     * @param newCode 新编码
     * @return 复制数量
     */
    int copyWorkProduct(@Param("sourceId") String sourceId, 
                       @Param("targetId") String targetId,
                       @Param("newName") String newName, 
                       @Param("newCode") String newCode);
}
