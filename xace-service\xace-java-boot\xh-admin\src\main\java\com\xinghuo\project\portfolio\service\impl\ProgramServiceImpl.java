package com.xinghuo.project.portfolio.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.portfolio.dao.ProgramMapper;
import com.xinghuo.project.portfolio.entity.ProgramEntity;
import com.xinghuo.project.portfolio.model.ProgramPagination;
import com.xinghuo.project.portfolio.service.ProgramService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 项目群服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class ProgramServiceImpl extends BaseServiceImpl<ProgramMapper, ProgramEntity> implements ProgramService {

    @Resource
    private ProgramMapper programMapper;

    @Override
    public List<ProgramEntity> getList(ProgramPagination pagination) {
        QueryWrapper<ProgramEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProgramEntity> lambda = queryWrapper.lambda();

        // 根据项目群编码模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getCode())) {
            lambda.like(ProgramEntity::getCode, pagination.getCode());
        }

        // 根据项目群名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(ProgramEntity::getName, pagination.getName());
        }

        // 根据项目群类型ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getTypeId())) {
            lambda.eq(ProgramEntity::getTypeId, pagination.getTypeId());
        }

        // 根据项目群经理ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getManagerId())) {
            lambda.eq(ProgramEntity::getManagerId, pagination.getManagerId());
        }

        // 根据状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getStatus())) {
            lambda.eq(ProgramEntity::getStatus, pagination.getStatus());
        }

        // 根据关键字搜索名称或编码
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(ProgramEntity::getName, keyword)
                    .or()
                    .like(ProgramEntity::getCode, keyword)
            );
        }

        // 排序
        lambda.orderByDesc(ProgramEntity::getCreatedAt);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public ProgramEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveInfo(ProgramEntity entity) {
        if (StrXhUtil.isEmpty(entity.getId())) {
            // 新增
            String id = RandomUtil.snowId();
            entity.setId(id);
        }
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInfo(ProgramEntity entity) {
        return this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(String id) {
        return this.removeById(id);
    }

    @Override
    public List<ProgramEntity> getListByManagerId(String managerId) {
        return programMapper.selectByManagerId(managerId);
    }

    @Override
    public List<ProgramEntity> getListByTypeId(String typeId) {
        return programMapper.selectByTypeId(typeId);
    }

    @Override
    public List<ProgramEntity> getListByStatus(String status) {
        return programMapper.selectByStatus(status);
    }

    @Override
    public boolean isExistByCode(String code, String excludeId) {
        int count = programMapper.checkCodeExists(code, excludeId);
        return count > 0;
    }
}
