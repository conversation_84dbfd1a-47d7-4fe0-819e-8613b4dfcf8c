package com.xinghuo.project.template.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.template.entity.WorkProductPlanTemplateDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 交付物计划模板明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface WorkProductPlanTemplateDetailMapper extends XHBaseMapper<WorkProductPlanTemplateDetailEntity> {

    /**
     * 根据模板ID查询交付物明细列表
     *
     * @param workproductPlanTplId 模板ID
     * @return 交付物明细列表
     */
    List<WorkProductPlanTemplateDetailEntity> selectByTemplateId(@Param("workproductPlanTplId") String workproductPlanTplId);

    /**
     * 根据模板ID删除所有交付物明细
     *
     * @param workproductPlanTplId 模板ID
     * @return 删除数量
     */
    int deleteByTemplateId(@Param("workproductPlanTplId") String workproductPlanTplId);

    /**
     * 批量插入交付物明细
     *
     * @param details 交付物明细列表
     * @return 插入数量
     */
    int batchInsert(@Param("details") List<WorkProductPlanTemplateDetailEntity> details);

    /**
     * 更新交付物序号
     *
     * @param id 交付物明细ID
     * @param seqNo 序号
     * @return 更新数量
     */
    int updateSeqNo(@Param("id") String id, @Param("seqNo") Integer seqNo);

    /**
     * 批量更新交付物序号
     *
     * @param details 交付物明细列表（包含ID和序号）
     * @return 更新数量
     */
    int batchUpdateSeqNo(@Param("details") List<WorkProductPlanTemplateDetailEntity> details);

    /**
     * 获取模板下一个序号
     *
     * @param workproductPlanTplId 模板ID
     * @return 下一个序号
     */
    Integer getNextSeqNo(@Param("workproductPlanTplId") String workproductPlanTplId);

    /**
     * 检查交付物名称在模板内是否重复
     *
     * @param workproductPlanTplId 模板ID
     * @param name 交付物名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkNameExistsInTemplate(@Param("workproductPlanTplId") String workproductPlanTplId, 
                                  @Param("name") String name, 
                                  @Param("excludeId") String excludeId);

    /**
     * 根据标准交付物库ID查询模板中的使用情况
     *
     * @param workproductLibraryId 标准交付物库ID
     * @return 使用该标准交付物的模板明细列表
     */
    List<WorkProductPlanTemplateDetailEntity> selectByWorkProductLibraryId(@Param("workproductLibraryId") String workproductLibraryId);

    /**
     * 复制模板的所有交付物明细到新模板
     *
     * @param sourceTemplateId 源模板ID
     * @param targetTemplateId 目标模板ID
     * @return 复制数量
     */
    int copyWorkProductDetails(@Param("sourceTemplateId") String sourceTemplateId, 
                              @Param("targetTemplateId") String targetTemplateId);

    /**
     * 根据阶段模板ID查询关联的交付物明细
     *
     * @param stageTemplateId 阶段模板ID
     * @return 交付物明细列表
     */
    List<WorkProductPlanTemplateDetailEntity> selectByStageTemplateId(@Param("stageTemplateId") String stageTemplateId);

    /**
     * 根据活动模板ID查询关联的交付物明细
     *
     * @param activityTemplateId 活动模板ID
     * @return 交付物明细列表
     */
    List<WorkProductPlanTemplateDetailEntity> selectByActivityTemplateId(@Param("activityTemplateId") String activityTemplateId);

    /**
     * 统计模板中的交付物信息
     *
     * @param workproductPlanTplId 模板ID
     * @return 统计信息（总数、需要评审数量、最终交付成果数量等）
     */
    Map<String, Object> getWorkProductStatistics(@Param("workproductPlanTplId") String workproductPlanTplId);
}
