<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    :width="800"
    @ok="handleSubmit"
  >
    <div class="pt-3px pr-3px">
      <BasicTable @register="registerTable" :canResize="false">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction
              :actions="[
                {
                  icon: 'ant-design:copy-outlined',
                  label: '选择复制',
                  onClick: handleSelectBusiness.bind(null, record),
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getBusinessList, BusinessModel } from '/@/api/project/business';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'success']);
  const selectedBusiness = ref<BusinessModel | null>(null);

  // 表格列定义
  const columns = [
    {
      title: '商机编号',
      dataIndex: 'businessNo',
      width: 120,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 200,
    },
    {
      title: '客户单位',
      dataIndex: 'custName',
      width: 150,
    },
    {
      title: '项目负责人',
      dataIndex: 'projectLeaderName',
      width: 120,
    },
    {
      title: '商机状态',
      dataIndex: 'status',
      width: 100,
    },
    {
      title: '预计落地月份',
      dataIndex: 'evaSignMonth',
      width: 120,
    },
    {
      title: '预计金额',
      dataIndex: 'yearMoney',
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
    },
  ];

  // 注册表格
  const [registerTable, { reload }] = useTable({
    title: '维护商机列表',
    api: getBusinessList,
    columns,
    formConfig: {
      labelWidth: 80,
      schemas: [
        {
          field: 'projectName',
          label: '项目名称',
          component: 'Input',
          colProps: { span: 8 },
        },
        {
          field: 'custName',
          label: '客户单位',
          component: 'Input',
          colProps: { span: 8 },
        },
        {
          field: 'status',
          label: '商机状态',
          component: 'Select',
          componentProps: {
            options: [
              { fullName: '全部', id: '' },
              { fullName: '跟踪中', id: '跟踪中' },
              { fullName: '方案报价中', id: '方案报价中' },
              { fullName: '商务谈判中', id: '商务谈判中' },
              { fullName: '已签', id: '已签' },
              { fullName: '已废弃', id: '已废弃' },
              { fullName: '明年跟踪', id: '明年跟踪' },
            ],
          },
          colProps: { span: 8 },
        },
      ],
    },
    useSearchForm: true,
    showTableSetting: false,
    bordered: true,
    showIndexColumn: false,
    canResize: false,
    beforeFetch: (params) => {
      // 只查询维护商机
      return {
        ...params,
        projType: '维护项目',
      };
    },
  });

  // 注册模态框
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false });
    selectedBusiness.value = null;
    reload();
  });

  // 获取标题
  const getTitle = computed(() => {
    return '选择要复制的维护商机';
  });

  // 选择商机
  function handleSelectBusiness(record: BusinessModel) {
    selectedBusiness.value = record;
    createMessage.success(`已选择商机：${record.projectName}`);
  }

  // 提交
  async function handleSubmit() {
    try {
      if (!selectedBusiness.value) {
        createMessage.warning('请先选择要复制的维护商机');
        return;
      }

      setModalProps({ confirmLoading: true });

      // 处理日期字段，加上1年
      const businessData = { ...selectedBusiness.value };
      
      // 需要加1年的日期字段
      const dateFields = ['startDate', 'evaSignMonth', 'evaFirstMonth', 'evaSecondMonth'];
      
      dateFields.forEach(field => {
        if (businessData[field]) {
          const date = new Date(businessData[field]);
          if (!isNaN(date.getTime())) {
            // 加上1年
            date.setFullYear(date.getFullYear() + 1);
            
            if (field === 'evaSignMonth' || field === 'evaFirstMonth' || field === 'evaSecondMonth') {
              // 月份字段格式化为 YYYY-MM
              businessData[field] = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            } else {
              // 日期字段格式化为 YYYY-MM-DD
              businessData[field] = date.toISOString().split('T')[0];
            }
          }
        }
      });

      // 清除一些不需要复制的字段
      delete businessData.id;
      delete businessData.businessNo;
      delete businessData.createTime;
      delete businessData.updateTime;
      delete businessData.createBy;
      delete businessData.updateBy;
      
      // 修改项目名称，添加复制标识
      businessData.projectName = `${businessData.projectName}（复制）`;
      
      // 重置状态为跟踪中
      businessData.status = '跟踪中';
      
      // 清空最后跟踪记录
      businessData.lastNote = '';

      console.log('复制的商机数据:', businessData);

      emit('success', businessData);
      closeModal();
    } catch (error) {
      console.error('复制商机失败:', error);
      createMessage.error('复制商机失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
