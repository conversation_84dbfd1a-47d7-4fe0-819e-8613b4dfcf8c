package com.xinghuo.project.template.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.template.entity.WorkProductLibraryEntity;
import com.xinghuo.project.template.model.WorkProductLibraryPagination;
import com.xinghuo.project.template.model.dto.WorkProductLibraryDTO;

import java.util.List;
import java.util.Map;

/**
 * 标准交付物库服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface WorkProductLibraryService extends BaseService<WorkProductLibraryEntity> {

    /**
     * 分页查询交付物库列表
     *
     * @param pagination 查询条件
     * @return 交付物列表
     */
    List<WorkProductLibraryEntity> getList(WorkProductLibraryPagination pagination);

    /**
     * 根据ID查询交付物详情
     *
     * @param id 交付物ID
     * @return 交付物详情
     */
    WorkProductLibraryDTO getDetailInfo(String id);

    /**
     * 根据ID查询交付物基本信息
     *
     * @param id 交付物ID
     * @return 交付物信息
     */
    WorkProductLibraryEntity getInfo(String id);

    /**
     * 根据编码查询交付物
     *
     * @param code 交付物编码
     * @return 交付物信息
     */
    WorkProductLibraryEntity getByCode(String code);

    /**
     * 创建交付物
     *
     * @param workProductDTO 交付物信息
     * @return 创建的交付物ID
     */
    String create(WorkProductLibraryDTO workProductDTO);

    /**
     * 更新交付物
     *
     * @param id 交付物ID
     * @param workProductDTO 交付物信息
     */
    void update(String id, WorkProductLibraryDTO workProductDTO);

    /**
     * 删除交付物
     *
     * @param id 交付物ID
     */
    void delete(String id);

    /**
     * 批量删除交付物
     *
     * @param ids 交付物ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 更新交付物状态
     *
     * @param id 交付物ID
     * @param statusId 状态ID
     */
    void updateStatus(String id, String statusId);

    /**
     * 批量更新状态
     *
     * @param ids 交付物ID列表
     * @param statusId 状态ID
     */
    void batchUpdateStatus(List<String> ids, String statusId);

    /**
     * 启用交付物
     *
     * @param id 交付物ID
     */
    void enable(String id);

    /**
     * 禁用交付物
     *
     * @param id 交付物ID
     */
    void disable(String id);

    /**
     * 检查交付物名称是否存在
     *
     * @param name 交付物名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByName(String name, String excludeId);

    /**
     * 检查交付物编码是否存在
     *
     * @param code 交付物编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByCode(String code, String excludeId);

    /**
     * 获取交付物选择列表
     *
     * @param keyword 关键字
     * @param statusId 状态ID
     * @param typeId 类型ID
     * @return 交付物列表
     */
    List<WorkProductLibraryEntity> getSelectList(String keyword, String statusId, String typeId);

    /**
     * 复制交付物
     *
     * @param id 源交付物ID
     * @param newName 新交付物名称
     * @return 新交付物ID
     */
    String copy(String id, String newName);

    /**
     * 生成交付物编码
     *
     * @return 交付物编码
     */
    String generateCode();

    /**
     * 获取交付物统计信息
     *
     * @param params 查询参数
     * @return 统计信息
     */
    List<Map<String, Object>> getWorkProductStatistics(Map<String, Object> params);

    /**
     * 根据项目模板ID查询关联的交付物
     *
     * @param projectTplId 项目模板ID
     * @return 交付物列表
     */
    List<WorkProductLibraryEntity> getByProjectTemplateId(String projectTplId);

    /**
     * 更新交付物与项目模板的关联关系
     *
     * @param workProductId 交付物ID
     * @param projectTemplateIds 项目模板ID列表
     */
    void updateProjectTemplateRelations(String workProductId, List<String> projectTemplateIds);

    /**
     * 获取交付物使用情况
     *
     * @param id 交付物ID
     * @return 使用情况统计
     */
    Map<String, Object> getWorkProductUsageInfo(String id);

    /**
     * 根据类型获取交付物列表
     *
     * @param typeId 类型ID
     * @param subTypeId 子类型ID
     * @return 交付物列表
     */
    List<WorkProductLibraryEntity> getByType(String typeId, String subTypeId);

    /**
     * 根据默认角色获取交付物列表
     *
     * @param defaultRoleId 默认角色ID
     * @return 交付物列表
     */
    List<WorkProductLibraryEntity> getByDefaultRole(String defaultRoleId);

    /**
     * 根据状态获取交付物列表
     *
     * @param statusId 状态ID
     * @return 交付物列表
     */
    List<WorkProductLibraryEntity> getByStatus(String statusId);
}
