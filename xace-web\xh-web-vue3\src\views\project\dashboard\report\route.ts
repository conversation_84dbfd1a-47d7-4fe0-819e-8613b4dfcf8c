import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

// 项目报表路由配置
export default {
  path: '/project/report',
  name: 'ProjectReport',
  component: LAYOUT,
  redirect: '/project/report/index',
  meta: {
    orderNo: 50,
    icon: 'icon-ym icon-ym-chart',
    title: t('项目报表'),
    defaultTitle: '项目报表',
  },
  children: [
    {
      path: 'index',
      name: 'ProjectReportIndex',
      component: () => import('/@/views/project/report/index.vue'),
      meta: {
        title: t('报表中心'),
        defaultTitle: '报表中心',
        icon: 'icon-ym icon-ym-dashboard',
      },
    },
    {
      path: 'invoice-standalone',
      name: 'ProjectReportInvoiceStandalone',
      component: () => import('/@/views/project/report/invoice/index.vue'),
      meta: {
        title: t('已开票未收款统计'),
        defaultTitle: '已开票未收款统计',
        icon: 'icon-ym icon-ym-invoice',
        hideMenu: true, // 隐藏菜单，通过报表中心访问
      },
    },
    {
      path: 'payment',
      name: 'ProjectReportPayment',
      component: () => import('/@/views/project/report/payment/index.vue'),
      meta: {
        title: t('收款进度统计'),
        defaultTitle: '收款进度统计',
        icon: 'icon-ym icon-ym-payment',
        hideMenu: true, // 暂时隐藏，待开发
      },
    },
    {
      path: 'payable',
      name: 'ProjectReportPayable',
      component: () => import('/@/views/project/report/payable/index.vue'),
      meta: {
        title: t('付款统计报表'),
        defaultTitle: '付款统计报表',
        icon: 'icon-ym icon-ym-payable',
        hideMenu: true, // 暂时隐藏，待开发
      },
    },
    {
      path: 'contract/summary',
      name: 'ProjectReportContractSummary',
      component: () => import('/@/views/project/report/contract/summary.vue'),
      meta: {
        title: t('合同汇总统计'),
        defaultTitle: '合同汇总统计',
        icon: 'icon-ym icon-ym-contract-summary',
        hideMenu: true, // 暂时隐藏，待开发
      },
    },
    {
      path: 'contract/progress',
      name: 'ProjectReportContractProgress',
      component: () => import('/@/views/project/report/contract/progress.vue'),
      meta: {
        title: t('合同执行进度'),
        defaultTitle: '合同执行进度',
        icon: 'icon-ym icon-ym-contract-progress',
        hideMenu: true, // 暂时隐藏，待开发
      },
    },
    {
      path: 'opportunity',
      name: 'ProjectReportOpportunity',
      component: () => import('/@/views/project/report/opportunity/index.vue'),
      meta: {
        title: t('商机统计分析'),
        defaultTitle: '商机统计分析',
        icon: 'icon-ym icon-ym-opportunity',
        hideMenu: true, // 暂时隐藏，待开发
      },
    },
    {
      path: 'conversion',
      name: 'ProjectReportConversion',
      component: () => import('/@/views/project/report/conversion/index.vue'),
      meta: {
        title: t('商机转化率'),
        defaultTitle: '商机转化率',
        icon: 'icon-ym icon-ym-conversion',
        hideMenu: true, // 暂时隐藏，待开发
      },
    },
    {
      path: 'project/progress',
      name: 'ProjectReportProjectProgress',
      component: () => import('/@/views/project/report/project/progress.vue'),
      meta: {
        title: t('项目进度统计'),
        defaultTitle: '项目进度统计',
        icon: 'icon-ym icon-ym-project-progress',
        hideMenu: true, // 暂时隐藏，待开发
      },
    },
    {
      path: 'milestone',
      name: 'ProjectReportMilestone',
      component: () => import('/@/views/project/report/milestone/index.vue'),
      meta: {
        title: t('里程碑完成情况'),
        defaultTitle: '里程碑完成情况',
        icon: 'icon-ym icon-ym-milestone',
        hideMenu: true, // 暂时隐藏，待开发
      },
    },
    {
      path: 'error-test',
      name: 'ProjectReportErrorTest',
      component: () => import('/@/views/project/report/error-test.vue'),
      meta: {
        title: t('错误修复测试'),
        defaultTitle: '错误修复测试',
        icon: 'icon-ym icon-ym-test',
        hideMenu: true, // 隐藏菜单，仅用于测试
      },
    },
  ],
};
