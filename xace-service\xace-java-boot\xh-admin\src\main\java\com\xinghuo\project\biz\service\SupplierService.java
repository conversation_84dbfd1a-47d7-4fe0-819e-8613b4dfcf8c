package com.xinghuo.project.biz.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.biz.entity.SupplierEntity;
import com.xinghuo.project.biz.model.SupplierPagination;

import java.util.List;

/**
 * 供应商服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface SupplierService extends BaseService<SupplierEntity> {

    /**
     * 分页查询供应商列表
     *
     * @param pagination 查询条件
     * @return 供应商列表
     */
    List<SupplierEntity> getList(SupplierPagination pagination);

    /**
     * 根据ID查询供应商信息
     *
     * @param id 供应商ID
     * @return 供应商信息
     */
    SupplierEntity getInfo(String id);

    /**
     * 创建供应商
     *
     * @param entity 供应商信息
     * @return 供应商ID
     */
    String create(SupplierEntity entity);

    /**
     * 更新供应商
     *
     * @param id 供应商ID
     * @param entity 更新信息
     */
    void update(String id, SupplierEntity entity);

    /**
     * 删除供应商
     *
     * @param id 供应商ID
     */
    void delete(String id);

    /**
     * 检查供应商名称是否存在
     *
     * @param name 供应商名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByName(String name, String excludeId);

    /**
     * 获取供应商选择列表
     *
     * @return 供应商选择列表
     */
    List<SupplierEntity> getSelectList();
}
