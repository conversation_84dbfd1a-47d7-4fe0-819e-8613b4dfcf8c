package com.xinghuo.project.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.biz.dao.CustomerContactMapper;
import com.xinghuo.project.biz.entity.CustomerContactEntity;
import com.xinghuo.project.biz.model.CustomerLinkmanPagination;
import com.xinghuo.project.biz.service.CustomerContactService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 客户联系人服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class CustomerContactServiceImpl extends BaseServiceImpl<CustomerContactMapper, CustomerContactEntity> implements CustomerContactService {

    @Resource
    private CustomerContactMapper customerContactMapper;

    @Override
    public List<CustomerContactEntity> getList(CustomerLinkmanPagination pagination) {
        QueryWrapper<CustomerContactEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<CustomerContactEntity> lambda = queryWrapper.lambda();

        // 根据客户ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getCustomerId())) {
            lambda.eq(CustomerContactEntity::getCuId, pagination.getCustomerId());
        }



        // 根据状态精确查询
        if (pagination.getStatus() != null) {
            lambda.eq(CustomerContactEntity::getStatus, pagination.getStatus());
        }

        // 根据关键字搜索姓名、电话或邮箱
        String keyword = pagination.getKeyword();
//        if (StrXhUtil.isNotEmpty(keyword)) {
//            lambda.and(wrapper -> wrapper
//                    .like(CustomerContactEntity::getName, keyword)
//                    .or()
//                    .like(CustomerContactEntity::getPhone, keyword)
//                    .or()
//                    .like(CustomerContactEntity::getEmail, keyword)
//            );
//        }



        return processDataType(queryWrapper, pagination);
    }

    @Override
    public List<CustomerContactEntity> getListByCustomerId(String customerId) {
        return customerContactMapper.selectByCustomerId(customerId);
    }

    @Override
    public CustomerContactEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(CustomerContactEntity entity) {
        String id = RandomUtil.snowId();
        entity.setId(id);
        this.save(entity);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, CustomerContactEntity entity) {
        entity.setId(id);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String id, Integer status) {
        customerContactMapper.updateStatus(id, status);
    }

    @Override
    public List<CustomerContactEntity> getSelectList(String customerId, String keyword) {
        if (StrXhUtil.isNotEmpty(customerId)) {
            return customerContactMapper.selectByCustomerIdAndKeyword(customerId, keyword);
        } else {
            QueryWrapper<CustomerContactEntity> queryWrapper = new QueryWrapper<>();
            LambdaQueryWrapper<CustomerContactEntity> lambda = queryWrapper.lambda();
            
//            if (StrXhUtil.isNotEmpty(keyword)) {
//                lambda.and(wrapper -> wrapper
//                        .like(CustomerContactEntity::getName, keyword)
//                        .or()
//                        .like(CustomerContactEntity::getPhone, keyword)
//                        .or()
//                        .like(CustomerContactEntity::getEmail, keyword)
//                );
//            }
            
            // 只返回有效状态的联系人
            lambda.eq(CustomerContactEntity::getStatus, 1);
//            lambda.orderBy(true, true, CustomerContactEntity::getName);
            
            return this.list(queryWrapper);
        }
    }
}
