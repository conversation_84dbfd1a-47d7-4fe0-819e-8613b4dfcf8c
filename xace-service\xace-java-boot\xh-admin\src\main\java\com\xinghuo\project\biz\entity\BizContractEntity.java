package com.xinghuo.project.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目合同实体类
 * 对应数据库表：zz_proj_contract
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode
@TableName("zz_proj_contract")
public class BizContractEntity {

    /**
     * 合同ID
     */
    @TableId("c_id")
    private String id;

    /**
     * 合同名称
     * 列表上显示
     */
    @TableField("name")
    private String name;

    /**
     * 合同编号
     * 列表上显示
     */
    @TableField("c_no")
    private String cno;

    /**
     * 客户ID
     * 列表上显示  关联CustomerEntity 
     */
    @TableField("cust_id")
    private String custId;

    /**
     * 最终用户ID
     * 列表上显示  关联CustomerEntity
     */
    @TableField("final_user_id")
    private String finalUserId;

    /**
     * 负责人ID
     * 列表上显示  关联SysUserEntity
     */
    @TableField("own_id")
    private String ownId;

    /**
     * 汇报频率
     */
    @TableField("reportfrequency")
    private String reportFrequency;

    /**
     * 合同金额
     * 列表上显示
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 已收金额-自动
     * 列表上显示  列表汇总
     */
    @TableField("ys_amount")
    private BigDecimal ysAmount;

    /**
     * 本年度收款金额
     */
    @TableField("year_ys_amount")
    private BigDecimal yearYsAmount;

    /**
     * 外采金额
     */
    @TableField("external_amount")
    private BigDecimal externalAmount;

    /**
     * 收款状态
     * 列表上显示 关联数据字典encode='contractType'
     */
    @TableField("money_status")
    private String moneyStatus;

    /**
     * 合同状态
     * 列表上显示 关联数据字典encode='HSZ'
     */
    @TableField("contract_status")
    private String contractStatus;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 部门ID
     * 列表上显示 所属部门  关联SysDeptEntity
     */
    @TableField("dept_id")
    private String deptId;

    /**
     * 合同联系人
     */
    @TableField("linkman")
    private String linkman;

    /**
     * 联系电话
     */
    @TableField("linktelephone")
    private String linkTelephone;

    /**
     * 合同签订日期
     */
    @TableField("sign_date")
    private Date signDate;

    /**
     * 中标日期
     */
    @TableField("bid_date")
    private Date bidDate;

    /**
     * 开工日期
     */
    @TableField("commencement_date")
    private Date commencementDate;

    /**
     * 初验日期
     */
    @TableField("initial_check_date")
    private Date initialCheckDate;

    /**
     * 终验日期
     */
    @TableField("final_check_date")
    private Date finalCheckDate;

    /**
     * 审计日期
     */
    @TableField("audit_date")
    private Date auditDate;

    /**
     * 合同年度
     */
    @TableField("sign_year")
    private Integer signYear;

    /**
     * 合同开始日期
     */
    @TableField("cstart_date")
    private Date cstartDate;

    /**
     * 合同结束日期
     */
    @TableField("cend_date")
    private Date cendDate;

    /**
     * 维保开始日期
     */
    @TableField("mstart_date")
    private Date mstartDate;

    /**
     * 维保结束日期
     */
    @TableField("mend_date")
    private Date mendDate;

    /**
     * 监理单位ID
     */
    @TableField("sv_dept_id")
    private String svDeptId;

    /**
     * 监理单位联系人
     */
    @TableField("sv_linkman")
    private String svLinkman;

    /**
     * 监理单位联系电话
     */
    @TableField("sv_telephone")
    private String svTelephone;

    /**
     * 三方测评单位ID
     */
    @TableField("review_dept_id")
    private String reviewDeptId;

    /**
     * 三方测评联系人
     */
    @TableField("review_linkman")
    private String reviewLinkman;

    /**
     * 三方测评联系电话
     */
    @TableField("review_telephone")
    private String reviewTelephone;

    /**
     * 等保单位ID
     */
    @TableField("db_dept_id")
    private String dbDeptId;

    /**
     * 等保单位联系人
     */
    @TableField("db_linkman")
    private String dbLinkman;

    /**
     * 等保单位联系电话
     */
    @TableField("db_telephone")
    private String dbTelephone;

    /**
     * 商密评测单位ID
     */
    @TableField("sm_dept_id")
    private String smDeptId;

    /**
     * 商密评测单位联系人
     */
    @TableField("sm_linkman")
    private String smLinkman;

    /**
     * 商密评测单位联系电话
     */
    @TableField("sm_telephone")
    private String smTelephone;

    /**
     * 结算单位id
     */
    @TableField("js_dept_id")
    private String jsDeptId;

    /**
     * 结算单位联系人
     */
    @TableField("js_linkman")
    private String jsLinkman;

    /**
     * 结算单位联系电话
     */
    @TableField("js_telephone")
    private String jsTelephone;

    /**
     * 是否续签 0-正常，1-已续签，9-不续签
     */
    @TableField("is_continue")
    private Integer isContinue;

    /**
     * 合同归档状态
     */
    @TableField("is_archive")
    private Integer isArchive;

    /**
     * 新合同ID
     */
    @TableField("new_cid")
    private String newCid;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 续签说明
     */
    @TableField("continue_desc")
    private String continueDesc;

    /**
     * 类型状态
     */
    @TableField("type_status")
    private String typeStatus;

    /**
     * 外采状态
     */
    @TableField("external_status")
    private String externalStatus;

    /**
     * 分配状态
     */
    @TableField("assign_status")
    private String assignStatus;

    /**
     * 是否检查
     */
    @TableField("is_check")
    private String isCheck;

    /**
     * 预估毛利
     */
    @TableField("est_probit")
    private BigDecimal estProbit;

    /**
     * 实际毛利
     */
    @TableField("act_probit")
    private BigDecimal actProbit;

    /**
     * 预估毛利率
     */
    @TableField("est_probit_ratio")
    private BigDecimal estProbitRatio;

    /**
     * 实际毛利率
     */
    @TableField("act_probit_ratio")
    private BigDecimal actProbitRatio;

    /**
     * 采购费用预测
     */
    @TableField("eva_external_amout")
    private BigDecimal evaExternalAmount;

    /**
     * 费用预测
     */
    @TableField("eva_cost_amount")
    private BigDecimal evaCostAmount;

    /**
     * 实际采购金额
     */
    @TableField("act_external_amount")
    private BigDecimal actExternalAmount;

    /**
     * 待签外采金额
     */
    @TableField("unsign_external_amount")
    private BigDecimal unsignExternalAmount;

    /**
     * 实际费用
     */
    @TableField("act_cost_amount")
    private BigDecimal actCostAmount;

    /**
     * 一部金额
     * 列表上显示 列表汇总
     */
    @TableField("yf_yb_amount")
    private BigDecimal yfYbAmount;

    /**
     * 二部金额
     * 列表上显示 列表汇总
     */
    @TableField("yf_eb_amount")
    private BigDecimal yfEbAmount;

    /**
     * 交付分部金额
     * 列表上显示 列表汇总
     */
    @TableField("yf_jf_amount")
    private BigDecimal yfJfAmount;

    /**
     * 综合分配金额
     */
    @TableField("yf_other_amount")
    private BigDecimal yfOtherAmount;

    /**
     * 一部外采金额
     */
    @TableField("out_yb_amount")
    private BigDecimal outYbAmount;

    /**
     * 二部外采金额
     */
    @TableField("out_eb_amount")
    private BigDecimal outEbAmount;

    /**
     * 交付外采金额
     */
    @TableField("out_jf_amount")
    private BigDecimal outJfAmount;

    /**
     * 综合外采金额
     */
    @TableField("out_other_amount")
    private BigDecimal outOtherAmount;

    /**
     * 待签一部外采
     */
    @TableField("unsign_out_yb_amount")
    private BigDecimal unsignOutYbAmount;

    /**
     * 待签二部待采
     */
    @TableField("unsign_out_eb_amount")
    private BigDecimal unsignOutEbAmount;

    /**
     * 待签交付待采
     */
    @TableField("unsign_out_jf_amount")
    private BigDecimal unsignOutJfAmount;

    /**
     * 待签综合外采
     */
    @TableField("unsign_out_other_amount")
    private BigDecimal unsignOutOtherAmount;

    /**
     * 一部外采已付
     */
    @TableField("out_yf_yb_amount")
    private BigDecimal outYfYbAmount;

    /**
     * 二部外采已付
     */
    @TableField("out_yf_eb_amount")
    private BigDecimal outYfEbAmount;

    /**
     * 交付外采已付
     */
    @TableField("out_yf_jf_amount")
    private BigDecimal outYfJfAmount;

    /**
     * 综合外采已付
     */
    @TableField("out_yf_other_amount")
    private BigDecimal outYfOtherAmount;

    /**
     * 项目相关人员
     */
    @TableField("view_users")
    private String viewUsers;

    /**
     * 验收文档状态
     */
    @TableField("accdoc_status")
    private String accdocStatus;

    /**
     * 验收文档Git路径
     */
    @TableField("accdoc_path")
    private String accdocPath;

    /**
     * 验收文档说明
     */
    @TableField("accdoc_note")
    private String accdocNote;

    /**
     * 创建用户
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最后修改人
     */
    @TableField("last_modified_user_id")
    private String lastModifiedUserId;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 毛利率区间
     */
    @TableField("auto_ratio_level")
    private String autoRatioLevel;

    /**
     * 金额区间
     */
    @TableField("auto_amount_level")
    private String autoAmountLevel;

    /**
     * 实际毛利率区间
     */
    @TableField("auto_actratio_level")
    private String autoActratioLevel;

    /**
     * 租户ID
     */
    @TableField("f_tenantid")
    private String tenantId;

    /**
     * 流程ID
     */
    @TableField("f_flowid")
    private String flowId;

    /**
     * 删除标志
     */
    @TableField("f_deletemark")
    private Integer deleteMark;

    /**
     * 工时填写状态 1-可填写 0-已结束
     */
    @TableField("work_status")
    private Integer workStatus;

    /**
     * 累计工时(自动计算)
     */
    @TableField("auto_manhours")
    private BigDecimal autoManhours;
}
