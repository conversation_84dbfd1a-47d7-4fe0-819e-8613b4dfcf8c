package com.xinghuo.project.template.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.template.entity.WorkProductPlanTemplateDetailEntity;
import com.xinghuo.project.template.entity.WorkProductPlanTemplateEntity;
import com.xinghuo.project.template.model.WorkProductPlanTemplatePagination;
import com.xinghuo.project.template.model.dto.WorkProductPlanTemplateDTO;
import com.xinghuo.project.template.service.WorkProductPlanTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 交付物计划模板管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "交付物计划模板管理", description = "交付物计划模板管理相关接口")
@RestController
@RequestMapping("/api/project/template/workProductPlanTemplate")
public class WorkProductPlanTemplateController {

    @Resource
    private WorkProductPlanTemplateService workProductPlanTemplateService;

    /**
     * 获取交付物计划模板列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取交付物计划模板列表")
    public ActionResult<PageListVO<WorkProductPlanTemplateDTO>> list(@RequestBody WorkProductPlanTemplatePagination pagination) {
        try {
            List<WorkProductPlanTemplateDTO> list = workProductPlanTemplateService.getList(pagination);
            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(list, page);
        } catch (Exception e) {
            log.error("获取交付物计划模板列表失败", e);
            return ActionResult.fail("获取交付物计划模板列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据知识状态获取模板列表
     */
    @GetMapping("/getListByKnStatus/{knStatusId}")
    @Operation(summary = "根据知识状态获取模板列表")
    public ActionResult<List<WorkProductPlanTemplateEntity>> getListByKnStatus(
            @Parameter(description = "知识状态ID") @PathVariable String knStatusId) {
        try {
            List<WorkProductPlanTemplateEntity> list = workProductPlanTemplateService.getListByKnStatus(knStatusId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据知识状态获取模板列表失败", e);
            return ActionResult.fail("获取模板列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板详情（包含交付物明细）
     */
    @GetMapping("/getDetailInfo/{id}")
    @Operation(summary = "获取模板详情")
    public ActionResult<WorkProductPlanTemplateDTO> getDetailInfo(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            WorkProductPlanTemplateDTO dto = workProductPlanTemplateService.getDetailInfo(id);
            if (dto == null) {
                return ActionResult.fail("模板不存在");
            }
            return ActionResult.success(dto);
        } catch (Exception e) {
            log.error("获取模板详情失败", e);
            return ActionResult.fail("获取模板详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板基本信息
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取模板基本信息")
    public ActionResult<WorkProductPlanTemplateEntity> getInfo(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            WorkProductPlanTemplateEntity entity = workProductPlanTemplateService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("模板不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("获取模板基本信息失败", e);
            return ActionResult.fail("获取模板基本信息失败：" + e.getMessage());
        }
    }

    /**
     * 创建交付物计划模板
     */
    @PostMapping("/create")
    @Operation(summary = "创建交付物计划模板")
    public ActionResult<String> create(@RequestBody @Valid WorkProductPlanTemplateDTO templateDTO) {
        try {
            String id = workProductPlanTemplateService.create(templateDTO);
            return ActionResult.success("创建成功", id);
        } catch (Exception e) {
            log.error("创建交付物计划模板失败", e);
            return ActionResult.fail("创建交付物计划模板失败：" + e.getMessage());
        }
    }

    /**
     * 更新交付物计划模板
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新交付物计划模板")
    public ActionResult<String> update(
            @Parameter(description = "模板ID") @PathVariable String id,
            @RequestBody @Valid WorkProductPlanTemplateDTO templateDTO) {
        try {
            workProductPlanTemplateService.update(id, templateDTO);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新交付物计划模板失败", e);
            return ActionResult.fail("更新交付物计划模板失败：" + e.getMessage());
        }
    }

    /**
     * 删除交付物计划模板
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除交付物计划模板")
    public ActionResult<String> delete(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            workProductPlanTemplateService.delete(id);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除交付物计划模板失败", e);
            return ActionResult.fail("删除交付物计划模板失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除交付物计划模板
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除交付物计划模板")
    public ActionResult<String> batchDelete(@RequestBody List<String> ids) {
        try {
            workProductPlanTemplateService.batchDelete(ids);
            return ActionResult.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除交付物计划模板失败", e);
            return ActionResult.fail("批量删除交付物计划模板失败：" + e.getMessage());
        }
    }

    /**
     * 更新模板知识状态
     */
    @PutMapping("/updateKnStatus/{id}")
    @Operation(summary = "更新模板知识状态")
    public ActionResult<String> updateKnStatus(
            @Parameter(description = "模板ID") @PathVariable String id,
            @RequestParam String knStatusId) {
        try {
            workProductPlanTemplateService.updateKnStatus(id, knStatusId);
            return ActionResult.success("状态更新成功");
        } catch (Exception e) {
            log.error("更新模板知识状态失败", e);
            return ActionResult.fail("更新模板知识状态失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新知识状态
     */
    @PutMapping("/batchUpdateKnStatus")
    @Operation(summary = "批量更新知识状态")
    public ActionResult<String> batchUpdateKnStatus(
            @RequestBody List<String> ids,
            @RequestParam String knStatusId) {
        try {
            workProductPlanTemplateService.batchUpdateKnStatus(ids, knStatusId);
            return ActionResult.success("批量更新状态成功");
        } catch (Exception e) {
            log.error("批量更新模板知识状态失败", e);
            return ActionResult.fail("批量更新状态失败：" + e.getMessage());
        }
    }

    /**
     * 发布模板
     */
    @PutMapping("/publish/{id}")
    @Operation(summary = "发布模板")
    public ActionResult<String> publish(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            workProductPlanTemplateService.publish(id);
            return ActionResult.success("发布成功");
        } catch (Exception e) {
            log.error("发布模板失败", e);
            return ActionResult.fail("发布模板失败：" + e.getMessage());
        }
    }

    /**
     * 归档模板
     */
    @PutMapping("/archive/{id}")
    @Operation(summary = "归档模板")
    public ActionResult<String> archive(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            workProductPlanTemplateService.archive(id);
            return ActionResult.success("归档成功");
        } catch (Exception e) {
            log.error("归档模板失败", e);
            return ActionResult.fail("归档模板失败：" + e.getMessage());
        }
    }

    /**
     * 复制模板
     */
    @PostMapping("/copy/{id}")
    @Operation(summary = "复制模板")
    public ActionResult<String> copy(
            @Parameter(description = "模板ID") @PathVariable String id,
            @RequestParam String newName) {
        try {
            String newId = workProductPlanTemplateService.copy(id, newName);
            return ActionResult.success("复制成功", newId);
        } catch (Exception e) {
            log.error("复制模板失败", e);
            return ActionResult.fail("复制模板失败：" + e.getMessage());
        }
    }

    /**
     * 检查模板名称是否存在
     */
    @GetMapping("/checkNameExists")
    @Operation(summary = "检查模板名称是否存在")
    public ActionResult<Boolean> checkNameExists(
            @RequestParam String name,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = workProductPlanTemplateService.isExistByName(name, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查模板名称失败", e);
            return ActionResult.fail("检查模板名称失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取模板选择列表")
    public ActionResult<List<WorkProductPlanTemplateDTO>> getSelectList(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String knStatusId) {
        try {
            List<WorkProductPlanTemplateDTO> list = workProductPlanTemplateService.getSelectList(keyword, knStatusId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("获取模板选择列表失败", e);
            return ActionResult.fail("获取模板选择列表失败：" + e.getMessage());
        }
    }

    /**
     * 从标准交付物库添加交付物
     */
    @PostMapping("/addWorkProductsFromLibrary/{templateId}")
    @Operation(summary = "从标准交付物库添加交付物")
    public ActionResult<List<WorkProductPlanTemplateDetailEntity>> addWorkProductsFromLibrary(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @RequestBody List<String> workProductLibraryIds) {
        try {
            List<WorkProductPlanTemplateDetailEntity> details = workProductPlanTemplateService.addWorkProductsFromLibrary(templateId, workProductLibraryIds);
            return ActionResult.success("添加成功", details);
        } catch (Exception e) {
            log.error("从标准交付物库添加交付物失败", e);
            return ActionResult.fail("添加交付物失败：" + e.getMessage());
        }
    }

    /**
     * 更新交付物顺序
     */
    @PutMapping("/updateWorkProductOrder/{templateId}")
    @Operation(summary = "更新交付物顺序")
    public ActionResult<String> updateWorkProductOrder(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @RequestBody List<Map<String, Object>> workProductOrders) {
        try {
            workProductPlanTemplateService.updateWorkProductOrder(templateId, workProductOrders);
            return ActionResult.success("更新顺序成功");
        } catch (Exception e) {
            log.error("更新交付物顺序失败", e);
            return ActionResult.fail("更新交付物顺序失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板统计信息
     */
    @PostMapping("/getTemplateStatistics")
    @Operation(summary = "获取模板统计信息")
    public ActionResult<List<Map<String, Object>>> getTemplateStatistics(@RequestBody Map<String, Object> params) {
        try {
            List<Map<String, Object>> statistics = workProductPlanTemplateService.getTemplateStatistics(params);
            return ActionResult.success(statistics);
        } catch (Exception e) {
            log.error("获取模板统计信息失败", e);
            return ActionResult.fail("获取模板统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目模板ID查询关联的交付物计划模板
     */
    @GetMapping("/getByProjectTemplateId/{projectTplId}")
    @Operation(summary = "根据项目模板ID查询关联的交付物计划模板")
    public ActionResult<List<WorkProductPlanTemplateEntity>> getByProjectTemplateId(
            @Parameter(description = "项目模板ID") @PathVariable String projectTplId) {
        try {
            List<WorkProductPlanTemplateEntity> list = workProductPlanTemplateService.getByProjectTemplateId(projectTplId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据项目模板ID查询关联的交付物计划模板失败", e);
            return ActionResult.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 更新模板与项目模板的关联关系
     */
    @PutMapping("/updateProjectTemplateRelations/{templateId}")
    @Operation(summary = "更新模板与项目模板的关联关系")
    public ActionResult<String> updateProjectTemplateRelations(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @RequestBody List<String> projectTemplateIds) {
        try {
            workProductPlanTemplateService.updateProjectTemplateRelations(templateId, projectTemplateIds);
            return ActionResult.success("更新关联关系成功");
        } catch (Exception e) {
            log.error("更新模板与项目模板的关联关系失败", e);
            return ActionResult.fail("更新关联关系失败：" + e.getMessage());
        }
    }

    /**
     * 从项目交付物计划创建模板
     */
    @PostMapping("/createFromProject/{projectId}")
    @Operation(summary = "从项目交付物计划创建模板")
    public ActionResult<String> createFromProject(
            @Parameter(description = "项目ID") @PathVariable String projectId,
            @RequestParam String templateName,
            @RequestParam(required = false) String description) {
        try {
            String templateId = workProductPlanTemplateService.createFromProject(projectId, templateName, description);
            return ActionResult.success("创建成功", templateId);
        } catch (Exception e) {
            log.error("从项目交付物计划创建模板失败", e);
            return ActionResult.fail("创建模板失败：" + e.getMessage());
        }
    }

    /**
     * 应用模板到项目
     */
    @PostMapping("/applyToProject/{templateId}")
    @Operation(summary = "应用模板到项目")
    public ActionResult<Map<String, Object>> applyToProject(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @RequestParam String projectId) {
        try {
            Map<String, Object> result = workProductPlanTemplateService.applyToProject(templateId, projectId);
            return ActionResult.success("应用成功", result);
        } catch (Exception e) {
            log.error("应用模板到项目失败", e);
            return ActionResult.fail("应用模板失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板使用情况
     */
    @GetMapping("/getTemplateUsageInfo/{id}")
    @Operation(summary = "获取模板使用情况")
    public ActionResult<Map<String, Object>> getTemplateUsageInfo(
            @Parameter(description = "模板ID") @PathVariable String id) {
        try {
            Map<String, Object> usageInfo = workProductPlanTemplateService.getTemplateUsageInfo(id);
            return ActionResult.success(usageInfo);
        } catch (Exception e) {
            log.error("获取模板使用情况失败", e);
            return ActionResult.fail("获取模板使用情况失败：" + e.getMessage());
        }
    }

    /**
     * 根据阶段模板ID查询关联的交付物
     */
    @GetMapping("/getWorkProductsByStageTemplate/{stageTemplateId}")
    @Operation(summary = "根据阶段模板ID查询关联的交付物")
    public ActionResult<List<WorkProductPlanTemplateDetailEntity>> getWorkProductsByStageTemplate(
            @Parameter(description = "阶段模板ID") @PathVariable String stageTemplateId) {
        try {
            List<WorkProductPlanTemplateDetailEntity> list = workProductPlanTemplateService.getWorkProductsByStageTemplate(stageTemplateId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据阶段模板ID查询关联的交付物失败", e);
            return ActionResult.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据活动模板ID查询关联的交付物
     */
    @GetMapping("/getWorkProductsByActivityTemplate/{activityTemplateId}")
    @Operation(summary = "根据活动模板ID查询关联的交付物")
    public ActionResult<List<WorkProductPlanTemplateDetailEntity>> getWorkProductsByActivityTemplate(
            @Parameter(description = "活动模板ID") @PathVariable String activityTemplateId) {
        try {
            List<WorkProductPlanTemplateDetailEntity> list = workProductPlanTemplateService.getWorkProductsByActivityTemplate(activityTemplateId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据活动模板ID查询关联的交付物失败", e);
            return ActionResult.fail("查询失败：" + e.getMessage());
        }
    }
}
