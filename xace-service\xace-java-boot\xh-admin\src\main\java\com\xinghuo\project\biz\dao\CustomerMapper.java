package com.xinghuo.project.biz.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.biz.entity.CustomerEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface CustomerMapper extends XHBaseMapper<CustomerEntity> {

    /**
     * 根据客户类型查询客户列表
     *
     * @param custType 客户类型
     * @return 客户列表
     */
    List<CustomerEntity> selectByCustType(@Param("custType") String custType);

    /**
     * 根据业务线查询客户列表
     *
     * @param custLine 业务线
     * @return 客户列表
     */
    List<CustomerEntity> selectByCustLine(@Param("custLine") String custLine);

    /**
     * 根据负责人查询客户列表
     *
     * @param leader 负责人
     * @return 客户列表
     */
    List<CustomerEntity> selectByLeader(@Param("leader") String leader);

    /**
     * 检查客户名称是否存在
     *
     * @param name 客户名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkNameExists(@Param("name") String name, @Param("excludeId") String excludeId);
}
