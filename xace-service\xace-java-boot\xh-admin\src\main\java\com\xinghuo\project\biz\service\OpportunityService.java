package com.xinghuo.project.biz.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.biz.entity.OpportunityEntity;
import com.xinghuo.project.biz.model.OpportunityPagination;
import com.xinghuo.project.biz.model.OpportunityStatusForm;

import java.util.List;
import java.util.Map;

/**
 * 商机服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface OpportunityService extends BaseService<OpportunityEntity> {

    /**
     * 分页查询商机列表
     *
     * @param pagination 查询条件
     * @return 商机列表
     */
    List<OpportunityEntity> getList(OpportunityPagination pagination);

    /**
     * 根据客户ID查询商机列表
     *
     * @param custId 客户ID
     * @return 商机列表
     */
    List<OpportunityEntity> getListByCustId(String custId);

    /**
     * 根据ID查询商机信息
     *
     * @param id 商机ID
     * @return 商机信息
     */
    OpportunityEntity getInfo(String id);

    /**
     * 创建商机
     *
     * @param entity 商机信息
     * @return 商机ID
     */
    String create(OpportunityEntity entity);

    /**
     * 更新商机
     *
     * @param id 商机ID
     * @param entity 更新信息
     * @param trackChanges 是否跟踪变更
     */
    void update(String id, OpportunityEntity entity, boolean trackChanges);

    /**
     * 删除商机
     *
     * @param id 商机ID
     */
    void delete(String id);

    /**
     * 更新商机状态
     *
     * @param id 商机ID
     * @param form 状态更新表单
     */
    void updateStatus(String id, OpportunityStatusForm form);

    /**
     * 更新最后跟踪记录
     *
     * @param id 商机ID
     * @param lastNote 最后跟踪记录
     */
    void updateLastNote(String id, String lastNote);

    /**
     * 更新工时填写状态
     *
     * @param id 商机ID
     * @param workStatus 工时填写状态
     */
    void updateWorkStatus(String id, Integer workStatus);

    /**
     * 商机转合同
     *
     * @param id 商机ID
     * @param projectNo 合同编号
     * @return 合同ID
     */
    String convertToContract(String id, String projectNo);

    /**
     * 获取销售漏斗数据
     *
     * @param params 查询参数
     * @return 销售漏斗数据
     */
    List<Map<String, Object>> getSalesFunnelData(Map<String, Object> params);

    /**
     * 获取商机预测数据
     *
     * @param params 查询参数
     * @return 商机预测数据
     */
    List<Map<String, Object>> getBusinessForecastData(Map<String, Object> params);

    /**
     * 获取赢单/输单分析数据
     *
     * @param params 查询参数
     * @return 赢单/输单分析数据
     */
    List<Map<String, Object>> getWinLoseAnalysisData(Map<String, Object> params);
}
