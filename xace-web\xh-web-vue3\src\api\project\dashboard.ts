import { defHttp } from '/@/utils/http/axios';

/**
 * 项目管理仪表板API
 */

// API URL前缀
const API_PREFIX = '/api/project/dashboard';

/**
 * 仪表板查询参数接口
 */
export interface DashboardQueryParams {
  year?: number;
  month?: number;
  deptId?: string;
  userId?: string;
  startDate?: string;
  endDate?: string;
}

/**
 * 项目概览数据接口
 */
export interface ProjectOverviewData {
  projectStatistics: any[];
  healthStatistics: any[];
}

/**
 * 商机概览数据接口
 */
export interface OpportunityOverviewData {
  salesFunnel: any[];
  businessForecast: any[];
  winLoseAnalysis: any[];
}

/**
 * 合同概览数据接口
 */
export interface ContractOverviewData {
  contractStatistics: any[];
}

/**
 * 综合仪表板数据接口
 */
export interface DashboardData {
  projectStatistics: any[];
  healthStatistics: any[];
  salesFunnel: any[];
  businessForecast: any[];
  contractStatistics: any[];
}

/**
 * 我的工作台数据接口
 */
export interface MyWorkspaceData {
  myProjects: any[];
  myOpportunities: any[];
  myContracts: any[];
}

/**
 * 获取项目概览数据
 * @param params 查询参数
 * @returns 项目概览数据
 */
export const getProjectOverview = (params?: DashboardQueryParams) => {
  return defHttp.post<ProjectOverviewData>({
    url: `${API_PREFIX}/getProjectOverview`,
    data: params,
  });
};

/**
 * 获取商机概览数据
 * @param params 查询参数
 * @returns 商机概览数据
 */
export const getOpportunityOverview = (params?: DashboardQueryParams) => {
  return defHttp.post<OpportunityOverviewData>({
    url: `${API_PREFIX}/getOpportunityOverview`,
    data: params,
  });
};

/**
 * 获取合同概览数据
 * @param params 查询参数
 * @returns 合同概览数据
 */
export const getContractOverview = (params?: DashboardQueryParams) => {
  return defHttp.post<ContractOverviewData>({
    url: `${API_PREFIX}/getContractOverview`,
    data: params,
  });
};

/**
 * 获取综合仪表板数据
 * @param params 查询参数
 * @returns 综合仪表板数据
 */
export const getDashboardData = (params?: DashboardQueryParams) => {
  return defHttp.post<DashboardData>({
    url: `${API_PREFIX}/getDashboardData`,
    data: params,
  });
};

/**
 * 获取我的工作台数据
 * @param userId 用户ID
 * @returns 我的工作台数据
 */
export const getMyWorkspace = (userId: string) => {
  return defHttp.get<MyWorkspaceData>({
    url: `${API_PREFIX}/getMyWorkspace/${userId}`,
  });
};
