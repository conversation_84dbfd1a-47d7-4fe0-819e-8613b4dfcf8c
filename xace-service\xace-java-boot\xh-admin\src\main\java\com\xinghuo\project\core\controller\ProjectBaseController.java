package com.xinghuo.project.core.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.core.entity.ProjectBaseEntity;
import com.xinghuo.project.core.model.ProjectPagination;
import com.xinghuo.project.core.service.ProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目基础管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "项目基础管理", description = "项目基础管理相关接口")
@RestController
@RequestMapping("/api/project/core/project")
public class ProjectBaseController {

    @Resource
    private ProjectService projectService;

    /**
     * 获取项目列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取项目列表")
    public ActionResult<PageListVO<ProjectBaseEntity>> list(@RequestBody ProjectPagination pagination) {
        try {
            List<ProjectBaseEntity> list = projectService.getList(pagination);
            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(list, page);
        } catch (Exception e) {
            log.error("获取项目列表失败", e);
            return ActionResult.fail("获取项目列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目类型获取项目列表
     */
    @GetMapping("/getListByProjectType/{projectType}")
    @Operation(summary = "根据项目类型获取项目列表")
    public ActionResult<List<ProjectBaseEntity>> getListByProjectType(
            @Parameter(description = "项目类型") @PathVariable String projectType) {
        try {
            List<ProjectBaseEntity> list = projectService.getListByProjectType(projectType);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据项目类型获取项目列表失败", e);
            return ActionResult.fail("获取项目列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目状态获取项目列表
     */
    @GetMapping("/getListByStatus/{status}")
    @Operation(summary = "根据项目状态获取项目列表")
    public ActionResult<List<ProjectBaseEntity>> getListByStatus(
            @Parameter(description = "项目状态") @PathVariable String status) {
        try {
            List<ProjectBaseEntity> list = projectService.getListByStatus(status);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据项目状态获取项目列表失败", e);
            return ActionResult.fail("获取项目列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目经理ID获取项目列表
     */
    @GetMapping("/getListByManagerId/{managerId}")
    @Operation(summary = "根据项目经理ID获取项目列表")
    public ActionResult<List<ProjectBaseEntity>> getListByManagerId(
            @Parameter(description = "项目经理ID") @PathVariable String managerId) {
        try {
            List<ProjectBaseEntity> list = projectService.getListByManagerId(managerId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据项目经理ID获取项目列表失败", e);
            return ActionResult.fail("获取项目列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据部门ID获取项目列表
     */
    @GetMapping("/getListByDeptId/{deptId}")
    @Operation(summary = "根据部门ID获取项目列表")
    public ActionResult<List<ProjectBaseEntity>> getListByDeptId(
            @Parameter(description = "部门ID") @PathVariable String deptId) {
        try {
            List<ProjectBaseEntity> list = projectService.getListByDeptId(deptId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据部门ID获取项目列表失败", e);
            return ActionResult.fail("获取项目列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取项目详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取项目详情")
    public ActionResult<ProjectBaseEntity> getInfo(
            @Parameter(description = "项目ID") @PathVariable String id) {
        try {
            ProjectBaseEntity entity = projectService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("项目不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("获取项目详情失败", e);
            return ActionResult.fail("获取项目详情失败：" + e.getMessage());
        }
    }

    /**
     * 创建项目
     */
    @PostMapping("/create")
    @Operation(summary = "创建项目")
    public ActionResult<String> create(@RequestBody @Valid ProjectBaseEntity entity) {
        try {
            String id = projectService.create(entity);
            return ActionResult.success("创建成功", id);
        } catch (Exception e) {
            log.error("创建项目失败", e);
            return ActionResult.fail("创建项目失败：" + e.getMessage());
        }
    }

    /**
     * 更新项目
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新项目")
    public ActionResult<String> update(
            @Parameter(description = "项目ID") @PathVariable String id,
            @RequestBody @Valid ProjectBaseEntity entity) {
        try {
            projectService.update(id, entity);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新项目失败", e);
            return ActionResult.fail("更新项目失败：" + e.getMessage());
        }
    }

    /**
     * 删除项目
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除项目")
    public ActionResult<String> delete(
            @Parameter(description = "项目ID") @PathVariable String id) {
        try {
            projectService.delete(id);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除项目失败", e);
            return ActionResult.fail("删除项目失败：" + e.getMessage());
        }
    }

    /**
     * 更新项目状态
     */
    @PutMapping("/updateStatus/{id}")
    @Operation(summary = "更新项目状态")
    public ActionResult<String> updateStatus(
            @Parameter(description = "项目ID") @PathVariable String id,
            @RequestParam String status) {
        try {
            projectService.updateStatus(id, status);
            return ActionResult.success("状态更新成功");
        } catch (Exception e) {
            log.error("更新项目状态失败", e);
            return ActionResult.fail("更新项目状态失败：" + e.getMessage());
        }
    }

    /**
     * 更新项目健康度
     */
    @PutMapping("/updateHealth/{id}")
    @Operation(summary = "更新项目健康度")
    public ActionResult<String> updateHealth(
            @Parameter(description = "项目ID") @PathVariable String id,
            @RequestParam String health) {
        try {
            projectService.updateHealth(id, health);
            return ActionResult.success("健康度更新成功");
        } catch (Exception e) {
            log.error("更新项目健康度失败", e);
            return ActionResult.fail("更新项目健康度失败：" + e.getMessage());
        }
    }

    /**
     * 检查项目编码是否存在
     */
    @GetMapping("/checkCodeExists")
    @Operation(summary = "检查项目编码是否存在")
    public ActionResult<Boolean> checkCodeExists(
            @RequestParam String code,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = projectService.isExistByCode(code, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查项目编码失败", e);
            return ActionResult.fail("检查项目编码失败：" + e.getMessage());
        }
    }

    /**
     * 获取项目统计数据
     */
    @PostMapping("/getProjectStatistics")
    @Operation(summary = "获取项目统计数据")
    public ActionResult<List<Map<String, Object>>> getProjectStatistics(@RequestBody Map<String, Object> params) {
        try {
            List<Map<String, Object>> statistics = projectService.getProjectStatistics(params);
            return ActionResult.success(statistics);
        } catch (Exception e) {
            log.error("获取项目统计数据失败", e);
            return ActionResult.fail("获取项目统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取项目健康度统计
     */
    @PostMapping("/getProjectHealthStatistics")
    @Operation(summary = "获取项目健康度统计")
    public ActionResult<List<Map<String, Object>>> getProjectHealthStatistics(@RequestBody Map<String, Object> params) {
        try {
            List<Map<String, Object>> statistics = projectService.getProjectHealthStatistics(params);
            return ActionResult.success(statistics);
        } catch (Exception e) {
            log.error("获取项目健康度统计失败", e);
            return ActionResult.fail("获取项目健康度统计失败：" + e.getMessage());
        }
    }

    /**
     * 项目归档
     */
    @PutMapping("/archive/{id}")
    @Operation(summary = "项目归档")
    public ActionResult<String> archiveProject(
            @Parameter(description = "项目ID") @PathVariable String id) {
        try {
            projectService.archiveProject(id);
            return ActionResult.success("项目归档成功");
        } catch (Exception e) {
            log.error("项目归档失败", e);
            return ActionResult.fail("项目归档失败：" + e.getMessage());
        }
    }

    /**
     * 项目激活
     */
    @PutMapping("/activate/{id}")
    @Operation(summary = "项目激活")
    public ActionResult<String> activateProject(
            @Parameter(description = "项目ID") @PathVariable String id) {
        try {
            projectService.activateProject(id);
            return ActionResult.success("项目激活成功");
        } catch (Exception e) {
            log.error("项目激活失败", e);
            return ActionResult.fail("项目激活失败：" + e.getMessage());
        }
    }

    /**
     * 获取项目选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取项目选择列表")
    public ActionResult<List<Map<String, Object>>> getSelectList(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status) {
        try {
            Map<String, Object> params = new HashMap<>();
            if (keyword != null) {
                params.put("keyword", keyword);
            }
            if (status != null) {
                params.put("status", status);
            }
            
            // 构建简化的查询条件
            ProjectPagination pagination = new ProjectPagination();
            pagination.setKeyword(keyword);
            pagination.setStatus(status);
            pagination.setPageSize(100); // 限制返回数量
            
            List<ProjectBaseEntity> list = projectService.getList(pagination);
            
            // 转换为选择列表格式
            List<Map<String, Object>> selectList = list.stream().map(project -> {
                Map<String, Object> option = new HashMap<>();
                option.put("id", project.getId());
                option.put("code", project.getCode());
                option.put("name", project.getName());
                option.put("fullName", project.getCode() + " - " + project.getName());
                option.put("status", project.getStatus());
                return option;
            }).toList();
            
            return ActionResult.success(selectList);
        } catch (Exception e) {
            log.error("获取项目选择列表失败", e);
            return ActionResult.fail("获取项目选择列表失败：" + e.getMessage());
        }
    }
}
