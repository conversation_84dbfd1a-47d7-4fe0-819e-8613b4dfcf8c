<template>
  <BasicModal 
    v-bind="$attrs" 
    @register="registerModal" 
    :title="getTitle" 
    @ok="handleSubmit"
    width="1200px"
    :okText="isView ? '关闭' : '保存'"
    :showCancelBtn="!isView"
  >
    <div class="template-modal-content">
      <!-- 基本信息 -->
      <div class="basic-info-section">
        <h3 class="section-title">基本信息</h3>
        <BasicForm @register="registerForm" />
      </div>

      <!-- 阶段明细 -->
      <div class="phase-detail-section" v-if="!isView || phaseDetailList.length > 0">
        <div class="section-header">
          <h3 class="section-title">阶段明细</h3>
          <div class="section-actions" v-if="!isView">
            <a-button type="primary" @click="handleAddPhaseFromLibrary">
              <Icon icon="ant-design:plus-outlined" />
              从阶段库添加
            </a-button>
            <a-button @click="handleAddPhase">
              <Icon icon="ant-design:plus-outlined" />
              直接添加
            </a-button>
          </div>
        </div>
        
        <div class="phase-list">
          <div 
            v-for="(phase, index) in phaseDetailList" 
            :key="phase.id || index"
            class="phase-item"
            :class="{ 'phase-item-readonly': isView }"
          >
            <div class="phase-item-header">
              <div class="phase-order">
                <Icon icon="ant-design:menu-outlined" class="drag-handle" v-if="!isView" />
                <span class="order-number">{{ index + 1 }}</span>
              </div>
              <div class="phase-info">
                <div class="phase-name">{{ phase.name }}</div>
                <div class="phase-code" v-if="phase.code">编码：{{ phase.code }}</div>
              </div>
              <div class="phase-meta">
                <span class="duration">{{ phase.duration || 1 }}天</span>
                <Tag :color="phase.canCut === 1 ? 'green' : 'red'">
                  {{ phase.canCut === 1 ? '可裁剪' : '不可裁剪' }}
                </Tag>
              </div>
              <div class="phase-actions" v-if="!isView">
                <a-button type="link" size="small" @click="handleEditPhase(phase, index)">
                  <Icon icon="ant-design:edit-outlined" />
                </a-button>
                <a-button type="link" size="small" danger @click="handleDeletePhase(index)">
                  <Icon icon="ant-design:delete-outlined" />
                </a-button>
              </div>
            </div>
            <div class="phase-description" v-if="phase.description">
              {{ phase.description }}
            </div>
          </div>
          
          <div v-if="phaseDetailList.length === 0" class="empty-phase">
            <a-empty description="暂无阶段明细" />
          </div>
        </div>
      </div>
    </div>

    <!-- 阶段编辑弹窗 -->
    <PhaseDetailModal @register="registerPhaseModal" @success="handlePhaseSuccess" />
    
    <!-- 从阶段库选择弹窗 -->
    <PhaseLibraryModal @register="registerLibraryModal" @success="handleLibrarySuccess" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref, reactive } from 'vue';
  import { Tag } from 'ant-design-vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { Icon } from '/@/components/Icon';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  
  import PhaseDetailModal from './PhaseDetailModal.vue';
  import PhaseLibraryModal from './PhaseLibraryModal.vue';
  import { formSchema } from './phasePlanTemplate.data';
  import { 
    createPhasePlanTemplate, 
    updatePhasePlanTemplate, 
    checkPhasePlanTemplateNameExists,
    getPhasePlanTemplateDetailInfo
  } from '/@/api/project/phasePlanTemplate';

  defineOptions({ name: 'PhasePlanTemplateModal' });

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  const isUpdate = ref(false);
  const isView = ref(false);
  const rowId = ref('');
  const phaseDetailList = ref<any[]>([]);

  const [registerPhaseModal, { openModal: openPhaseModal }] = useModal();
  const [registerLibraryModal, { openModal: openLibraryModal }] = useModal();

  const [registerForm, { setFieldsValue, updateSchema, resetFields, validate }] = useForm({
    labelWidth: 100,
    baseColProps: { span: 24 },
    schemas: formSchema,
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;
    isView.value = !!data?.isView;
    phaseDetailList.value = [];

    if (unref(isUpdate) || unref(isView)) {
      rowId.value = data.record.id;
      
      // 获取详细信息（包含阶段明细）
      try {
        const detailInfo = await getPhasePlanTemplateDetailInfo(data.record.id);
        if (detailInfo) {
          setFieldsValue({
            ...detailInfo,
          });
          phaseDetailList.value = detailInfo.phaseDetails || [];
        }
      } catch (error) {
        createMessage.error('获取模板详情失败');
      }
    }

    // 设置表单只读状态
    const schemas = formSchema.map((schema) => ({
      ...schema,
      componentProps: {
        ...schema.componentProps,
        disabled: unref(isView),
      },
    }));
    updateSchema(schemas);

    // 添加名称重复验证
    if (!unref(isView)) {
      updateSchema([
        {
          field: 'name',
          rules: [
            { required: true, message: '请输入模板名称' },
            {
              validator: async (_, value) => {
                if (!value) return Promise.resolve();
                const exists = await checkPhasePlanTemplateNameExists(value, unref(isUpdate) ? rowId.value : undefined);
                if (exists) {
                  return Promise.reject('模板名称已存在');
                }
                return Promise.resolve();
              },
              trigger: 'blur',
            },
          ],
        },
      ]);
    }
  });

  const getTitle = computed(() => {
    if (unref(isView)) return '查看阶段计划模板';
    return !unref(isUpdate) ? '新建阶段计划模板' : '编辑阶段计划模板';
  });

  function handleAddPhase() {
    openPhaseModal(true, {
      isUpdate: false,
    });
  }

  function handleEditPhase(phase: any, index: number) {
    openPhaseModal(true, {
      record: phase,
      index,
      isUpdate: true,
    });
  }

  function handleDeletePhase(index: number) {
    phaseDetailList.value.splice(index, 1);
    // 重新设置序号
    phaseDetailList.value.forEach((phase, idx) => {
      phase.seqNo = idx + 1;
    });
  }

  function handleAddPhaseFromLibrary() {
    openLibraryModal(true, {});
  }

  function handlePhaseSuccess(data: any) {
    if (data.isUpdate) {
      // 更新现有阶段
      phaseDetailList.value[data.index] = { ...data.record };
    } else {
      // 添加新阶段
      const newPhase = { ...data.record };
      newPhase.seqNo = phaseDetailList.value.length + 1;
      phaseDetailList.value.push(newPhase);
    }
  }

  function handleLibrarySuccess(selectedPhases: any[]) {
    // 从阶段库添加选中的阶段
    selectedPhases.forEach(phase => {
      const newPhase = {
        code: phase.code,
        name: phase.name,
        description: phase.description,
        duration: phase.stdDuration || 1,
        canCut: 1,
        approvalSchemaId: phase.defaultApprovalId,
        checkTemplateId: phase.defaultChecklistId,
        seqNo: phaseDetailList.value.length + 1,
      };
      phaseDetailList.value.push(newPhase);
    });
  }

  async function handleSubmit() {
    if (unref(isView)) {
      closeModal();
      return;
    }

    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      // 构建提交数据
      const submitData = {
        ...values,
        phaseDetails: phaseDetailList.value,
      };

      if (unref(isUpdate)) {
        await updatePhasePlanTemplate(rowId.value, submitData);
        createMessage.success('更新成功');
      } else {
        await createPhasePlanTemplate(submitData);
        createMessage.success('创建成功');
      }

      closeModal();
      emit('success');
    } catch (error) {
      createMessage.error(unref(isUpdate) ? '更新失败' : '创建失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<style lang="less" scoped>
.template-modal-content {
  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #262626;
  }

  .basic-info-section {
    margin-bottom: 24px;
  }

  .phase-detail-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .section-actions {
        display: flex;
        gap: 8px;
      }
    }

    .phase-list {
      .phase-item {
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        margin-bottom: 12px;
        padding: 16px;
        background: #fafafa;

        &.phase-item-readonly {
          background: #f5f5f5;
        }

        .phase-item-header {
          display: flex;
          align-items: center;
          gap: 12px;

          .phase-order {
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 60px;

            .drag-handle {
              cursor: move;
              color: #8c8c8c;
            }

            .order-number {
              font-weight: 600;
              color: #1890ff;
            }
          }

          .phase-info {
            flex: 1;

            .phase-name {
              font-weight: 600;
              font-size: 14px;
              color: #262626;
            }

            .phase-code {
              font-size: 12px;
              color: #8c8c8c;
              margin-top: 4px;
            }
          }

          .phase-meta {
            display: flex;
            align-items: center;
            gap: 8px;

            .duration {
              font-size: 12px;
              color: #595959;
            }
          }

          .phase-actions {
            display: flex;
            gap: 4px;
          }
        }

        .phase-description {
          margin-top: 12px;
          padding-top: 12px;
          border-top: 1px solid #e8e8e8;
          font-size: 13px;
          color: #595959;
          line-height: 1.5;
        }
      }

      .empty-phase {
        text-align: center;
        padding: 40px 0;
      }
    }
  }
}
</style>
