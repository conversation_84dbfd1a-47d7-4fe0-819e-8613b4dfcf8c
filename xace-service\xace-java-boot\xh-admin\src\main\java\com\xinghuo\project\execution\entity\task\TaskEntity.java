package com.xinghuo.project.execution.entity.task;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目任务实体类
 */
@Data
@EqualsAndHashCode
@TableName("zz_project_task")
public class TaskEntity {

    /**
     * 主键ID
     */
    @TableId("id")
    private String id;

    /**
     * 所属分部
     */
    @TableField("fb_id")
    private String fbId;

    /**
     * 专题任务编号
     */
    @TableField("bill_no")
    private String billNo;

    /**
     * 任务标题
     */
    @TableField("title")
    private String title;

    /**
     * 指派人
     */
    @TableField("leader")
    private String leader;

    /**
     * 任务描述
     */
    @TableField("task_note")
    private String taskNote;

    /**
     * 开始日期
     */
    @TableField("begin_date")
    private Date beginDate;

    /**
     * 结束日期
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 累计工时(自动计算)
     */
    @TableField("auto_manhours")
    private BigDecimal autoManhours;

    /**
     * 创建用户
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最后修改人
     */
    @TableField("last_modified_user_id")
    private String lastModifiedUserId;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除标志
     */
    @TableField("F_DeleteMark")
    private Integer deleteMark;

    /**
     * 流程ID
     */
    @TableField("f_flowid")
    private String flowId;

    /**
     * 租户ID
     */
    @TableField("f_tenantid")
    private String tenantId;

    /**
     * 工时填写状态 1-可填写 0-已结束
     */
    @TableField("work_status")
    private Integer workStatus;
}
