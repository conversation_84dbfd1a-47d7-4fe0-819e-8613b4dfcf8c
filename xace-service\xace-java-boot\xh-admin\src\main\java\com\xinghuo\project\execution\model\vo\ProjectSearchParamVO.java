package com.xinghuo.project.execution.model.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 项目搜索参数VO
 * 对应API: /api/system/common/search/projects
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
public class ProjectSearchParamVO {

    /**
     * 菜单ID
     */
    private Integer menuID;

    /**
     * 搜索名称
     */
    private String searchName;

    /**
     * 功能名称
     */
    private String functionName;

    /**
     * 项目模板ID
     */
    private String templateID;

    /**
     * 项目组合ID
     */
    private String portfolioID;

    /**
     * 项目状态
     * 0-未开始, 1-计划中, 2-进行中, 3-暂停, 4-已完成, 5-已取消, 6-已关闭
     */
    private Integer status;

    /**
     * 是否分页
     */
    private Boolean paged;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 搜索类型
     * simpleSearch-简单搜索, advancedSearch-高级搜索
     */
    private String searchType;

    /**
     * 项目经理用户ID
     */
    private String managerUserId;

    /**
     * 部门ID
     */
    private String departmentId;

    /**
     * 项目类型
     */
    private Integer projectType;

    /**
     * 备选状态
     */
    private Integer alternativeStatus;

    /**
     * 工作流状态
     */
    private Integer wfStatus;

    /**
     * 项目优先级
     */
    private Integer priority;

    /**
     * 项目风险等级
     */
    private Integer riskLevel;

    /**
     * 项目健康度
     */
    private Integer healthStatus;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;

    /**
     * 计划开始时间开始
     */
    private Date planStartTimeStart;

    /**
     * 计划开始时间结束
     */
    private Date planStartTimeEnd;

    /**
     * 计划结束时间开始
     */
    private Date planEndTimeStart;

    /**
     * 计划结束时间结束
     */
    private Date planEndTimeEnd;

    /**
     * 预期结束时间开始
     */
    private Date expectEndTimeStart;

    /**
     * 预期结束时间结束
     */
    private Date expectEndTimeEnd;

    /**
     * 项目标签列表
     */
    private List<String> tags;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 关键字搜索（项目名称、编码、描述）
     */
    private String keyword;

    /**
     * 创建人用户ID
     */
    private String creatorUserId;

    /**
     * 父项目ID
     */
    private String parentProjectId;

    /**
     * 是否包含子项目
     */
    private Boolean includeSubProjects;

    /**
     * 项目团队成员用户ID列表
     */
    private List<String> teamMemberIds;

    /**
     * 项目角色ID列表
     */
    private List<String> roleIds;

    /**
     * 预算范围最小值
     */
    private Double budgetMin;

    /**
     * 预算范围最大值
     */
    private Double budgetMax;

    /**
     * 进度范围最小值
     */
    private Double progressMin;

    /**
     * 进度范围最大值
     */
    private Double progressMax;

    /**
     * 是否我参与的项目
     */
    private Boolean myParticipated;

    /**
     * 是否我管理的项目
     */
    private Boolean myManaged;

    /**
     * 是否我关注的项目
     */
    private Boolean myFollowed;

    /**
     * 是否我收藏的项目
     */
    private Boolean myFavorited;

    /**
     * 是否最近访问的项目
     */
    private Boolean recentlyVisited;

    /**
     * 最近访问天数
     */
    private Integer recentDays;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向
     * asc-升序, desc-降序
     */
    private String sortDirection;

    /**
     * 自定义字段搜索条件
     */
    private java.util.Map<String, Object> customFieldConditions;

    /**
     * 扩展搜索条件
     */
    private java.util.Map<String, Object> extendConditions;

    /**
     * 搜索结果包含的字段
     */
    private List<String> includeFields;

    /**
     * 搜索结果排除的字段
     */
    private List<String> excludeFields;

    /**
     * 是否包含统计信息
     */
    private Boolean includeStatistics;

    /**
     * 是否包含权限信息
     */
    private Boolean includePermissions;

    /**
     * 搜索范围
     * all-全部, department-部门, portfolio-组合, personal-个人
     */
    private String searchScope;

    /**
     * 数据权限过滤
     */
    private Boolean dataPermissionFilter;

    /**
     * 搜索历史记录
     */
    private Boolean saveSearchHistory;

    /**
     * 搜索配置ID
     */
    private String searchConfigId;
}
