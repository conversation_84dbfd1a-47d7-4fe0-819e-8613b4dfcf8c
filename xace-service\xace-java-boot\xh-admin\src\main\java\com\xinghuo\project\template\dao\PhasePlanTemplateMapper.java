package com.xinghuo.project.template.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.template.entity.PhasePlanTemplateEntity;
import com.xinghuo.project.template.model.dto.PhasePlanTemplateDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 阶段计划模板Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface PhasePlanTemplateMapper extends XHBaseMapper<PhasePlanTemplateEntity> {

    /**
     * 检查模板名称是否存在
     *
     * @param name 模板名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkNameExists(@Param("name") String name, @Param("excludeId") String excludeId);

    /**
     * 根据知识状态查询模板列表
     *
     * @param knStatusId 知识状态ID
     * @return 模板列表
     */
    List<PhasePlanTemplateEntity> selectByKnStatus(@Param("knStatusId") String knStatusId);

    /**
     * 获取模板详情（包含阶段明细）
     *
     * @param id 模板ID
     * @return 模板详情
     */
    PhasePlanTemplateDTO selectDetailById(@Param("id") String id);

    /**
     * 获取模板选择列表
     *
     * @param keyword 关键字
     * @param knStatusId 知识状态ID
     * @return 模板列表
     */
    List<PhasePlanTemplateDTO> selectForSelect(@Param("keyword") String keyword, @Param("knStatusId") String knStatusId);

    /**
     * 更新模板知识状态
     *
     * @param id 模板ID
     * @param knStatusId 知识状态ID
     * @return 更新数量
     */
    int updateKnStatus(@Param("id") String id, @Param("knStatusId") String knStatusId);

    /**
     * 批量更新知识状态
     *
     * @param ids ID列表
     * @param knStatusId 知识状态ID
     * @return 更新数量
     */
    int batchUpdateKnStatus(@Param("ids") List<String> ids, @Param("knStatusId") String knStatusId);

    /**
     * 获取模板统计信息
     *
     * @param params 查询参数
     * @return 统计信息
     */
    List<Map<String, Object>> getTemplateStatistics(@Param("params") Map<String, Object> params);

    /**
     * 根据项目模板ID查询关联的阶段计划模板
     *
     * @param projectTplId 项目模板ID
     * @return 阶段计划模板列表
     */
    List<PhasePlanTemplateEntity> selectByProjectTemplateId(@Param("projectTplId") String projectTplId);

    /**
     * 查询模板的使用情况
     *
     * @param id 模板ID
     * @return 使用情况统计
     */
    Map<String, Object> getTemplateUsageInfo(@Param("id") String id);
}
