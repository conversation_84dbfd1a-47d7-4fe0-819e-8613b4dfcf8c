package com.xinghuo.project.biz.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.biz.entity.ContractEntity;
import com.xinghuo.project.biz.model.ContractPagination;

import java.util.List;
import java.util.Map;

/**
 * 合同服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ContractService extends BaseService<ContractEntity> {

    /**
     * 分页查询合同列表
     *
     * @param pagination 查询条件
     * @return 合同列表
     */
    List<ContractEntity> getList(ContractPagination pagination);

    /**
     * 根据客户ID查询合同列表
     *
     * @param custId 客户ID
     * @return 合同列表
     */
    List<ContractEntity> getListByCustId(String custId);

    /**
     * 根据负责人ID查询合同列表
     *
     * @param ownId 负责人ID
     * @return 合同列表
     */
    List<ContractEntity> getListByOwnId(String ownId);

    /**
     * 根据ID查询合同信息
     *
     * @param id 合同ID
     * @return 合同信息
     */
    ContractEntity getInfo(String id);

    /**
     * 创建合同
     *
     * @param entity 合同信息
     * @return 合同ID
     */
    String create(ContractEntity entity);

    /**
     * 更新合同
     *
     * @param id 合同ID
     * @param entity 更新信息
     */
    void update(String id, ContractEntity entity);

    /**
     * 删除合同
     *
     * @param id 合同ID
     */
    void delete(String id);

    /**
     * 更新合同状态
     *
     * @param id 合同ID
     * @param contractStatus 合同状态
     */
    void updateContractStatus(String id, String contractStatus);

    /**
     * 更新收款状态
     *
     * @param id 合同ID
     * @param moneyStatus 收款状态
     */
    void updateMoneyStatus(String id, String moneyStatus);

    /**
     * 更新工时填写状态
     *
     * @param id 合同ID
     * @param workStatus 工时填写状态
     */
    void updateWorkStatus(String id, Integer workStatus);

    /**
     * 合同续签
     *
     * @param id 原合同ID
     * @param newContract 新合同信息
     * @return 新合同ID
     */
    String renewContract(String id, ContractEntity newContract);

    /**
     * 检查合同编号是否存在
     *
     * @param cno 合同编号
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByCno(String cno, String excludeId);

    /**
     * 获取合同统计数据
     *
     * @param params 查询参数
     * @return 统计数据
     */
    List<Map<String, Object>> getContractStatistics(Map<String, Object> params);

    /**
     * 计算合同毛利率
     *
     * @param id 合同ID
     */
    void calculateProfitRatio(String id);

    /**
     * 更新合同已收金额
     *
     * @param id 合同ID
     */
    void updateReceivedAmount(String id);

    /**
     * 更新合同累计工时
     *
     * @param id 合同ID
     */
    void updateAutoManhours(String id);
}
