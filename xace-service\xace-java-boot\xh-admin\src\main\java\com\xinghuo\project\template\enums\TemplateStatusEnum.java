package com.xinghuo.project.template.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模板状态枚举
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Getter
@AllArgsConstructor
public enum TemplateStatusEnum {

    /**
     * 草稿状态
     */
    DRAFT("draft", "草稿"),

    /**
     * 启用状态
     */
    ACTIVE("active", "启用"),

    /**
     * 归档状态
     */
    ARCHIVED("archived", "归档"),

    /**
     * 禁用状态
     */
    INACTIVE("inactive", "禁用");

    private final String code;
    private final String message;

    /**
     * 根据代码获取枚举
     */
    public static TemplateStatusEnum getByCode(String code) {
        for (TemplateStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
