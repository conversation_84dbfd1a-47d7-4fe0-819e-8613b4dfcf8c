<template>
  <div class="invoice-report-container">
    <!-- 页面标题 -->
    <div class="report-header mb-4">
      <h2 class="report-title">
        <FileTextOutlined class="title-icon" />
        已开票未收款统计报表
      </h2>
      <div class="report-description">
        查看已开票但尚未收到款项的统计数据，帮助您了解应收账款情况
      </div>
    </div>

    <!-- 查询条件 -->
    <a-card :bordered="false" class="search-card mb-4">
      <BasicForm @register="registerSearchForm" @submit="handleSearch" @reset="handleReset">
        <template #submitBefore>
          <a-button type="primary" @click="handleExport" :loading="exportLoading">
            <template #icon><ExportOutlined /></template>
            导出Excel
          </a-button>
        </template>
      </BasicForm>
    </a-card>

    <!-- 项目经理统计汇总 -->
    <a-card :bordered="false" class="summary-card mb-4" v-if="managerSummary.length > 0">
      <template #title>
        <div class="card-title">
          <UserOutlined class="title-icon" />
          项目经理汇总统计
        </div>
      </template>
      <div class="manager-summary">
        <div class="summary-grid">
          <div 
            v-for="item in managerSummary" 
            :key="item.manager"
            class="summary-item"
          >
            <div class="manager-name">{{ item.manager }}</div>
            <div class="manager-amount">{{ formatAmount(item.totalAmount) }}</div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 明细数据表格 -->
    <a-card :bordered="false" class="table-card">
      <template #title>
        <div class="card-title">
          <TableOutlined class="title-icon" />
          明细数据
        </div>
      </template>
      <template #extra>
        <a-space>
          <a-statistic 
            title="总金额" 
            :value="totalAmount" 
            :formatter="formatAmount"
            class="total-statistic"
          />
        </a-space>
      </template>
      
      <BasicTable 
        @register="registerTable" 
        :loading="loading"
        :scroll="{ x: 1800 }"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ index + 1 }}
          </template>
          <template v-else-if="column.key === 'contractName'">
            <a-tooltip :title="record.contractName">
              <span class="contract-name">{{ record.contractName }}</span>
            </a-tooltip>
          </template>
          <template v-else-if="column.key === 'cmMoney'">
            <span class="amount-text">{{ formatAmount(record.cmMoney) }}</span>
          </template>
          <template v-else-if="column.key === 'ybAmount'">
            <span class="amount-text">{{ formatAmount(record.ybAmount) }}</span>
          </template>
          <template v-else-if="column.key === 'ebAmount'">
            <span class="amount-text">{{ formatAmount(record.ebAmount) }}</span>
          </template>
          <template v-else-if="column.key === 'otherAmount'">
            <span class="amount-text">{{ formatAmount(record.otherAmount) }}</span>
          </template>
          <template v-else-if="column.key === 'allAmount'">
            <span class="amount-text total-amount">{{ formatAmount(record.allAmount) }}</span>
          </template>
          <template v-else-if="column.key === 'payStatus'">
            <a-tag :color="getPayStatusColor(record.payStatus)">
              {{ getPayStatusText(record.payStatus) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'note'">
            <a-tooltip :title="record.fullNote" v-if="record.note">
              <span class="note-text">{{ record.note }}</span>
            </a-tooltip>
            <span v-else>-</span>
          </template>
        </template>
      </BasicTable>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed } from 'vue';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form';
  import { BasicTable, useTable, BasicColumn } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { 
    getInvoiceReportList, 
    getInvoiceReportSummary,
    exportInvoiceReport,
    getDepartmentList 
  } from '/@/api/project/report/invoice';
  import { 
    ExportOutlined, 
    UserOutlined, 
    TableOutlined,
    FileTextOutlined
  } from '@ant-design/icons-vue';

  defineOptions({ name: 'ProjectReportInvoice' });

  const { createMessage } = useMessage();
  const loading = ref(false);
  const exportLoading = ref(false);
  const managerSummary = ref<any[]>([]);
  const totalAmount = ref(0);

  // 生成年份选项
  const currentYear = new Date().getFullYear();
  const yearOptions = [];
  for (let year = 2020; year <= currentYear; year++) {
    yearOptions.push({ label: `${year}年`, value: year });
  }
  yearOptions.reverse(); // 最新年份在前

  // 查询表单配置
  const searchSchemas: FormSchema[] = [
    {
      field: 'year',
      label: '年份',
      component: 'Select',
      componentProps: {
        placeholder: '请选择年份',
        options: yearOptions,
        allowClear: false,
      },
      colProps: { span: 6 },
    },
    {
      field: 'dept',
      label: '分部',
      component: 'Select',
      componentProps: {
        placeholder: '请选择分部',
        options: [
          { label: '一部', value: 'YB' },
          { label: '二部', value: 'EB' },
          { label: '综合', value: 'OTHER' },
        ],
        allowClear: true,
      },
      colProps: { span: 6 },
    },
    {
      field: 'xmjl',
      label: '项目经理',
      component: 'Input',
      componentProps: {
        placeholder: '请输入项目经理姓名',
      },
      colProps: { span: 6 },
    },
  ];

  // 表格列配置
  const columns: BasicColumn[] = [
    {
      title: '序号',
      key: 'index',
      width: 60,
      align: 'center',
    },
    {
      title: '月份',
      dataIndex: 'month',
      width: 100,
      align: 'center',
    },
    {
      title: '分部',
      dataIndex: 'deptname',
      width: 100,
      align: 'center',
    },
    {
      title: '项目经理',
      dataIndex: 'fRealname',
      width: 100,
      align: 'center',
    },
    {
      title: '合同名称',
      key: 'contractName',
      dataIndex: 'name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '签署年份',
      dataIndex: 'signYear',
      width: 80,
      align: 'center',
    },
    {
      title: '收款条件',
      dataIndex: 'fktj',
      width: 150,
      ellipsis: true,
    },
    {
      title: '收款金额',
      key: 'cmMoney',
      dataIndex: 'cmMoney',
      width: 120,
      align: 'right',
    },
    {
      title: '应收日期',
      dataIndex: 'yingshouDate',
      width: 100,
      align: 'center',
    },
    {
      title: '预收日期',
      dataIndex: 'yushouDate',
      width: 100,
      align: 'center',
    },
    {
      title: '开票日期',
      dataIndex: 'kaipiaoDate',
      width: 100,
      align: 'center',
    },
    {
      title: '一部金额',
      key: 'ybAmount',
      dataIndex: 'ybAmount',
      width: 100,
      align: 'right',
    },
    {
      title: '二部金额',
      key: 'ebAmount',
      dataIndex: 'ebAmount',
      width: 100,
      align: 'right',
    },
    {
      title: '综合金额',
      key: 'otherAmount',
      dataIndex: 'otherAmount',
      width: 100,
      align: 'right',
    },
    {
      title: '合计',
      key: 'allAmount',
      dataIndex: 'allAmount',
      width: 120,
      align: 'right',
    },
    {
      title: '备注',
      key: 'note',
      dataIndex: 'note',
      width: 200,
      ellipsis: true,
    },
  ];

  // 注册表单
  const [registerSearchForm, { getFieldsValue, resetFields, setFieldsValue }] = useForm({
    labelWidth: 80,
    schemas: searchSchemas,
    autoSubmitOnEnter: true,
    submitButtonOptions: {
      text: '查询',
    },
    resetButtonOptions: {
      text: '重置',
    },
    actionColOptions: {
      span: 6,
    },
    showActionButtonGroup: true,
  });

  // 注册表格
  const [registerTable, { setTableData, getDataSource }] = useTable({
    columns,
    pagination: false,
    showIndexColumn: false,
    bordered: true,
    size: 'small',
  });

  // 格式化金额
  function formatAmount(amount: number | string) {
    const num = Number(amount) || 0;
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
    }).format(num);
  }

  // 获取支付状态颜色
  function getPayStatusColor(status: number) {
    const statusMap = {
      0: 'error',    // 未收款
      1: 'success',  // 已收款
      2: 'warning',  // 部分收款
    };
    return statusMap[status] || 'default';
  }

  // 获取支付状态文本
  function getPayStatusText(status: number) {
    const statusMap = {
      0: '未收款',
      1: '已收款', 
      2: '部分收款',
    };
    return statusMap[status] || '未知';
  }

  // 查询数据
  async function loadData() {
    try {
      loading.value = true;
      const params = getFieldsValue();
      
      // 获取明细数据
      const listData = await getInvoiceReportList(params);
      setTableData(listData || []);
      
      // 计算总金额
      totalAmount.value = (listData || []).reduce((sum, item) => sum + (Number(item.allAmount) || 0), 0);
      
      // 获取汇总数据
      const summaryData = await getInvoiceReportSummary(params);
      managerSummary.value = summaryData || [];
      
    } catch (error) {
      console.error('获取数据失败:', error);
      createMessage.error('获取数据失败');
    } finally {
      loading.value = false;
    }
  }

  // 处理查询
  function handleSearch() {
    loadData();
  }

  // 处理重置
  function handleReset() {
    resetFields();
    loadData();
  }

  // 处理导出
  async function handleExport() {
    try {
      exportLoading.value = true;
      const params = getFieldsValue();
      await exportInvoiceReport(params);
      createMessage.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      createMessage.error('导出失败');
    } finally {
      exportLoading.value = false;
    }
  }

  onMounted(() => {
    // 设置默认年份为当前年份
    setFieldsValue({
      year: currentYear
    });
    loadData();
  });
</script>

<style lang="less" scoped>
.invoice-report-container {
  .report-header {
    .report-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;

      .title-icon {
        color: #1890ff;
        font-size: 22px;
      }
    }

    .report-description {
      color: #666;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .search-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .summary-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .manager-summary {
      .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 16px;

        .summary-item {
          padding: 16px;
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
          border-radius: 8px;
          text-align: center;
          transition: transform 0.2s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }

          .manager-name {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
          }

          .manager-amount {
            font-size: 18px;
            font-weight: 600;
            color: #1890ff;
          }
        }
      }
    }
  }

  .table-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;

    .title-icon {
      color: #1890ff;
      font-size: 16px;
    }
  }

  .total-statistic {
    :deep(.ant-statistic-content) {
      color: #1890ff;
      font-weight: 600;
    }
  }

  .contract-name {
    color: #1890ff;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }

  .amount-text {
    font-weight: 500;
    color: #2c3e50;

    &.total-amount {
      color: #1890ff;
      font-weight: 600;
    }
  }

  .note-text {
    color: #666;
    font-size: 12px;
    line-height: 1.4;
  }

  :deep(.ant-table) {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      color: #2c3e50;
    }

    .ant-table-tbody > tr:hover > td {
      background: #f5f7fa;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .invoice-report-container {
    .summary-card {
      .manager-summary {
        .summary-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>
