package com.xinghuo.project.portfolio.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.portfolio.entity.PortfolioEntity;
import com.xinghuo.project.portfolio.model.PortfolioPagination;

import java.util.List;

/**
 * 项目组合服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface PortfolioService extends BaseService<PortfolioEntity> {

    /**
     * 分页查询项目组合列表
     *
     * @param pagination 查询条件
     * @return 组合列表
     */
    List<PortfolioEntity> getList(PortfolioPagination pagination);

    /**
     * 根据ID查询项目组合信息
     *
     * @param id 组合ID
     * @return 组合信息
     */
    PortfolioEntity getInfo(String id);

    /**
     * 创建项目组合
     *
     * @param entity 组合信息
     * @return 创建结果
     */
    boolean saveInfo(PortfolioEntity entity);

    /**
     * 更新项目组合
     *
     * @param entity 更新信息
     * @return 更新结果
     */
    boolean updateInfo(PortfolioEntity entity);

    /**
     * 删除项目组合
     *
     * @param id 组合ID
     * @return 删除结果
     */
    boolean deleteById(String id);

    /**
     * 根据负责人ID查询组合列表
     *
     * @param ownerId 负责人ID
     * @return 组合列表
     */
    List<PortfolioEntity> getListByOwnerId(String ownerId);

    /**
     * 根据组合类型ID查询组合列表
     *
     * @param typeId 组合类型ID
     * @return 组合列表
     */
    List<PortfolioEntity> getListByTypeId(String typeId);

    /**
     * 根据状态查询组合列表
     *
     * @param status 状态
     * @return 组合列表
     */
    List<PortfolioEntity> getListByStatus(String status);

    /**
     * 检查组合编码是否存在
     *
     * @param code 组合编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByCode(String code, String excludeId);
}
