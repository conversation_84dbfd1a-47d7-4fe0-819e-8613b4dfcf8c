package com.xinghuo.project.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.core.dao.ProjectBaseMapper;
import com.xinghuo.project.core.entity.ProjectBaseEntity;
import com.xinghuo.project.core.model.ProjectPagination;
import com.xinghuo.project.core.service.ProjectService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 项目服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class ProjectServiceImpl extends BaseServiceImpl<ProjectBaseMapper, ProjectBaseEntity> implements ProjectService {

    @Resource
    private ProjectBaseMapper projectBaseMapper;

    @Override
    public List<ProjectBaseEntity> getList(ProjectPagination pagination) {
        QueryWrapper<ProjectBaseEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectBaseEntity> lambda = queryWrapper.lambda();

        // 根据项目编码模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getCode())) {
            lambda.like(ProjectBaseEntity::getCode, pagination.getCode());
        }

        // 根据项目名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(ProjectBaseEntity::getName, pagination.getName());
        }

        // 根据项目类型精确查询
        if (StrXhUtil.isNotEmpty(pagination.getProjectType())) {
            lambda.eq(ProjectBaseEntity::getTypeId, pagination.getProjectType());
        }

        // 根据项目状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getStatus())) {
            lambda.eq(ProjectBaseEntity::getStatus, pagination.getStatus());
        }

        // 根据项目健康度精确查询
        if (StrXhUtil.isNotEmpty(pagination.getHealth())) {
            lambda.eq(ProjectBaseEntity::getHealth, pagination.getHealth());
        }

        // 根据项目经理ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getManagerId())) {
            lambda.eq(ProjectBaseEntity::getManagerId, pagination.getManagerId());
        }

        // 根据部门ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getDeptId())) {
            lambda.eq(ProjectBaseEntity::getDepartmentId, pagination.getDeptId());
        }

        // 根据优先级精确查询
        if (StrXhUtil.isNotEmpty(pagination.getPriority())) {
            lambda.eq(ProjectBaseEntity::getPriority, pagination.getPriority());
        }

        // 项目开始日期范围查询
        if (pagination.getStartDateStart() != null) {
            lambda.ge(ProjectBaseEntity::getPlannedStartDate, pagination.getStartDateStart());
        }
        if (pagination.getStartDateEnd() != null) {
            lambda.le(ProjectBaseEntity::getPlannedStartDate, pagination.getStartDateEnd());
        }

        // 项目结束日期范围查询
        if (pagination.getEndDateStart() != null) {
            lambda.ge(ProjectBaseEntity::getPlannedEndDate, pagination.getEndDateStart());
        }
        if (pagination.getEndDateEnd() != null) {
            lambda.le(ProjectBaseEntity::getPlannedEndDate, pagination.getEndDateEnd());
        }

        // 创建时间范围查询
        if (pagination.getCreateTimeStart() != null) {
            lambda.ge(ProjectBaseEntity::getCreatedAt, pagination.getCreateTimeStart());
        }
        if (pagination.getCreateTimeEnd() != null) {
            lambda.le(ProjectBaseEntity::getCreatedAt, pagination.getCreateTimeEnd());
        }

        // 是否归档查询
        if (pagination.getArchived() != null) {
            // 这里假设归档状态通过status字段的特定值来判断
            if (pagination.getArchived()) {
                lambda.eq(ProjectBaseEntity::getStatus, "archived");
            } else {
                lambda.ne(ProjectBaseEntity::getStatus, "archived");
            }
        }

        // 根据关键字搜索项目名称或编码
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(ProjectBaseEntity::getName, keyword)
                    .or()
                    .like(ProjectBaseEntity::getCode, keyword)
            );
        }

        // 排序：按创建时间倒序
        lambda.orderByDesc(ProjectBaseEntity::getCreatedAt);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public List<ProjectBaseEntity> getListByProjectType(String projectType) {
        return projectBaseMapper.selectByProjectType(projectType);
    }

    @Override
    public List<ProjectBaseEntity> getListByStatus(String status) {
        return projectBaseMapper.selectByStatus(status);
    }

    @Override
    public List<ProjectBaseEntity> getListByManagerId(String managerId) {
        return projectBaseMapper.selectByManagerId(managerId);
    }

    @Override
    public List<ProjectBaseEntity> getListByDeptId(String deptId) {
        return projectBaseMapper.selectByDeptId(deptId);
    }

    @Override
    public ProjectBaseEntity getInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("查询项目信息ID为空");
            return null;
        }
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(ProjectBaseEntity entity) {
        if (entity == null) {
            log.warn("创建项目信息为空");
            throw new RuntimeException("项目信息不能为空");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getName())) {
            log.warn("项目名称不能为空");
            throw new RuntimeException("项目名称不能为空");
        }

        // 检查项目编码是否重复
        if (StrXhUtil.isNotEmpty(entity.getCode())) {
            boolean exists = isExistByCode(entity.getCode(), null);
            if (exists) {
                log.warn("项目编码已存在: {}", entity.getCode());
                throw new RuntimeException("项目编码已存在");
            }
        } else {
            // 如果没有提供编码，自动生成
            entity.setCode(generateProjectCode());
        }

        // 设置ID
        String id = RandomUtil.snowId();
        entity.setId(id);

        // 设置默认状态
        if (StrXhUtil.isEmpty(entity.getStatus())) {
            entity.setStatus("planning"); // 默认为规划中
        }

        // 设置默认健康度
        if (StrXhUtil.isEmpty(entity.getHealth())) {
            entity.setHealth("normal"); // 默认为正常
        }

        // 设置默认优先级
        if (StrXhUtil.isEmpty(entity.getPriority())) {
            entity.setPriority("medium"); // 默认为中等优先级
        }

        this.save(entity);
        log.info("创建项目成功, ID: {}, 名称: {}", id, entity.getName());
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, ProjectBaseEntity entity) {
        if (StrXhUtil.isEmpty(id) || entity == null) {
            log.warn("更新项目参数无效, ID: {}", id);
            throw new RuntimeException("更新参数无效");
        }

        // 查询原记录是否存在
        ProjectBaseEntity dbEntity = this.getById(id);
        if (dbEntity == null) {
            log.warn("更新的项目不存在, ID: {}", id);
            throw new RuntimeException("项目不存在");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getName())) {
            log.warn("项目名称不能为空");
            throw new RuntimeException("项目名称不能为空");
        }

        // 检查项目编码是否重复（排除自身）
        if (StrXhUtil.isNotEmpty(entity.getCode())) {
            boolean exists = isExistByCode(entity.getCode(), id);
            if (exists) {
                log.warn("项目编码已存在: {}", entity.getCode());
                throw new RuntimeException("项目编码已存在");
            }
        }

        entity.setId(id);
        this.updateById(entity);
        log.info("更新项目成功, ID: {}, 名称: {}", id, entity.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("删除项目ID为空");
            throw new RuntimeException("项目ID不能为空");
        }

        // 查询项目是否存在
        ProjectBaseEntity entity = this.getById(id);
        if (entity == null) {
            log.warn("删除的项目不存在, ID: {}", id);
            throw new RuntimeException("项目不存在");
        }

        // 检查项目状态，进行中的项目不能删除
        if ("executing".equals(entity.getStatus())) {
            log.warn("进行中的项目不能删除, ID: {}", id);
            throw new RuntimeException("进行中的项目不能删除");
        }

        this.removeById(id);
        log.info("删除项目成功, ID: {}, 名称: {}", id, entity.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String id, String status) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(status)) {
            log.warn("更新项目状态参数无效, ID: {}, status: {}", id, status);
            throw new RuntimeException("参数无效");
        }

        ProjectBaseEntity entity = new ProjectBaseEntity();
        entity.setId(id);
        entity.setStatus(status);
        this.updateById(entity);
        log.info("更新项目状态成功, ID: {}, status: {}", id, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateHealth(String id, String health) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(health)) {
            log.warn("更新项目健康度参数无效, ID: {}, health: {}", id, health);
            throw new RuntimeException("参数无效");
        }

        ProjectBaseEntity entity = new ProjectBaseEntity();
        entity.setId(id);
        entity.setHealth(health);
        this.updateById(entity);
        log.info("更新项目健康度成功, ID: {}, health: {}", id, health);
    }

    @Override
    public boolean isExistByCode(String code, String excludeId) {
        if (StrXhUtil.isEmpty(code)) {
            return false;
        }
        int count = projectBaseMapper.checkCodeExists(code, excludeId);
        return count > 0;
    }

    @Override
    public List<Map<String, Object>> getProjectStatistics(Map<String, Object> params) {
        return projectBaseMapper.getProjectStatistics(params);
    }

    @Override
    public List<Map<String, Object>> getProjectHealthStatistics(Map<String, Object> params) {
        return projectBaseMapper.getProjectHealthStatistics(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void archiveProject(String id) {
        updateStatus(id, "archived");
        log.info("项目归档成功, ID: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activateProject(String id) {
        updateStatus(id, "executing");
        log.info("项目激活成功, ID: {}", id);
    }

    /**
     * 生成项目编码
     * 
     * @return 项目编码
     */
    private String generateProjectCode() {
        // 简单的编码生成规则：PROJ + 时间戳后6位 + 随机数
        String timestamp = String.valueOf(System.currentTimeMillis());
        String suffix = timestamp.substring(timestamp.length() - 6);
        String random = String.valueOf((int)(Math.random() * 1000));
        return "PROJ" + suffix + String.format("%03d", Integer.parseInt(random));
    }
}
