package com.xinghuo.project.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.entity.ProjBaseEntity;

import java.util.List;

/**
 * 项目基础信息服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ProjBaseService extends BaseService<ProjBaseEntity> {

    /**
     * 分页查询项目列表
     *
     * @param pagination 查询条件
     * @return 项目列表
     */
    List<ProjBaseEntity> getList(Object pagination);

    /**
     * 根据ID查询项目信息
     *
     * @param id 项目ID
     * @return 项目信息
     */
    ProjBaseEntity getInfo(String id);

    /**
     * 创建项目
     *
     * @param entity 项目信息
     * @return 创建结果
     */
    boolean saveInfo(ProjBaseEntity entity);

    /**
     * 更新项目
     *
     * @param entity 更新信息
     * @return 更新结果
     */
    boolean updateInfo(ProjBaseEntity entity);

    /**
     * 删除项目
     *
     * @param id 项目ID
     * @return 删除结果
     */
    boolean deleteById(String id);

    /**
     * 根据项目经理ID查询项目列表
     *
     * @param managerId 项目经理ID
     * @return 项目列表
     */
    List<ProjBaseEntity> getListByManagerId(String managerId);

    /**
     * 根据项目群ID查询项目列表
     *
     * @param programId 项目群ID
     * @return 项目列表
     */
    List<ProjBaseEntity> getListByProgramId(String programId);

    /**
     * 根据项目类型ID查询项目列表
     *
     * @param typeId 项目类型ID
     * @return 项目列表
     */
    List<ProjBaseEntity> getListByTypeId(String typeId);

    /**
     * 根据项目状态ID查询项目列表
     *
     * @param statusId 项目状态ID
     * @return 项目列表
     */
    List<ProjBaseEntity> getListByStatusId(String statusId);

    /**
     * 根据健康状态查询项目列表
     *
     * @param healthStatus 健康状态
     * @return 项目列表
     */
    List<ProjBaseEntity> getListByHealthStatus(String healthStatus);

    /**
     * 检查项目编码是否存在
     *
     * @param code 项目编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByCode(String code, String excludeId);

    /**
     * 统计项目群下的项目数量
     *
     * @param programId 项目群ID
     * @return 项目数量
     */
    int countByProgramId(String programId);
}
