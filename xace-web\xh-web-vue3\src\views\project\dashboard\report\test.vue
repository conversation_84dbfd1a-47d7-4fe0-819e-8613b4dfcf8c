<template>
  <div class="test-page">
    <PageWrapper title="报表API测试" contentBackground>
      <a-card title="API测试结果">
        <a-space direction="vertical" style="width: 100%">
          <a-button type="primary" @click="testDashboardAPI" :loading="loading">
            测试仪表板API
          </a-button>
          
          <a-button type="primary" @click="testInvoiceAPI" :loading="loading">
            测试已开票未收款API
          </a-button>
          
          <a-divider />
          
          <div v-if="result">
            <h3>测试结果：</h3>
            <pre>{{ JSON.stringify(result, null, 2) }}</pre>
          </div>
          
          <div v-if="error" style="color: red;">
            <h3>错误信息：</h3>
            <pre>{{ error }}</pre>
          </div>
        </a-space>
      </a-card>
    </PageWrapper>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { PageWrapper } from '/@/components/Page';
  import { getDashboardData } from '/@/api/project/report/dashboard';
  import { getInvoiceReportList } from '/@/api/project/report/invoice';

  defineOptions({ name: 'ReportTest' });

  const loading = ref(false);
  const result = ref(null);
  const error = ref('');

  async function testDashboardAPI() {
    try {
      loading.value = true;
      error.value = '';
      result.value = null;
      
      const data = await getDashboardData();
      result.value = data;
    } catch (err: any) {
      error.value = err.message || '请求失败';
      console.error('仪表板API测试失败:', err);
    } finally {
      loading.value = false;
    }
  }

  async function testInvoiceAPI() {
    try {
      loading.value = true;
      error.value = '';
      result.value = null;
      
      const data = await getInvoiceReportList({
        year: new Date().getFullYear(),
        dept: '',
        xmjl: ''
      });
      result.value = data;
    } catch (err: any) {
      error.value = err.message || '请求失败';
      console.error('已开票未收款API测试失败:', err);
    } finally {
      loading.value = false;
    }
  }
</script>

<style lang="less" scoped>
.test-page {
  pre {
    background: #f5f5f5;
    padding: 16px;
    border-radius: 4px;
    overflow: auto;
    max-height: 400px;
  }
}
</style>
