import { nextTick } from 'vue';
import type { Router } from 'vue-router';

/**
 * 报表页面路由守卫
 * 用于处理KeepAlive相关的路由问题
 */
export function setupReportRouteGuard(router: Router) {
  // 路由跳转前的安全检查
  router.beforeEach((to, from, next) => {
    // 只处理报表相关的路由
    if (!to.path.startsWith('/project/report')) {
      next();
      return;
    }

    // 检查是否是刷新操作导致的路由跳转
    if (to.query._t || to.query._retry) {
      // 清理临时查询参数
      const cleanQuery = { ...to.query };
      delete cleanQuery._t;
      delete cleanQuery._retry;
      
      next({
        path: to.path,
        query: cleanQuery,
        replace: true
      });
      return;
    }

    next();
  });

  // 路由跳转后的处理
  router.afterEach((to, from) => {
    // 只处理报表相关的路由
    if (!to.path.startsWith('/project/report')) {
      return;
    }

    // 延迟执行，确保DOM更新完成
    nextTick(() => {
      try {
        // 检查页面是否正常渲染
        const pageElement = document.querySelector('.report-dashboard, .report-page');
        if (!pageElement) {
          console.warn('Report page element not found, possible rendering issue');
        }
      } catch (error) {
        console.error('Route after hook error:', error);
      }
    });
  });
}

/**
 * 安全的路由跳转方法
 * 用于报表页面之间的跳转
 */
export function safeNavigate(router: Router, path: string, options: any = {}) {
  return new Promise((resolve, reject) => {
    try {
      // 添加时间戳防止缓存问题
      const query = {
        ...options.query,
        _nav: Date.now()
      };

      const navigationPromise = options.replace 
        ? router.replace({ path, query, ...options })
        : router.push({ path, query, ...options });

      navigationPromise
        .then(() => {
          // 导航成功后清理时间戳
          nextTick(() => {
            const currentRoute = router.currentRoute.value;
            if (currentRoute.query._nav) {
              const cleanQuery = { ...currentRoute.query };
              delete cleanQuery._nav;
              
              router.replace({
                path: currentRoute.path,
                query: cleanQuery
              }).catch(() => {
                // 忽略清理失败的错误
              });
            }
          });
          resolve(true);
        })
        .catch((error) => {
          console.error('Navigation failed:', error);
          
          // 如果导航失败，尝试回退到报表首页
          router.replace('/project/report/index').catch(() => {
            reject(error);
          });
        });
    } catch (error) {
      console.error('Safe navigate error:', error);
      reject(error);
    }
  });
}

/**
 * 页面刷新的安全处理
 */
export function safeRefresh(router: Router) {
  return new Promise((resolve, reject) => {
    try {
      const currentRoute = router.currentRoute.value;
      
      // 使用replace避免历史记录问题
      router.replace({
        path: currentRoute.path,
        query: {
          ...currentRoute.query,
          _refresh: Date.now()
        }
      }).then(() => {
        // 刷新成功后清理参数
        nextTick(() => {
          const newRoute = router.currentRoute.value;
          if (newRoute.query._refresh) {
            const cleanQuery = { ...newRoute.query };
            delete cleanQuery._refresh;
            
            router.replace({
              path: newRoute.path,
              query: cleanQuery
            }).catch(() => {
              // 忽略清理失败
            });
          }
        });
        resolve(true);
      }).catch((error) => {
        console.error('Safe refresh failed:', error);
        reject(error);
      });
    } catch (error) {
      console.error('Safe refresh error:', error);
      reject(error);
    }
  });
}

/**
 * 错误恢复处理
 */
export function handleRouteError(router: Router, error: Error) {
  console.error('Route error detected:', error);
  
  // 检查是否是KeepAlive相关错误
  if (error.message?.includes('Cannot destructure property') && 
      error.message?.includes('vnode')) {
    
    console.warn('Handling KeepAlive route error...');
    
    // 延迟处理，避免在Vue更新周期中操作
    setTimeout(() => {
      try {
        const currentPath = router.currentRoute.value.path;
        
        // 如果当前在报表页面，尝试重新导航
        if (currentPath.startsWith('/project/report')) {
          router.replace({
            path: currentPath,
            query: { _recover: Date.now() }
          }).catch(() => {
            // 如果重新导航失败，回到报表首页
            router.replace('/project/report/index');
          });
        }
      } catch (recoveryError) {
        console.error('Route error recovery failed:', recoveryError);
        // 最后的兜底方案：刷新页面
        window.location.reload();
      }
    }, 100);
  }
}
