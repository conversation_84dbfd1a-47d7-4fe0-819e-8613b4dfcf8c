package com.xinghuo.project.execution.entity.task;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目里程碑实体类
 */
@Data
@EqualsAndHashCode
@TableName("zz_project_milestone")
public class MilestoneEntity {

    /**
     * 主键
     */
    @TableId("f_id")
    private String id;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 里程碑ID
     */
    @TableField("milestone_id")
    private String milestoneId;

    /**
     * 里程碑名称
     */
    @TableField("milestone_name")
    private String milestoneName;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 排序
     */
    @TableField("list_order")
    private Integer listOrder;

    /**
     * 是否必填
     */
    @TableField("is_required")
    private Boolean isRequired;

    /**
     * 计划完成日期
     */
    @TableField("plan_finish_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planFinishDate;

    /**
     * 实际完成日期
     */
    @TableField("actual_finish_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualFinishDate;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建人员
     */
    @TableField("f_created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("f_created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 最后修改人员
     */
    @TableField("f_last_updated_by")
    private String lastUpdatedBy;

    /**
     * 最后修改时间
     */
    @TableField("f_last_updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdatedAt;

    /**
     * 租户ID
     */
    @TableField("f_tenantid")
    private String tenantId;
}
