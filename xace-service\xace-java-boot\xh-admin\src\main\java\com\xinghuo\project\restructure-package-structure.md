# 项目管理模块重构后包结构示意图

```
com.xinghuo.project
├── core                             # 核心领域
│   ├── controller                   # 核心控制器
│   │   └── ProjectController.java   # 项目控制器
│   ├── service                      # 核心服务接口
│   │   └── ProjectService.java      # 项目服务接口
│   ├── service.impl                 # 核心服务实现
│   │   └── ProjectServiceImpl.java  # 项目服务实现
│   ├── dao                          # 核心数据访问
│   │   └── ProjectMapper.java       # 项目数据访问接口
│   ├── entity                       # 核心实体
│   │   ├── ProjectEntity.java       # 项目实体
│   │   ├── TagEntity.java           # 标签实体
│   │   └── ProjectTagRelEntity.java # 项目标签关联实体
│   ├── dto                          # 核心数据传输对象
│   │   ├── ProjectCreateDTO.java    # 创建项目DTO
│   │   └── ProjectUpdateDTO.java    # 更新项目DTO
│   ├── vo                           # 核心视图对象
│   │   ├── ProjectVO.java           # 项目视图对象
│   │   └── ProjectListVO.java       # 项目列表视图对象
│   ├── enums                        # 核心枚举
│   │   ├── ProjectTypeEnum.java     # 项目类型枚举
│   │   ├── ProjectStatusEnum.java   # 项目状态枚举
│   │   └── ProjectHealthEnum.java   # 项目健康状态枚举
│   └── constants                    # 核心常量
│       └── ProjectConstants.java    # 项目相关常量
├── portfolio                        # 组合领域
│   ├── controller                   # 组合控制器
│   │   ├── PortfolioController.java # 组合控制器
│   │   └── ProgramController.java   # 项目群控制器
│   ├── service                      # 组合服务接口
│   ├── service.impl                 # 组合服务实现
│   ├── dao                          # 组合数据访问
│   ├── entity                       # 组合实体
│   │   ├── PortfolioEntity.java     # 组合实体
│   │   ├── ProgramEntity.java       # 项目群实体
│   │   ├── PortfolioProgramRelEntity.java # 组合-项目群关联
│   │   └── PortfolioProjectRelEntity.java # 组合-项目关联
│   ├── dto                          # 组合数据传输对象
│   ├── vo                           # 组合视图对象
│   └── enums                        # 组合枚举
├── execution                        # 执行领域
│   ├── controller                   # 执行控制器
│   │   ├── TaskController.java      # 任务控制器
│   │   └── MilestoneController.java # 里程碑控制器
│   ├── service                      # 执行服务接口
│   ├── service.impl                 # 执行服务实现
│   ├── dao                          # 执行数据访问
│   ├── entity                       # 执行实体
│   │   ├── task                     # 任务相关实体
│   │   │   ├── TaskEntity.java      # 任务实体
│   │   │   ├── MilestoneEntity.java # 里程碑实体
│   │   │   └── DevelopmentProjectDetailEntity.java # 研发项目详情实体
│   │   └── log                      # 日志相关实体
│   │       ├── ProgressLogEntity.java # 进度日志实体
│   │       └── WeeklyReportEntity.java # 周报实体
│   ├── dto                          # 执行数据传输对象
│   ├── vo                           # 执行视图对象
│   └── enums                        # 执行枚举
├── resource                         # 资源领域
│   ├── controller                   # 资源控制器
│   ├── service                      # 资源服务接口
│   ├── service.impl                 # 资源服务实现
│   ├── dao                          # 资源数据访问
│   ├── entity                       # 资源实体
│   │   ├── manhour                  # 工时相关实体
│   │   │   └── ManhourLogEntity.java # 工时日志实体
│   │   └── member                   # 成员相关实体
│   │       └── ProjectMemberEntity.java # 项目成员实体
│   ├── dto                          # 资源数据传输对象
│   ├── vo                           # 资源视图对象
│   └── enums                        # 资源枚举
├── biz                              # 业务领域
│   ├── controller                   # 业务控制器
│   │   ├── ContractController.java  # 合同控制器
│   │   └── CustomerController.java  # 客户控制器
│   ├── service                      # 业务服务接口
│   ├── service.impl                 # 业务服务实现
│   ├── dao                          # 业务数据访问
│   ├── entity                       # 业务实体
│   │   ├── contract                 # 合同相关实体
│   │   │   └── ContractEntity.java  # 合同实体
│   │   ├── customer                 # 客户相关实体
│   │   │   ├── CustomerEntity.java  # 客户实体
│   │   │   └── CustomerLinkmanEntity.java # 客户联系人实体
│   │   └── supplier                 # 供应商相关实体
│   │       └── SupplierEntity.java  # 供应商实体
│   ├── dto                          # 业务数据传输对象
│   ├── vo                           # 业务视图对象
│   └── enums                        # 业务枚举
└── template                         # 模板领域
    ├── controller                   # 模板控制器
    ├── service                      # 模板服务接口
    ├── service.impl                 # 模板服务实现
    ├── dao                          # 模板数据访问
    ├── entity                       # 模板实体
    ├── dto                          # 模板数据传输对象
    ├── vo                           # 模板视图对象
    └── enums                        # 模板枚举
```

## 数据库表与实体类映射关系

| 数据库表名 | 实体类 | 包路径 |
|------------|--------|--------|
| zz_proj_base | ProjectEntity | com.xinghuo.project.core.entity |
| zz_proj_tag | TagEntity | com.xinghuo.project.core.entity |
| zz_proj_tag_rel | ProjectTagRelEntity | com.xinghuo.project.core.entity |
| zz_proj_portfolio | PortfolioEntity | com.xinghuo.project.portfolio.entity |
| zz_proj_program | ProgramEntity | com.xinghuo.project.portfolio.entity |
| zz_proj_portfolio_program_rel | PortfolioProgramRelEntity | com.xinghuo.project.portfolio.entity |
| zz_proj_portfolio_project_rel | PortfolioProjectRelEntity | com.xinghuo.project.portfolio.entity |
| zz_project_task | TaskEntity | com.xinghuo.project.execution.entity.task |
| zz_project_milestone | MilestoneEntity | com.xinghuo.project.execution.entity.task |
| zz_dev_project | DevelopmentProjectDetailEntity | com.xinghuo.project.execution.entity.task |
| zz_proj_business_datalog | ProgressLogEntity | com.xinghuo.project.execution.entity.log |
| zz_proj_business_weeklog | WeeklyReportEntity | com.xinghuo.project.execution.entity.log |
| zz_proj_manhour | ManhourLogEntity | com.xinghuo.project.resource.entity.manhour |
| zz_proj_contract_member | ProjectMemberEntity | com.xinghuo.project.resource.entity.member |
| zz_proj_contract | ContractEntity | com.xinghuo.project.biz.entity.contract |
| zz_proj_customer | CustomerEntity | com.xinghuo.project.biz.entity.customer |
| zz_proj_customer_linkman | CustomerLinkmanEntity | com.xinghuo.project.biz.entity.customer |
| zz_proj_supplier | SupplierEntity | com.xinghuo.project.biz.entity.supplier |

## 注意事项

1. 实体类名称已规范化，但数据库表名保持不变
2. 使用@TableName注解明确指定表名
3. 所有实体类按照业务含义归类到相应的包中
4. 领域内部可能进一步划分子包，以提高代码组织清晰度
5. 为保证实体类之间的依赖清晰，避免循环依赖，实体类应只依赖同领域或核心领域的其他实体
