package com.xinghuo.project.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目客户联系人实体类
 * 对应数据库表：zz_proj_customer_linkman
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode
@TableName("zz_proj_customer_linkman")
public class CustomerContactEntity {

    /**
     * 主键ID
     */
    @TableId("id")
    private String id;

    /**
     * 客户ID
     */
    @TableField("cu_id")
    private String cuId;

    /**
     * 联系人
     */
    @TableField("linkman")
    private String linkman;

    /**
     * 联系电话
     */
    @TableField("telephone")
    private String telephone;

    /**
     * 状态 1-有效，0-无效
     */
    @TableField("status")
    private Integer status;

    /**
     * 主题
     */
    @TableField("topic")
    private String topic;

    /**
     * 内容
     */
    @TableField("content")
    private String content;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 创建用户
     */
    @TableField("creator_user_id")
    private String creatorUserId;

    /**
     * 创建时间
     */
    @TableField("creator_time")
    private Date creatorTime;

    /**
     * 最后修改人
     */
    @TableField("last_modify_user_id")
    private String lastModifyUserId;

    /**
     * 修改时间
     */
    @TableField("last_modify_time")
    private Date lastModifyTime;

    /**
     * 租户ID
     */
    @TableField("f_tenantid")
    private String tenantId;

    /**
     * 流程ID
     */
    @TableField("f_flowid")
    private String flowId;
}
