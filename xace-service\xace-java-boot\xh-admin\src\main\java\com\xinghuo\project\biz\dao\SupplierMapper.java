package com.xinghuo.project.biz.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.biz.entity.SupplierEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 供应商Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface SupplierMapper extends XHBaseMapper<SupplierEntity> {

    /**
     * 检查供应商名称是否存在
     *
     * @param name 供应商名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkNameExists(@Param("name") String name, @Param("excludeId") String excludeId);
}
