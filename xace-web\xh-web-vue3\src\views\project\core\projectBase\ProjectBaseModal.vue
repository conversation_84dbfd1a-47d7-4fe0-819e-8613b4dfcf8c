<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from './projectBase.data';
  import { createProject, updateProject, checkProjectCodeExists } from '/@/api/project/projectBase';
  import { useMessage } from '/@/hooks/web/useMessage';

  defineOptions({ name: 'ProjectBaseModal' });

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  const isUpdate = ref(true);
  const isView = ref(false);
  const rowId = ref('');

  const [registerForm, { setFieldsValue, updateSchema, resetFields, validate }] = useForm({
    labelWidth: 100,
    baseColProps: { span: 24 },
    schemas: formSchema,
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;
    isView.value = !!data?.isView;

    if (unref(isUpdate)) {
      rowId.value = data.record.id;
      setFieldsValue({
        ...data.record,
      });
    }

    // 设置表单只读状态
    const schemas = formSchema.map((schema) => ({
      ...schema,
      componentProps: {
        ...schema.componentProps,
        disabled: unref(isView),
      },
    }));
    updateSchema(schemas);

    // 添加编码重复验证
    updateSchema([
      {
        field: 'code',
        componentProps: {
          disabled: unref(isView),
        },
        rules: [
          {
            validator: async (_, value) => {
              if (!value) return Promise.resolve();
              const exists = await checkProjectCodeExists(value, unref(isUpdate) ? rowId.value : undefined);
              if (exists) {
                return Promise.reject('项目编码已存在');
              }
              return Promise.resolve();
            },
            trigger: 'blur',
          },
        ],
      },
    ]);
  });

  const getTitle = computed(() => {
    if (unref(isView)) return '查看项目';
    return !unref(isUpdate) ? '新增项目' : '编辑项目';
  });

  async function handleSubmit() {
    if (unref(isView)) {
      closeModal();
      return;
    }

    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      if (unref(isUpdate)) {
        await updateProject(rowId.value, values);
        createMessage.success('更新成功');
      } else {
        await createProject(values);
        createMessage.success('创建成功');
      }

      closeModal();
      emit('success');
    } catch (error) {
      createMessage.error(unref(isUpdate) ? '更新失败' : '创建失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
