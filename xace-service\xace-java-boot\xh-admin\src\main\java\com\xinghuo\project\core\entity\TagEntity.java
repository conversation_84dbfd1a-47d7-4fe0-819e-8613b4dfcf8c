package com.xinghuo.project.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标签实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_tags")
public class TagEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 标签名称
     */
    @TableField("tag_name")
    private String tagName;

    /**
     * 标签颜色 (例如: "#FF0000" 代表红色)
     */
    @TableField("tag_color")
    private String tagColor;

    /**
     * 标签描述
     */
    @TableField("description")
    private String description;

    /**
     * 标签范围 ('global': 所有用户可见, 'user_specific': 特定用户或组可见)
     */
    @TableField("scope")
    private String scope;
}
