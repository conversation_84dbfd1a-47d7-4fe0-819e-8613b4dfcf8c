import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetList = '/api/project/template/phaseTemplate/getList',
  GetListByStatus = '/api/project/template/phaseTemplate/getListByStatus',
  GetInfo = '/api/project/template/phaseTemplate/getInfo',
  GetByCode = '/api/project/template/phaseTemplate/getByCode',
  Create = '/api/project/template/phaseTemplate/create',
  Update = '/api/project/template/phaseTemplate/update',
  Delete = '/api/project/template/phaseTemplate/delete',
  BatchDelete = '/api/project/template/phaseTemplate/batchDelete',
  UpdateStatus = '/api/project/template/phaseTemplate/updateStatus',
  BatchUpdateStatus = '/api/project/template/phaseTemplate/batchUpdateStatus',
  Enable = '/api/project/template/phaseTemplate/enable',
  Disable = '/api/project/template/phaseTemplate/disable',
  Copy = '/api/project/template/phaseTemplate/copy',
  CheckCodeExists = '/api/project/template/phaseTemplate/checkCodeExists',
  GetSelectList = '/api/project/template/phaseTemplate/getSelectList',
  GenerateCode = '/api/project/template/phaseTemplate/generateCode',
}

/**
 * 阶段模板接口
 */

// 获取阶段模板列表
export function getPhaseTemplateList(params: any) {
  return defHttp.post({
    url: Api.GetList,
    data: params,
  });
}

// 根据状态获取阶段模板列表
export function getPhaseTemplateListByStatus(status: number) {
  return defHttp.get({
    url: `${Api.GetListByStatus}/${status}`,
  });
}

// 获取阶段模板详情
export function getPhaseTemplateInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetInfo}/${id}`,
  });
}

// 根据编码获取阶段模板
export function getPhaseTemplateByCode(code: string) {
  return defHttp.get({
    url: `${Api.GetByCode}/${code}`,
  });
}

// 创建阶段模板
export function createPhaseTemplate(params: any) {
  return defHttp.post({
    url: Api.Create,
    data: params,
  });
}

// 更新阶段模板
export function updatePhaseTemplate(id: string, params: any) {
  return defHttp.put({
    url: `${Api.Update}/${id}`,
    data: params,
  });
}

// 删除阶段模板
export function deletePhaseTemplate(id: string) {
  return defHttp.delete({
    url: `${Api.Delete}/${id}`,
  });
}

// 批量删除阶段模板
export function batchDeletePhaseTemplate(ids: string[]) {
  return defHttp.delete({
    url: Api.BatchDelete,
    data: ids,
  });
}

// 更新阶段模板状态
export function updatePhaseTemplateStatus(id: string, status: number) {
  return defHttp.put({
    url: `${Api.UpdateStatus}/${id}`,
    params: { status },
  });
}

// 批量更新状态
export function batchUpdatePhaseTemplateStatus(ids: string[], status: number) {
  return defHttp.put({
    url: Api.BatchUpdateStatus,
    data: ids,
    params: { status },
  });
}

// 启用阶段模板
export function enablePhaseTemplate(id: string) {
  return defHttp.put({
    url: `${Api.Enable}/${id}`,
  });
}

// 禁用阶段模板
export function disablePhaseTemplate(id: string) {
  return defHttp.put({
    url: `${Api.Disable}/${id}`,
  });
}

// 复制阶段模板
export function copyPhaseTemplate(id: string, newName: string) {
  return defHttp.post({
    url: `${Api.Copy}/${id}`,
    params: { newName },
  });
}

// 检查阶段编码是否存在
export function checkPhaseTemplateCodeExists(code: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckCodeExists,
    params: { code, excludeId },
  });
}

// 获取阶段模板选择列表
export function getPhaseTemplateSelectList(keyword?: string) {
  return defHttp.get({
    url: Api.GetSelectList,
    params: { keyword },
  });
}

// 生成阶段编码
export function generatePhaseTemplateCode() {
  return defHttp.get({
    url: Api.GenerateCode,
  });
}
