package com.xinghuo.project.model.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目基础信息视图对象
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@Schema(description = "项目基础信息视图对象")
public class ProjBaseVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 项目编码
     */
    @Schema(description = "项目编码")
    private String code;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String name;

    /**
     * 项目类型ID
     */
    @Schema(description = "项目类型ID")
    private String typeId;

    /**
     * 项目类型名称
     */
    @Schema(description = "项目类型名称")
    private String typeName;

    /**
     * 项目经理ID
     */
    @Schema(description = "项目经理ID")
    private String managerId;

    /**
     * 项目经理姓名
     */
    @Schema(description = "项目经理姓名")
    private String managerName;

    /**
     * 所属项目群ID
     */
    @Schema(description = "所属项目群ID")
    private String programId;

    /**
     * 所属项目群名称
     */
    @Schema(description = "所属项目群名称")
    private String programName;

    /**
     * 项目状态ID
     */
    @Schema(description = "项目状态ID")
    private String statusId;

    /**
     * 项目状态名称
     */
    @Schema(description = "项目状态名称")
    private String statusName;

    /**
     * 项目健康状态
     */
    @Schema(description = "项目健康状态")
    private String healthStatus;

    /**
     * 项目健康状态名称
     */
    @Schema(description = "项目健康状态名称")
    private String healthStatusName;

    /**
     * 项目总体进度%
     */
    @Schema(description = "项目总体进度%")
    private Integer progress;

    /**
     * 计划开始日期
     */
    @Schema(description = "计划开始日期")
    private Date planStartDate;

    /**
     * 计划结束日期
     */
    @Schema(description = "计划结束日期")
    private Date planEndDate;

    /**
     * 实际开始日期
     */
    @Schema(description = "实际开始日期")
    private Date actualStartDate;

    /**
     * 实际结束日期
     */
    @Schema(description = "实际结束日期")
    private Date actualEndDate;

    /**
     * 项目描述
     */
    @Schema(description = "项目描述")
    private String description;

    /**
     * 预算-人力成本
     */
    @Schema(description = "预算-人力成本")
    private BigDecimal budgetCostLabor;

    /**
     * 预算-采购成本
     */
    @Schema(description = "预算-采购成本")
    private BigDecimal budgetCostPurchase;

    /**
     * 预算-差旅成本
     */
    @Schema(description = "预算-差旅成本")
    private BigDecimal budgetCostTravel;

    /**
     * 预算总成本
     */
    @Schema(description = "预算总成本")
    private BigDecimal budgetCostTotal;

    /**
     * 实际-人力成本
     */
    @Schema(description = "实际-人力成本")
    private BigDecimal actualCostLabor;

    /**
     * 实际-采购成本
     */
    @Schema(description = "实际-采购成本")
    private BigDecimal actualCostPurchase;

    /**
     * 实际-差旅成本
     */
    @Schema(description = "实际-差旅成本")
    private BigDecimal actualCostTravel;

    /**
     * 实际总成本
     */
    @Schema(description = "实际总成本")
    private BigDecimal actualCostTotal;

    /**
     * 成本差异
     */
    @Schema(description = "成本差异")
    private BigDecimal costVariance;

    /**
     * 成本差异率%
     */
    @Schema(description = "成本差异率%")
    private BigDecimal costVarianceRate;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createdAt;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    private String createdBy;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名")
    private String createdByName;

    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间")
    private Date lastUpdatedAt;

    /**
     * 最后更新人ID
     */
    @Schema(description = "最后更新人ID")
    private String lastUpdatedBy;

    /**
     * 最后更新人姓名
     */
    @Schema(description = "最后更新人姓名")
    private String lastUpdatedByName;
}
