package com.xinghuo.project.core.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.core.entity.TagEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标签数据访问接口
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface TagMapper extends XHBaseMapper<TagEntity> {

    /**
     * 检查标签名称是否存在
     *
     * @param tagName 标签名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkTagNameExists(@Param("tagName") String tagName, @Param("excludeId") String excludeId);

    /**
     * 获取标签选择列表
     *
     * @param keyword 关键字
     * @return 标签列表
     */
    List<TagEntity> selectForSelect(@Param("keyword") String keyword);

    /**
     * 根据范围查询标签列表
     *
     * @param scope 标签范围
     * @return 标签列表
     */
    List<TagEntity> selectByScope(@Param("scope") String scope);
}
