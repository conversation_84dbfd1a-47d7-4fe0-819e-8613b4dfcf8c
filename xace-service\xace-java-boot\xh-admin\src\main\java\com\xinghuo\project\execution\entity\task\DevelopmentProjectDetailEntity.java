package com.xinghuo.project.execution.entity.task;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 研发项目管理实体类
 */
@Data
@EqualsAndHashCode
@TableName("zz_dev_project")
public class DevelopmentProjectDetailEntity {

    /**
     * 主键ID
     */
    @TableId("id")
    private String id;

    /**
     * 研发项目类型
     */
    @TableField("project_type")
    private String projectType;

    /**
     * 项目名称
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 项目KEY
     */
    @TableField("project_no")
    private String projectNo;

    /**
     * 项目紧急程度
     */
    @TableField("level")
    private String level;

    /**
     * 所属分部
     */
    @TableField("fb_id")
    private String fbId;

    /**
     * 项目种类
     */
    @TableField("species")
    private String species;

    /**
     * 项目状态
     */
    @TableField("status")
    private String status;

    /**
     * 项目经理
     */
    @TableField("leader")
    private String leader;

    /**
     * 需求分析师
     */
    @TableField("analyst")
    private String analyst;

    /**
     * 启动日期
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 计划完成时间
     */
    @TableField("plan_completion_date")
    private Date planCompletionDate;

    /**
     * 项目备注
     */
    @TableField("note")
    private String note;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 失效日期
     */
    @TableField("expiration_date")
    private Date expirationDate;

    /**
     * 技术经理
     */
    @TableField("develop_leader")
    private String developLeader;

    /**
     * 测试经理
     */
    @TableField("test_leader")
    private String testLeader;

    /**
     * 部署经理
     */
    @TableField("deploy_leader")
    private String deployLeader;

    /**
     * 类型
     */
    @TableField("type")
    private String type;

    /**
     * 汇报频率
     */
    @TableField("reportfrequency")
    private String reportFrequency;

    /**
     * 流程类型
     */
    @TableField("flow_type")
    private String flowType;

    /**
     * 工时填写状态 1-可填写 0-已结束
     */
    @TableField("work_status")
    private Integer workStatus;

    /**
     * 累计工时(自动计算)
     */
    @TableField("auto_manhours")
    private BigDecimal autoManhours;

    /**
     * 启用标识
     */
    @TableField("enable_mark")
    private Boolean enableMark;

    /**
     * 创建用户
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最后修改人
     */
    @TableField("last_modified_user_id")
    private String lastModifiedUserId;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 租户ID
     */
    @TableField("f_tenantid")
    private String tenantId;

    /**
     * 流程ID
     */
    @TableField("f_flowid")
    private String flowId;

    /**
     * 删除标志
     */
    @TableField("f_deletemark")
    private Integer deleteMark;
}
