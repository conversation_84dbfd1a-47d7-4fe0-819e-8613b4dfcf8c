package com.xinghuo.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.dao.ProjPortfolioMapper;
import com.xinghuo.project.entity.ProjPortfolioEntity;
import com.xinghuo.project.service.ProjPortfolioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 项目组合服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class ProjPortfolioServiceImpl extends BaseServiceImpl<ProjPortfolioMapper, ProjPortfolioEntity> implements ProjPortfolioService {

    @Resource
    private ProjPortfolioMapper projPortfolioMapper;

    @Resource
    private UserProvider userProvider;

    @Override
    public List<ProjPortfolioEntity> getList(Object pagination) {
        // 这里需要根据具体的分页对象类型来实现
        // 暂时返回所有记录，后续可以根据实际需求调整
        QueryWrapper<ProjPortfolioEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("f_created_at");
        return this.list(queryWrapper);
    }

    @Override
    public ProjPortfolioEntity getInfo(String id) {
        if (StrXhUtil.isBlank(id)) {
            log.warn("查询项目组合信息ID为空");
            return null;
        }
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveInfo(ProjPortfolioEntity entity) {
        if (entity == null) {
            log.warn("保存项目组合信息为空");
            return false;
        }

        try {
            log.info("开始创建项目组合: {}", entity.getName());

            // 检查组合编码是否重复
            if (StrXhUtil.isNotBlank(entity.getCode()) && isExistByCode(entity.getCode(), null)) {
                log.warn("组合编码已存在: {}", entity.getCode());
                throw new RuntimeException("组合编码已存在");
            }

            // 设置默认值
            entity.setId(RandomUtil.snowId());
            if (StrXhUtil.isBlank(entity.getStatus())) {
                entity.setStatus("active");
            }

            boolean result = this.save(entity);
            if (result) {
                log.info("创建项目组合成功, ID: {}", entity.getId());
            }
            return result;

        } catch (RuntimeException e) {
            log.warn("创建项目组合失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建项目组合异常", e);
            throw new RuntimeException("创建项目组合失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInfo(ProjPortfolioEntity entity) {
        if (entity == null || StrXhUtil.isBlank(entity.getId())) {
            log.warn("更新项目组合信息ID为空");
            return false;
        }

        try {
            log.info("开始更新项目组合: {}", entity.getName());

            // 查询原记录是否存在
            ProjPortfolioEntity dbEntity = this.getById(entity.getId());
            if (dbEntity == null) {
                log.warn("更新的项目组合不存在, ID: {}", entity.getId());
                return false;
            }

            // 检查组合编码是否重复
            if (StrXhUtil.isNotBlank(entity.getCode()) && isExistByCode(entity.getCode(), entity.getId())) {
                log.warn("组合编码已存在: {}", entity.getCode());
                throw new RuntimeException("组合编码已存在");
            }

            boolean result = this.updateById(entity);
            if (result) {
                log.info("更新项目组合成功, ID: {}", entity.getId());
            }
            return result;

        } catch (RuntimeException e) {
            log.warn("更新项目组合失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("更新项目组合异常", e);
            throw new RuntimeException("更新项目组合失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(String id) {
        if (StrXhUtil.isBlank(id)) {
            log.warn("删除项目组合ID为空");
            return false;
        }

        try {
            log.info("开始删除项目组合, ID: {}", id);

            // 检查组合是否存在
            ProjPortfolioEntity entity = this.getById(id);
            if (entity == null) {
                log.warn("删除的项目组合不存在, ID: {}", id);
                return false;
            }

            boolean result = this.removeById(id);
            if (result) {
                log.info("删除项目组合成功, ID: {}", id);
            }
            return result;

        } catch (Exception e) {
            log.error("删除项目组合异常, ID: {}", id, e);
            throw new RuntimeException("删除项目组合失败");
        }
    }

    @Override
    public List<ProjPortfolioEntity> getListByOwnerId(String ownerId) {
        if (StrXhUtil.isBlank(ownerId)) {
            return null;
        }
        return projPortfolioMapper.selectByOwnerId(ownerId);
    }

    @Override
    public List<ProjPortfolioEntity> getListByTypeId(String typeId) {
        if (StrXhUtil.isBlank(typeId)) {
            return null;
        }
        return projPortfolioMapper.selectByTypeId(typeId);
    }

    @Override
    public List<ProjPortfolioEntity> getListByStatus(String status) {
        if (StrXhUtil.isBlank(status)) {
            return null;
        }
        return projPortfolioMapper.selectByStatus(status);
    }

    @Override
    public boolean isExistByCode(String code, String excludeId) {
        if (StrXhUtil.isBlank(code)) {
            return false;
        }
        int count = projPortfolioMapper.checkCodeExists(code, excludeId);
        return count > 0;
    }
}
