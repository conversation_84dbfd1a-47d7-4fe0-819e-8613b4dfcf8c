package com.xinghuo.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.dao.ProjBaseMapper;
import com.xinghuo.project.entity.ProjBaseEntity;
import com.xinghuo.project.service.ProjBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 项目基础信息服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class ProjBaseServiceImpl extends BaseServiceImpl<ProjBaseMapper, ProjBaseEntity> implements ProjBaseService {

    @Resource
    private ProjBaseMapper projBaseMapper;

    @Resource
    private UserProvider userProvider;

    @Override
    public List<ProjBaseEntity> getList(Object pagination) {
        // 这里需要根据具体的分页对象类型来实现
        // 暂时返回所有记录，后续可以根据实际需求调整
        QueryWrapper<ProjBaseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("f_created_at");
        return this.list(queryWrapper);
    }

    @Override
    public ProjBaseEntity getInfo(String id) {
        if (StrXhUtil.isBlank(id)) {
            log.warn("查询项目信息ID为空");
            return null;
        }
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveInfo(ProjBaseEntity entity) {
        if (entity == null) {
            log.warn("保存项目信息为空");
            return false;
        }

        try {
            log.info("开始创建项目: {}", entity.getName());

            // 检查项目编码是否重复
            if (StrXhUtil.isNotBlank(entity.getCode()) && isExistByCode(entity.getCode(), null)) {
                log.warn("项目编码已存在: {}", entity.getCode());
                throw new RuntimeException("项目编码已存在");
            }

            // 设置默认值
            entity.setId(RandomUtil.snowId());
            if (StrXhUtil.isBlank(entity.getHealthStatus())) {
                entity.setHealthStatus("green");
            }
            if (entity.getProgress() == null) {
                entity.setProgress(0);
            }
            // 初始化成本字段为0
            if (entity.getBudgetCostLabor() == null) {
                entity.setBudgetCostLabor(BigDecimal.ZERO);
            }
            if (entity.getBudgetCostPurchase() == null) {
                entity.setBudgetCostPurchase(BigDecimal.ZERO);
            }
            if (entity.getBudgetCostTravel() == null) {
                entity.setBudgetCostTravel(BigDecimal.ZERO);
            }
            if (entity.getActualCostLabor() == null) {
                entity.setActualCostLabor(BigDecimal.ZERO);
            }
            if (entity.getActualCostPurchase() == null) {
                entity.setActualCostPurchase(BigDecimal.ZERO);
            }
            if (entity.getActualCostTravel() == null) {
                entity.setActualCostTravel(BigDecimal.ZERO);
            }

            boolean result = this.save(entity);
            if (result) {
                log.info("创建项目成功, ID: {}", entity.getId());
            }
            return result;

        } catch (RuntimeException e) {
            log.warn("创建项目失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建项目异常", e);
            throw new RuntimeException("创建项目失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInfo(ProjBaseEntity entity) {
        if (entity == null || StrXhUtil.isBlank(entity.getId())) {
            log.warn("更新项目信息ID为空");
            return false;
        }

        try {
            log.info("开始更新项目: {}", entity.getName());

            // 查询原记录是否存在
            ProjBaseEntity dbEntity = this.getById(entity.getId());
            if (dbEntity == null) {
                log.warn("更新的项目不存在, ID: {}", entity.getId());
                return false;
            }

            // 检查项目编码是否重复
            if (StrXhUtil.isNotBlank(entity.getCode()) && isExistByCode(entity.getCode(), entity.getId())) {
                log.warn("项目编码已存在: {}", entity.getCode());
                throw new RuntimeException("项目编码已存在");
            }

            // 进度范围校验
            if (entity.getProgress() != null && (entity.getProgress() < 0 || entity.getProgress() > 100)) {
                log.warn("项目进度必须在0-100之间: {}", entity.getProgress());
                throw new RuntimeException("项目进度必须在0-100之间");
            }

            boolean result = this.updateById(entity);
            if (result) {
                log.info("更新项目成功, ID: {}", entity.getId());
            }
            return result;

        } catch (RuntimeException e) {
            log.warn("更新项目失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("更新项目异常", e);
            throw new RuntimeException("更新项目失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(String id) {
        if (StrXhUtil.isBlank(id)) {
            log.warn("删除项目ID为空");
            return false;
        }

        try {
            log.info("开始删除项目, ID: {}", id);

            // 检查项目是否存在
            ProjBaseEntity entity = this.getById(id);
            if (entity == null) {
                log.warn("删除的项目不存在, ID: {}", id);
                return false;
            }

            boolean result = this.removeById(id);
            if (result) {
                log.info("删除项目成功, ID: {}", id);
            }
            return result;

        } catch (Exception e) {
            log.error("删除项目异常, ID: {}", id, e);
            throw new RuntimeException("删除项目失败");
        }
    }

    @Override
    public List<ProjBaseEntity> getListByManagerId(String managerId) {
        if (StrXhUtil.isBlank(managerId)) {
            return null;
        }
        return projBaseMapper.selectByManagerId(managerId);
    }

    @Override
    public List<ProjBaseEntity> getListByProgramId(String programId) {
        if (StrXhUtil.isBlank(programId)) {
            return null;
        }
        return projBaseMapper.selectByProgramId(programId);
    }

    @Override
    public List<ProjBaseEntity> getListByTypeId(String typeId) {
        if (StrXhUtil.isBlank(typeId)) {
            return null;
        }
        return projBaseMapper.selectByTypeId(typeId);
    }

    @Override
    public List<ProjBaseEntity> getListByStatusId(String statusId) {
        if (StrXhUtil.isBlank(statusId)) {
            return null;
        }
        return projBaseMapper.selectByStatusId(statusId);
    }

    @Override
    public List<ProjBaseEntity> getListByHealthStatus(String healthStatus) {
        if (StrXhUtil.isBlank(healthStatus)) {
            return null;
        }
        return projBaseMapper.selectByHealthStatus(healthStatus);
    }

    @Override
    public boolean isExistByCode(String code, String excludeId) {
        if (StrXhUtil.isBlank(code)) {
            return false;
        }
        int count = projBaseMapper.checkCodeExists(code, excludeId);
        return count > 0;
    }

    @Override
    public int countByProgramId(String programId) {
        if (StrXhUtil.isBlank(programId)) {
            return 0;
        }
        return projBaseMapper.countByProgramId(programId);
    }
}
