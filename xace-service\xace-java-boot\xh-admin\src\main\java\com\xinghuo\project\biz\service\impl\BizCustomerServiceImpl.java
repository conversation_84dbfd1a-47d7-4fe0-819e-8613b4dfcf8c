package com.xinghuo.project.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.biz.dao.BizCustomerMapper;
import com.xinghuo.project.biz.entity.BizCustomerEntity;
import com.xinghuo.project.biz.model.CustomerPagination;
import com.xinghuo.project.biz.service.BizCustomerService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 客户服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class BizCustomerServiceImpl extends BaseServiceImpl<BizCustomerMapper, BizCustomerEntity> implements BizCustomerService {

    @Resource
    private BizCustomerMapper bizCustomerMapper;

    @Override
    public List<BizCustomerEntity> getList(CustomerPagination pagination) {
        QueryWrapper<BizCustomerEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<BizCustomerEntity> lambda = queryWrapper.lambda();

        // 根据客户名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(BizCustomerEntity::getName, pagination.getName());
        }

        // 根据客户类型精确查询
        if (StrXhUtil.isNotEmpty(pagination.getCustType())) {
            lambda.eq(BizCustomerEntity::getCustType, pagination.getCustType());
        }

        // 根据业务线精确查询
        if (StrXhUtil.isNotEmpty(pagination.getCustLine())) {
            lambda.eq(BizCustomerEntity::getCustLine, pagination.getCustLine());
        }

        // 根据负责人精确查询
        if (StrXhUtil.isNotEmpty(pagination.getLeader())) {
            lambda.eq(BizCustomerEntity::getLeader, pagination.getLeader());
        }

        // 根据关键字搜索客户名称
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.like(BizCustomerEntity::getName, keyword);
        }

        // 排除已删除的记录
        lambda.eq(BizCustomerEntity::getDeleteMark, 0);

        // 排序
        lambda.orderByAsc(BizCustomerEntity::getSortCode);
        lambda.orderByDesc(BizCustomerEntity::getCreatorTime);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public List<BizCustomerEntity> getListByCustType(String custType) {
        return bizCustomerMapper.selectByCustType(custType);
    }

    @Override
    public List<BizCustomerEntity> getListByCustLine(String custLine) {
        return bizCustomerMapper.selectByCustLine(custLine);
    }

    @Override
    public List<BizCustomerEntity> getListByLeader(String leader) {
        return bizCustomerMapper.selectByLeader(leader);
    }

    @Override
    public BizCustomerEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(BizCustomerEntity entity) {
        // 生成客户ID
        String id = RandomUtil.snowId();
        entity.setId(id);

        // 设置删除标记
        entity.setDeleteMark(0);

        // 设置排序码
        if (entity.getSortCode() == null) {
            entity.setSortCode(System.currentTimeMillis());
        }

        // 保存客户
        this.save(entity);

        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, BizCustomerEntity entity) {
        BizCustomerEntity oldEntity = this.getById(id);
        if (oldEntity == null) {
            throw new RuntimeException("客户不存在");
        }

        // 设置ID
        entity.setId(id);

        // 更新客户
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        BizCustomerEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("客户不存在");
        }

        // 逻辑删除
        entity.setDeleteMark(1);
        this.updateById(entity);
    }

    @Override
    public boolean isExistByName(String name, String excludeId) {
        int count = bizCustomerMapper.checkNameExists(name, excludeId);
        return count > 0;
    }

    @Override
    public List<BizCustomerEntity> getSelectList() {
        LambdaQueryWrapper<BizCustomerEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizCustomerEntity::getDeleteMark, 0);
        queryWrapper.orderByAsc(BizCustomerEntity::getSortCode);
        queryWrapper.orderByAsc(BizCustomerEntity::getName);
        return this.list(queryWrapper);
    }
}
