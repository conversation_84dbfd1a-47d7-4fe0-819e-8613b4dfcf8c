import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

// 客户单位管理路由配置
export default {
  path: '/project/customer',
  name: 'ProjectCustomer',
  component: LAYOUT,
  redirect: '/project/customer/index',
  meta: {
    orderNo: 10,
    icon: 'icon-ym icon-ym-customer',
    title: t('客户单位管理'),
    defaultTitle: '客户单位管理',
  },
  children: [
    {
      path: 'index',
      name: 'ProjectCustomerIndex',
      component: () => import('/@/views/project/customer/index.vue'),
      meta: {
        title: t('客户单位列表'),
        defaultTitle: '客户单位列表',
        icon: 'icon-ym icon-ym-customer',
      },
    },
    {
      path: 'detail/:id',
      name: 'ProjectCustomerDetail',
      component: () => import('/@/views/project/customer/Detail.vue'),
      meta: {
        title: t('客户单位详情'),
        defaultTitle: '客户单位详情',
        hideMenu: true,
      },
    },
  ],
};
