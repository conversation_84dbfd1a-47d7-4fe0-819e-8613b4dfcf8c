package com.xinghuo.project.biz.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.biz.entity.BizContractEntity;
import com.xinghuo.project.biz.model.ContractPagination;
import com.xinghuo.project.biz.service.BizContractService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 合同管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "合同管理", description = "合同管理相关接口")
@RestController
@RequestMapping("/api/project/biz/contract")
public class BizContractController {

    @Resource
    private BizContractService bizContractService;

    /**
     * 获取合同列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取合同列表")
    public ActionResult<PageListVO<BizContractEntity>> list(@RequestBody ContractPagination pagination) {
        List<BizContractEntity> list = bizContractService.getList(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 根据客户ID获取合同列表
     */
    @GetMapping("/getListByCustId/{custId}")
    @Operation(summary = "根据客户ID获取合同列表")
    public ActionResult<List<BizContractEntity>> getListByCustId(
            @Parameter(description = "客户ID") @PathVariable String custId) {
        List<BizContractEntity> list = bizContractService.getListByCustId(custId);
        return ActionResult.success(list);
    }

    /**
     * 根据负责人ID获取合同列表
     */
    @GetMapping("/getListByOwnId/{ownId}")
    @Operation(summary = "根据负责人ID获取合同列表")
    public ActionResult<List<BizContractEntity>> getListByOwnId(
            @Parameter(description = "负责人ID") @PathVariable String ownId) {
        List<BizContractEntity> list = bizContractService.getListByOwnId(ownId);
        return ActionResult.success(list);
    }

    /**
     * 获取合同详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取合同详情")
    public ActionResult<BizContractEntity> getInfo(
            @Parameter(description = "合同ID") @PathVariable String id) {
        BizContractEntity entity = bizContractService.getInfo(id);
        return ActionResult.success(entity);
    }

    /**
     * 创建合同
     */
    @PostMapping("/create")
    @Operation(summary = "创建合同")
    public ActionResult<String> create(@RequestBody @Valid BizContractEntity entity) {
        String id = bizContractService.create(entity);
        return ActionResult.success("创建成功", id);
    }

    /**
     * 更新合同
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新合同")
    public ActionResult<String> update(
            @Parameter(description = "合同ID") @PathVariable String id,
            @RequestBody @Valid BizContractEntity entity) {
        bizContractService.update(id, entity);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除合同
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除合同")
    public ActionResult<String> delete(
            @Parameter(description = "合同ID") @PathVariable String id) {
        bizContractService.delete(id);
        return ActionResult.success("删除成功");
    }

    /**
     * 更新合同状态
     */
    @PutMapping("/updateContractStatus/{id}")
    @Operation(summary = "更新合同状态")
    public ActionResult<String> updateContractStatus(
            @Parameter(description = "合同ID") @PathVariable String id,
            @RequestParam String contractStatus) {
        bizContractService.updateContractStatus(id, contractStatus);
        return ActionResult.success("合同状态更新成功");
    }

    /**
     * 更新收款状态
     */
    @PutMapping("/updateMoneyStatus/{id}")
    @Operation(summary = "更新收款状态")
    public ActionResult<String> updateMoneyStatus(
            @Parameter(description = "合同ID") @PathVariable String id,
            @RequestParam String moneyStatus) {
        bizContractService.updateMoneyStatus(id, moneyStatus);
        return ActionResult.success("收款状态更新成功");
    }

    /**
     * 更新工时填写状态
     */
    @PutMapping("/updateWorkStatus/{id}")
    @Operation(summary = "更新工时填写状态")
    public ActionResult<String> updateWorkStatus(
            @Parameter(description = "合同ID") @PathVariable String id,
            @RequestParam Integer workStatus) {
        bizContractService.updateWorkStatus(id, workStatus);
        return ActionResult.success("工时状态更新成功");
    }

    /**
     * 合同续签
     */
    @PostMapping("/renewContract/{id}")
    @Operation(summary = "合同续签")
    public ActionResult<String> renewContract(
            @Parameter(description = "原合同ID") @PathVariable String id,
            @RequestBody @Valid BizContractEntity newContract) {
        String newId = bizContractService.renewContract(id, newContract);
        return ActionResult.success("续签成功", newId);
    }

    /**
     * 检查合同编号是否存在
     */
    @GetMapping("/checkCnoExists")
    @Operation(summary = "检查合同编号是否存在")
    public ActionResult<Boolean> checkCnoExists(
            @RequestParam String cno,
            @RequestParam(required = false) String excludeId) {
        boolean exists = bizContractService.isExistByCno(cno, excludeId);
        return ActionResult.success(exists);
    }

    /**
     * 获取合同统计数据
     */
    @PostMapping("/getContractStatistics")
    @Operation(summary = "获取合同统计数据")
    public ActionResult<List<Map<String, Object>>> getContractStatistics(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> data = bizContractService.getContractStatistics(params);
        return ActionResult.success(data);
    }

    /**
     * 计算合同毛利率
     */
    @PutMapping("/calculateProfitRatio/{id}")
    @Operation(summary = "计算合同毛利率")
    public ActionResult<String> calculateProfitRatio(
            @Parameter(description = "合同ID") @PathVariable String id) {
        bizContractService.calculateProfitRatio(id);
        return ActionResult.success("毛利率计算完成");
    }

    /**
     * 更新合同已收金额
     */
    @PutMapping("/updateReceivedAmount/{id}")
    @Operation(summary = "更新合同已收金额")
    public ActionResult<String> updateReceivedAmount(
            @Parameter(description = "合同ID") @PathVariable String id) {
        bizContractService.updateReceivedAmount(id);
        return ActionResult.success("已收金额更新完成");
    }

    /**
     * 更新合同累计工时
     */
    @PutMapping("/updateAutoManhours/{id}")
    @Operation(summary = "更新合同累计工时")
    public ActionResult<String> updateAutoManhours(
            @Parameter(description = "合同ID") @PathVariable String id) {
        bizContractService.updateAutoManhours(id);
        return ActionResult.success("累计工时更新完成");
    }
}
