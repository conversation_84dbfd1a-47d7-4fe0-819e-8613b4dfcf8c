package com.xinghuo.project.resource.entity.manhour;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工时日志实体类
 *
 * <AUTHOR>
 * @date 2023-12-13
 */

@Data
@TableName("zz_proj_manhour")
public class ManhourLogEntity {
    @TableId(value ="F_ID"  )
    private String id;
    @TableField(value = "MT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String mtId;
    @TableField(value = "USER_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String userId;
    @TableField(value = "PROJ_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private String projType;
    @TableField(value = "PROJECT_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String projectId;
    @TableField(value = "PROJECT_NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String projectName;
    @TableField(value = "MODULE_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String moduleId;
    @TableField(value = "MODULE_NAME" , updateStrategy = FieldStrategy.IGNORED)
    private String moduleName;
    @TableField(value = "WORK_TYPE" , updateStrategy = FieldStrategy.IGNORED)
    private String workType;
    @TableField(value = "MONTH" , updateStrategy = FieldStrategy.IGNORED)
    private String month;
    @TableField(value = "WORK_MONTH" , updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal workMonth;
    @TableField(value = "WORK_NOTE" , updateStrategy = FieldStrategy.IGNORED)
    private String workNote;
    @TableField(value = "CREATE_USER_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String createUserId;
    @TableField(value = "CREATE_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date createTime;
    @TableField(value = "LAST_MODIFIED_USER_ID" , updateStrategy = FieldStrategy.IGNORED)
    private String lastModifiedUserId;
    @TableField(value = "UPDATE_TIME" , updateStrategy = FieldStrategy.IGNORED)
    private Date updateTime;
    @TableField(value = "F_DELETEMARK" , updateStrategy = FieldStrategy.IGNORED)
    private Integer deletemark;
    @TableField(value = "F_FLOWID" , updateStrategy = FieldStrategy.IGNORED)
    private String flowid;
    @TableField(value = "F_TENANTID" , updateStrategy = FieldStrategy.IGNORED)
    private String tenantid;

    // 扩展字段，用于分析查询
    @TableField(exist = false)
    private String userName;
    @TableField(exist = false)
    private String fbId;
    @TableField(exist = false)
    private String fbName;
    @TableField(exist = false)
    private String projTypeName;
    @TableField(exist = false)
    private Integer status;
}
