package com.xinghuo.project.execution.model.vo;

import com.xinghuo.project.execution.model.dto.ProjectDTO;
import com.xinghuo.project.execution.model.dto.ProjectTeamDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 项目快速创建参数VO
 * 对应API: /api/ppm/project/saveFastNew
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
public class ProjectFastNewParamVO {

    /**
     * 项目快速创建参数
     */
    private ProjectFastNewParam projectFastNewParamVo;

    /**
     * 项目基本信息
     */
    private ProjectDTO info;

    /**
     * 项目团队列表
     */
    private List<ProjectTeamDTO> projectTeamList;

    /**
     * 项目快速创建参数内部类
     */
    @Data
    public static class ProjectFastNewParam {

        /**
         * 是否从项目复制
         */
        private Boolean copyFromProject;

        /**
         * 项目模板ID或项目ID
         */
        private String schemaOrProjectID;

        /**
         * 预算分解方式
         * 0-不分解, 1-按阶段分解, 2-按活动分解
         */
        private Integer budgetDecomposition;

        /**
         * 收入分解方式
         * 0-不分解, 1-按阶段分解, 2-按活动分解
         */
        private Integer incomeDecomposition;

        /**
         * 日历模板ID
         */
        private String calendarTemplateID;

        /**
         * 日历开始时间
         */
        private Date calendarStart;

        /**
         * 日历结束时间
         */
        private Date calendarEnd;

        /**
         * 是否创建子项目
         */
        private Boolean createSubProject;

        /**
         * 是否创建阶段计划
         */
        private Boolean createPhasePlan;

        /**
         * 是否创建活动计划
         */
        private Boolean createActivityPlan;

        /**
         * 是否创建任务计划
         */
        private Boolean createTaskPlan;

        /**
         * 是否创建交付物计划
         */
        private Boolean createWorkProductPlan;

        /**
         * 是否创建资源计划
         */
        private Boolean createResourcePlan;

        /**
         * 是否创建成本预算
         */
        private Boolean createCostBudget;

        /**
         * 是否创建收入预算
         */
        private Boolean createIncomeBudget;

        /**
         * 是否创建风险登记册
         */
        private Boolean createRiskRegister;

        /**
         * 是否创建问题日志
         */
        private Boolean createIssueLog;

        /**
         * 是否创建变更日志
         */
        private Boolean createChangeLog;

        /**
         * 是否创建质量计划
         */
        private Boolean createQualityPlan;

        /**
         * 是否创建沟通计划
         */
        private Boolean createCommunicationPlan;

        /**
         * 是否创建采购计划
         */
        private Boolean createProcurementPlan;

        /**
         * 是否创建干系人登记册
         */
        private Boolean createStakeholderRegister;

        /**
         * 项目创建选项
         */
        private ProjectCreateOptions createOptions;
    }

    /**
     * 项目创建选项
     */
    @Data
    public static class ProjectCreateOptions {

        /**
         * 是否自动生成项目编码
         */
        private Boolean autoGenerateCode;

        /**
         * 项目编码前缀
         */
        private String codePrefix;

        /**
         * 是否发送通知
         */
        private Boolean sendNotification;

        /**
         * 通知接收人列表
         */
        private List<String> notificationReceivers;

        /**
         * 是否立即启动项目
         */
        private Boolean startImmediately;

        /**
         * 项目启动时间
         */
        private Date startTime;

        /**
         * 是否创建项目文档库
         */
        private Boolean createDocumentLibrary;

        /**
         * 文档库模板ID
         */
        private String documentLibraryTemplateId;

        /**
         * 是否集成外部系统
         */
        private Boolean integrateExternalSystems;

        /**
         * 外部系统集成配置
         */
        private List<ExternalSystemConfig> externalSystemConfigs;

        /**
         * 项目标签
         */
        private List<String> projectTags;

        /**
         * 项目自定义字段
         */
        private java.util.Map<String, Object> customFields;

        /**
         * 项目模板应用选项
         */
        private TemplateApplyOptions templateApplyOptions;
    }

    /**
     * 外部系统集成配置
     */
    @Data
    public static class ExternalSystemConfig {

        /**
         * 系统类型
         */
        private String systemType;

        /**
         * 系统名称
         */
        private String systemName;

        /**
         * 集成配置
         */
        private java.util.Map<String, Object> config;

        /**
         * 是否启用
         */
        private Boolean enabled;
    }

    /**
     * 模板应用选项
     */
    @Data
    public static class TemplateApplyOptions {

        /**
         * 是否应用阶段模板
         */
        private Boolean applyPhaseTemplate;

        /**
         * 阶段模板ID列表
         */
        private List<String> phaseTemplateIds;

        /**
         * 是否应用活动模板
         */
        private Boolean applyActivityTemplate;

        /**
         * 活动模板ID列表
         */
        private List<String> activityTemplateIds;

        /**
         * 是否应用交付物模板
         */
        private Boolean applyWorkProductTemplate;

        /**
         * 交付物模板ID列表
         */
        private List<String> workProductTemplateIds;

        /**
         * 是否应用角色模板
         */
        private Boolean applyRoleTemplate;

        /**
         * 角色模板ID列表
         */
        private List<String> roleTemplateIds;

        /**
         * 模板应用策略
         * 1-完全覆盖, 2-合并应用, 3-选择性应用
         */
        private Integer applyStrategy;
    }
}
