package com.xinghuo.project.template.model.dto;

import com.xinghuo.project.template.entity.WorkProductLibraryEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 标准交付物库DTO类
 * 包含基本信息和扩展信息
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkProductLibraryDTO extends WorkProductLibraryEntity {

    /**
     * 关联的项目模板ID列表
     */
    private List<String> projectTemplateIds;

    /**
     * 关联的评审模板ID列表
     */
    private List<String> reviewTemplateIds;

    /**
     * 交付物类型名称（冗余字段，便于显示）
     */
    private String typeName;

    /**
     * 交付物子类型名称（冗余字段，便于显示）
     */
    private String subTypeName;

    /**
     * 默认责任角色名称（冗余字段，便于显示）
     */
    private String defaultRoleName;

    /**
     * 状态名称（冗余字段，便于显示）
     */
    private String statusName;

    /**
     * 创建人姓名（冗余字段，便于显示）
     */
    private String creatorUserName;

    /**
     * 使用次数统计
     */
    private Integer usageCount;

    /**
     * 关联项目模板数量
     */
    private Integer projectTemplateCount;

    /**
     * 是否需要评审的文本描述
     */
    private String needReviewText;

    /**
     * 是否是项目最终交付成果的文本描述
     */
    private String isDeliverableText;

    /**
     * 是否可裁剪的文本描述
     */
    private String canCutText;
}
