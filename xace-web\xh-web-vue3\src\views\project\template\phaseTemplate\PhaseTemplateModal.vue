<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #codeSlot="{ model, field }">
        <a-input-group compact>
          <a-input
            v-model:value="model[field]"
            placeholder="请输入阶段编码"
            style="width: calc(100% - 80px)"
            :disabled="isView"
          />
          <a-button @click="handleGenerateCode" :disabled="isView" style="width: 80px">
            生成
          </a-button>
        </a-input-group>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from './phaseTemplate.data';
  import { 
    createPhaseTemplate, 
    updatePhaseTemplate, 
    checkPhaseTemplateCodeExists,
    generatePhaseTemplateCode 
  } from '/@/api/project/phaseTemplate';
  import { useMessage } from '/@/hooks/web/useMessage';

  defineOptions({ name: 'PhaseTemplateModal' });

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  const isUpdate = ref(true);
  const isView = ref(false);
  const rowId = ref('');

  const [registerForm, { setFieldsValue, updateSchema, resetFields, validate, getFieldsValue }] = useForm({
    labelWidth: 100,
    baseColProps: { span: 24 },
    schemas: formSchema,
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;
    isView.value = !!data?.isView;

    if (unref(isUpdate)) {
      rowId.value = data.record.id;
      setFieldsValue({
        ...data.record,
      });
    }

    // 设置表单只读状态
    const schemas = formSchema.map((schema) => {
      if (schema.field === 'code') {
        return {
          ...schema,
          slot: unref(isView) ? undefined : 'codeSlot',
          component: unref(isView) ? 'Input' : undefined,
          componentProps: {
            ...schema.componentProps,
            disabled: unref(isView),
          },
        };
      }
      return {
        ...schema,
        componentProps: {
          ...schema.componentProps,
          disabled: unref(isView),
        },
      };
    });
    updateSchema(schemas);

    // 添加编码重复验证
    if (!unref(isView)) {
      updateSchema([
        {
          field: 'code',
          rules: [
            {
              pattern: /^[A-Z]{2}\d{8}$/,
              message: '阶段编码格式为：PL + 8位数字',
              trigger: 'blur',
            },
            {
              validator: async (_, value) => {
                if (!value) return Promise.resolve();
                const exists = await checkPhaseTemplateCodeExists(value, unref(isUpdate) ? rowId.value : undefined);
                if (exists) {
                  return Promise.reject('阶段编码已存在');
                }
                return Promise.resolve();
              },
              trigger: 'blur',
            },
          ],
        },
      ]);
    }
  });

  const getTitle = computed(() => {
    if (unref(isView)) return '查看阶段模板';
    return !unref(isUpdate) ? '新增阶段模板' : '编辑阶段模板';
  });

  async function handleGenerateCode() {
    try {
      const code = await generatePhaseTemplateCode();
      setFieldsValue({ code });
      createMessage.success('编码生成成功');
    } catch (error) {
      createMessage.error('编码生成失败');
    }
  }

  async function handleSubmit() {
    if (unref(isView)) {
      closeModal();
      return;
    }

    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      if (unref(isUpdate)) {
        await updatePhaseTemplate(rowId.value, values);
        createMessage.success('更新成功');
      } else {
        await createPhaseTemplate(values);
        createMessage.success('创建成功');
      }

      closeModal();
      emit('success');
    } catch (error) {
      createMessage.error(unref(isUpdate) ? '更新失败' : '创建失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
