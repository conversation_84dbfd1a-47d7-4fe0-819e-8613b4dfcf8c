<template>
  <div class="report-dashboard">
    <ErrorBoundary>
      <SafeRefreshHandler>
        <PageWrapper title="项目报表中心" contentBackground>
          <div class="dashboard-layout">
            <!-- 左侧报表导航 -->
            <div class="report-sidebar">
              <a-card :bordered="false" class="sidebar-card">
                <template #title>
                  <div class="sidebar-title">
                    <MenuOutlined class="title-icon" />
                    报表导航
                  </div>
                </template>

                <a-menu v-model:selectedKeys="selectedKeys" mode="inline" class="report-menu" @click="handleMenuClick">
                  <!-- 报表中心首页 -->
                  <a-menu-item key="dashboard">
                    <template #icon><DashboardOutlined /></template>
                    报表中心
                  </a-menu-item>
                  
                  <a-menu-divider />
                  
                  <a-menu-item-group key="financial" title="财务报表">
                    <a-menu-item key="invoice">
                      <template #icon><FileTextOutlined /></template>
                      已开票未收款统计
                    </a-menu-item>
                    <a-menu-item key="payment">
                      <template #icon><MoneyCollectOutlined /></template>
                      收款进度统计
                    </a-menu-item>
                    <a-menu-item key="payable">
                      <template #icon><CreditCardOutlined /></template>
                      付款统计报表
                    </a-menu-item>
                  </a-menu-item-group>

                  <a-menu-item-group key="contract" title="合同报表">
                    <a-menu-item key="contract-summary">
                      <template #icon><ContainerOutlined /></template>
                      合同汇总统计
                    </a-menu-item>
                    <a-menu-item key="contract-progress">
                      <template #icon><ClockCircleOutlined /></template>
                      合同执行进度
                    </a-menu-item>
                  </a-menu-item-group>

                  <a-menu-item-group key="business" title="商机报表">
                    <a-menu-item key="opportunity">
                      <template #icon><RocketOutlined /></template>
                      商机统计分析
                    </a-menu-item>
                    <a-menu-item key="conversion">
                      <template #icon><FilterOutlined /></template>
                      商机转化率
                    </a-menu-item>
                  </a-menu-item-group>

                  <a-menu-item-group key="project" title="项目报表">
                    <a-menu-item key="project-progress">
                      <template #icon><ProjectOutlined /></template>
                      项目进度统计
                    </a-menu-item>
                    <a-menu-item key="milestone">
                      <template #icon><FlagOutlined /></template>
                      里程碑完成情况
                    </a-menu-item>
                  </a-menu-item-group>
                </a-menu>
              </a-card>
            </div>

            <!-- 右侧主要内容 -->
            <div class="report-content">
              <!-- 报表中心首页内容 -->
              <div v-if="activeReportKey === 'dashboard'" class="dashboard-content">
                <!-- 年份选择器 -->
                <div class="year-selector-section mb-4">
                  <a-card :bordered="false" class="year-card">
                    <div class="year-selector-content">
                      <div class="year-label">
                        <CalendarOutlined class="year-icon" />
                        数据年份：
                      </div>
                      <a-select v-model:value="selectedYear" class="year-select" @change="handleYearChange">
                        <a-select-option v-for="year in yearOptions" :key="year" :value="year"> {{ year }}年 </a-select-option>
                      </a-select>
                      <div class="year-tip">
                        <InfoCircleOutlined class="tip-icon" />
                        默认显示{{ currentYear }}年数据
                      </div>
                    </div>
                  </a-card>
                </div>

                <!-- 统计概览卡片 -->
                <div class="overview-section mb-6">
                  <a-row :gutter="[16, 16]">
                    <a-col :xs="24" :sm="12" :lg="6">
                      <a-card :bordered="false" class="stat-card contract-card">
                        <a-statistic title="合同总数" :value="dashboardData.contractCount" :loading="loading">
                          <template #prefix>
                            <ContainerOutlined class="stat-icon" />
                          </template>
                        </a-statistic>
                        <div class="stat-extra">
                          <span class="extra-text">本月新增: {{ dashboardData.monthlyNewContracts }}</span>
                        </div>
                      </a-card>
                    </a-col>

                    <a-col :xs="24" :sm="12" :lg="6">
                      <a-card :bordered="false" class="stat-card revenue-card">
                        <a-statistic title="合同总金额" :value="dashboardData.totalContractAmount" :precision="2" :loading="loading" suffix="万元">
                          <template #prefix>
                            <DollarOutlined class="stat-icon" />
                          </template>
                        </a-statistic>
                        <div class="stat-extra">
                          <span class="extra-text">已收: {{ formatAmount(dashboardData.receivedAmount) }}</span>
                        </div>
                      </a-card>
                    </a-col>

                    <a-col :xs="24" :sm="12" :lg="6">
                      <a-card :bordered="false" class="stat-card payment-card">
                        <a-statistic title="待收款金额" :value="dashboardData.pendingAmount" :precision="2" :loading="loading" suffix="万元">
                          <template #prefix>
                            <MoneyCollectOutlined class="stat-icon" />
                          </template>
                        </a-statistic>
                        <div class="stat-extra">
                          <span class="extra-text">逾期: {{ formatAmount(dashboardData.overdueAmount) }}</span>
                        </div>
                      </a-card>
                    </a-col>

                    <a-col :xs="24" :sm="12" :lg="6">
                      <a-card :bordered="false" class="stat-card opportunity-card">
                        <a-statistic title="商机总数" :value="dashboardData.opportunityCount" :loading="loading">
                          <template #prefix>
                            <RocketOutlined class="stat-icon" />
                          </template>
                        </a-statistic>
                        <div class="stat-extra">
                          <span class="extra-text">转化率: {{ dashboardData.conversionRate }}%</span>
                        </div>
                      </a-card>
                    </a-col>
                  </a-row>
                </div>

                <!-- 图表展示区域 -->
                <div class="charts-section">
                  <a-row :gutter="[16, 16]">
                    <!-- 收款趋势图 -->
                    <a-col :xs="24" :lg="12">
                      <a-card :bordered="false" class="chart-card">
                        <template #title>
                          <div class="card-title">
                            <LineChartOutlined class="title-icon" />
                            收款趋势分析
                          </div>
                        </template>
                        <div ref="paymentTrendChart" class="chart-container"></div>
                      </a-card>
                    </a-col>

                    <!-- 合同状态分布 -->
                    <a-col :xs="24" :lg="12">
                      <a-card :bordered="false" class="chart-card">
                        <template #title>
                          <div class="card-title">
                            <PieChartOutlined class="title-icon" />
                            合同状态分布
                          </div>
                        </template>
                        <div ref="contractStatusChart" class="chart-container"></div>
                      </a-card>
                    </a-col>

                    <!-- 部门收款排行 -->
                    <a-col :xs="24" :lg="12">
                      <a-card :bordered="false" class="chart-card">
                        <template #title>
                          <div class="card-title">
                            <BarChartOutlined class="title-icon" />
                            部门收款排行
                          </div>
                        </template>
                        <div ref="departmentRankChart" class="chart-container"></div>
                      </a-card>
                    </a-col>

                    <!-- 商机转化漏斗 -->
                    <a-col :xs="24" :lg="12">
                      <a-card :bordered="false" class="chart-card">
                        <template #title>
                          <div class="card-title">
                            <FunnelPlotOutlined class="title-icon" />
                            商机转化漏斗
                          </div>
                        </template>
                        <div ref="conversionFunnelChart" class="chart-container"></div>
                      </a-card>
                    </a-col>
                  </a-row>
                </div>

                <!-- 快速操作区域 -->
                <div class="quick-actions-section mt-6">
                  <a-card :bordered="false" class="actions-card">
                    <template #title>
                      <div class="card-title">
                        <ThunderboltOutlined class="title-icon" />
                        快速操作
                      </div>
                    </template>

                    <a-row :gutter="[16, 16]">
                      <a-col :xs="24" :sm="12" :md="8" :lg="6">
                        <div class="action-item" @click="handleMenuClick({key: 'invoice'})">
                          <FileTextOutlined class="action-icon" />
                          <div class="action-content">
                            <div class="action-title">开票统计</div>
                            <div class="action-desc">查看已开票未收款情况</div>
                          </div>
                        </div>
                      </a-col>

                      <a-col :xs="24" :sm="12" :md="8" :lg="6">
                        <div class="action-item" @click="handleMenuClick({key: 'contract-summary'})">
                          <ContainerOutlined class="action-icon" />
                          <div class="action-content">
                            <div class="action-title">合同汇总</div>
                            <div class="action-desc">查看合同整体情况</div>
                          </div>
                        </div>
                      </a-col>

                      <a-col :xs="24" :sm="12" :md="8" :lg="6">
                        <div class="action-item" @click="handleMenuClick({key: 'opportunity'})">
                          <RocketOutlined class="action-icon" />
                          <div class="action-content">
                            <div class="action-title">商机分析</div>
                            <div class="action-desc">查看商机统计数据</div>
                          </div>
                        </div>
                      </a-col>

                      <a-col :xs="24" :sm="12" :md="8" :lg="6">
                        <div class="action-item" @click="handleMenuClick({key: 'project-progress'})">
                          <ProjectOutlined class="action-icon" />
                          <div class="action-content">
                            <div class="action-title">项目进度</div>
                            <div class="action-desc">查看项目执行情况</div>
                          </div>
                        </div>
                      </a-col>
                    </a-row>
                  </a-card>
                </div>
              </div>

              <!-- 动态报表内容区域 -->
              <div v-else class="dynamic-report-content">
                <!-- 面包屑导航 -->
                <div class="breadcrumb-section mb-4">
                  <a-breadcrumb>
                    <a-breadcrumb-item>
                      <a @click="handleMenuClick({key: 'dashboard'})">
                        <DashboardOutlined />
                        报表中心
                      </a>
                    </a-breadcrumb-item>
                    <a-breadcrumb-item>{{ getReportTitle(activeReportKey) }}</a-breadcrumb-item>
                  </a-breadcrumb>
                </div>

                <!-- 动态加载的报表组件 -->
                <component :is="currentReportComponent" v-if="currentReportComponent" />
                
                <!-- 未实现的报表提示 -->
                <div v-else class="empty-report">
                  <a-empty :description="`${getReportTitle(activeReportKey)}正在开发中...`">
                    <template #image>
                      <BarChartOutlined style="font-size: 64px; color: #d9d9d9;" />
                    </template>
                    <a-button type="primary" @click="handleMenuClick({key: 'dashboard'})">
                      返回报表中心
                    </a-button>
                  </a-empty>
                </div>
              </div>
            </div>
          </div>
        </PageWrapper>
      </SafeRefreshHandler>
    </ErrorBoundary>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, nextTick, onErrorCaptured, onBeforeUnmount, defineAsyncComponent } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { PageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import ErrorBoundary from './components/ErrorBoundary.vue';
  import SafeRefreshHandler from './components/SafeRefreshHandler.vue';
  import { safeNavigate, safeRefresh, handleRouteError } from './utils/routeGuard';
  import {
    getDashboardData,
    getPaymentTrendData,
    getContractStatusData,
    getDepartmentRankData,
    getConversionFunnelData,
  } from '/@/api/project/report/dashboard';
  import * as echarts from 'echarts';
  import {
    MenuOutlined,
    FileTextOutlined,
    MoneyCollectOutlined,
    CreditCardOutlined,
    ContainerOutlined,
    ClockCircleOutlined,
    RocketOutlined,
    FilterOutlined,
    ProjectOutlined,
    FlagOutlined,
    DollarOutlined,
    LineChartOutlined,
    PieChartOutlined,
    BarChartOutlined,
    ThunderboltOutlined,
    CalendarOutlined,
    InfoCircleOutlined,
    DashboardOutlined,
    FunnelPlotOutlined,
  } from '@ant-design/icons-vue';

  defineOptions({ name: 'ProjectReportDashboard' });

  const router = useRouter();
  const route = useRoute();
  const { createMessage } = useMessage();

  const loading = ref(false);
  const selectedKeys = ref<string[]>(['dashboard']);
  const activeReportKey = ref('dashboard');
  const currentReportComponent = ref(null);

  // 动态导入报表组件
  const reportComponents = {
    invoice: defineAsyncComponent(() => import('./invoice/index.vue')),
    // 可以继续添加其他报表组件
    // payment: defineAsyncComponent(() => import('./payment/index.vue')),
    // payable: defineAsyncComponent(() => import('./payable/index.vue')),
    // ...etc
  };

  // 报表标题映射
  const reportTitles = {
    dashboard: '报表中心',
    invoice: '已开票未收款统计',
    payment: '收款进度统计',
    payable: '付款统计报表',
    'contract-summary': '合同汇总统计',
    'contract-progress': '合同执行进度',
    opportunity: '商机统计分析',
    conversion: '商机转化率',
    'project-progress': '项目进度统计',
    milestone: '里程碑完成情况',
  };

  // 年份相关
  const currentYear = new Date().getFullYear();
  const selectedYear = ref(currentYear);
  const yearOptions = ref<number[]>([]);

  // 图表引用
  const paymentTrendChart = ref();
  const contractStatusChart = ref();
  const departmentRankChart = ref();
  const conversionFunnelChart = ref();

  // 仪表板数据
  const dashboardData = ref({
    contractCount: 0,
    monthlyNewContracts: 0,
    totalContractAmount: 0,
    receivedAmount: 0,
    pendingAmount: 0,
    overdueAmount: 0,
    opportunityCount: 0,
    conversionRate: 0,
  });

  // 获取报表标题
  function getReportTitle(key: string) {
    return reportTitles[key] || '未知报表';
  }

  // 格式化金额
  function formatAmount(amount: number) {
    return (amount / 10000).toFixed(2) + '万';
  }

  // 菜单点击处理
  function handleMenuClick({ key }: { key: string }) {
    selectedKeys.value = [key];
    activeReportKey.value = key;
    
    if (key === 'dashboard') {
      currentReportComponent.value = null;
      // 重新初始化图表
      nextTick(() => {
        initCharts();
      });
    } else {
      // 加载对应的报表组件
      const component = reportComponents[key];
      if (component) {
        currentReportComponent.value = component;
      } else {
        currentReportComponent.value = null;
        createMessage.info(`${getReportTitle(key)} 报表正在开发中...`);
      }
    }
  }

  // 初始化年份选项
  function initYearOptions() {
    const years: number[] = [];
    for (let year = 2020; year <= currentYear; year++) {
      years.push(year);
    }
    yearOptions.value = years.reverse(); // 最新年份在前
  }

  // 年份变更处理
  function handleYearChange(year: number) {
    selectedYear.value = year;
    loadDashboardData();
    initCharts();
  }

  // 加载仪表板数据
  async function loadDashboardData() {
    try {
      loading.value = true;
      // 这里可以传递年份参数给API
      const data = await getDashboardData();
      dashboardData.value = data;
    } catch (error) {
      console.error('获取仪表板数据失败:', error);
      createMessage.error('获取数据失败');
    } finally {
      loading.value = false;
    }
  }

  // 初始化图表
  async function initCharts() {
    if (activeReportKey.value !== 'dashboard') return;
    
    await nextTick();
    try {
      await Promise.all([initPaymentTrendChart(), initContractStatusChart(), initDepartmentRankChart(), initConversionFunnelChart()]);
    } catch (error) {
      console.error('初始化图表失败:', error);
    }
  }

  // 收款趋势图
  async function initPaymentTrendChart() {
    if (!paymentTrendChart.value) return;

    try {
      const data = await getPaymentTrendData();
      const chart = echarts.init(paymentTrendChart.value);
      const option = {
        tooltip: { trigger: 'axis' },
        legend: { data: ['收款金额', '目标金额'] },
        xAxis: {
          type: 'category',
          data: data.months,
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}万',
          },
        },
        series: [
          {
            name: '收款金额',
            type: 'line',
            data: data.receivedAmounts,
            smooth: true,
            itemStyle: { color: '#1890ff' },
          },
          {
            name: '目标金额',
            type: 'line',
            data: data.targetAmounts,
            lineStyle: { type: 'dashed' },
            itemStyle: { color: '#52c41a' },
          },
        ],
      };
      chart.setOption(option);
    } catch (error) {
      console.error('初始化收款趋势图失败:', error);
    }
  }

  // 合同状态分布图
  async function initContractStatusChart() {
    if (!contractStatusChart.value) return;

    try {
      const data = await getContractStatusData();
      const chart = echarts.init(contractStatusChart.value);

      // 确保data是数组
      const chartData = Array.isArray(data) ? data : [];

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        series: [
          {
            name: '合同状态',
            type: 'pie',
            radius: '60%',
            data: chartData.map(item => ({
              value: item.value,
              name: item.name,
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      };
      chart.setOption(option);
    } catch (error) {
      console.error('初始化合同状态分布图失败:', error);
      // 使用默认数据
      const chart = echarts.init(contractStatusChart.value);
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        series: [
          {
            name: '合同状态',
            type: 'pie',
            radius: '60%',
            data: [
              { value: 35, name: '执行中' },
              { value: 25, name: '已完成' },
              { value: 15, name: '草稿' },
              { value: 25, name: '已归档' },
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      };
      chart.setOption(option);
    }
  }

  // 部门收款排行图
  async function initDepartmentRankChart() {
    if (!departmentRankChart.value) return;

    try {
      const data = await getDepartmentRankData();
      const chart = echarts.init(departmentRankChart.value);
      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}万元',
        },
        xAxis: {
          type: 'category',
          data: data.departments,
          axisLabel: {
            rotate: 45,
            interval: 0,
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}万',
          },
        },
        series: [
          {
            type: 'bar',
            data: data.amounts,
            itemStyle: { color: '#1890ff' },
          },
        ],
      };
      chart.setOption(option);
    } catch (error) {
      console.error('初始化部门收款排行图失败:', error);
    }
  }

  // 商机转化漏斗图
  async function initConversionFunnelChart() {
    if (!conversionFunnelChart.value) return;

    try {
      const data = await getConversionFunnelData();
      const chart = echarts.init(conversionFunnelChart.value);

      // 确保data是数组
      const chartData = Array.isArray(data) ? data : [];

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c}',
        },
        series: [
          {
            name: '商机转化',
            type: 'funnel',
            data: chartData.map(item => ({
              value: item.value,
              name: item.name,
            })),
            sort: 'descending',
            gap: 2,
            label: {
              show: true,
              position: 'inside',
            },
            labelLine: {
              length: 10,
              lineStyle: {
                width: 1,
                type: 'solid',
              },
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1,
            },
          },
        ],
      };
      chart.setOption(option);
    } catch (error) {
      console.error('初始化商机转化漏斗图失败:', error);
      // 使用默认数据
      const chart = echarts.init(conversionFunnelChart.value);
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c}',
        },
        series: [
          {
            name: '商机转化',
            type: 'funnel',
            data: [
              { value: 100, name: '潜在客户' },
              { value: 80, name: '意向客户' },
              { value: 60, name: '商机跟进' },
              { value: 40, name: '方案提交' },
              { value: 20, name: '合同签署' },
            ],
            sort: 'descending',
            gap: 2,
            label: {
              show: true,
              position: 'inside',
            },
          },
        ],
      };
      chart.setOption(option);
    }
  }

  // 错误捕获处理
  onErrorCaptured((error: Error, instance, info) => {
    console.error('Dashboard error captured:', error, info);
    handleRouteError(router, error);
    return false; // 阻止错误继续传播
  });

  // 组件卸载前的清理
  onBeforeUnmount(() => {
    try {
      // 清理图表实例
      [paymentTrendChart, contractStatusChart, departmentRankChart, conversionFunnelChart].forEach(chartRef => {
        if (chartRef.value) {
          const chartInstance = echarts.getInstanceByDom(chartRef.value);
          if (chartInstance) {
            chartInstance.dispose();
          }
        }
      });
    } catch (error) {
      console.error('Chart cleanup error:', error);
    }
  });

  onMounted(() => {
    try {
      initYearOptions();
      loadDashboardData();
      
      // 根据路由参数确定初始显示的报表
      const reportKey = route.query.report as string;
      if (reportKey && reportKey !== 'dashboard') {
        handleMenuClick({ key: reportKey });
      } else {
        initCharts();
      }
    } catch (error) {
      console.error('Dashboard mount error:', error);
      createMessage.error('页面初始化失败');
      handleRouteError(router, error as Error);
    }
  });
</script>

<style lang="less" scoped>
  .report-dashboard {
    .dashboard-layout {
      display: flex;
      gap: 16px;
      min-height: calc(100vh - 200px);
    }

    .report-sidebar {
      width: 280px;
      flex-shrink: 0;

      .sidebar-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        height: fit-content;
        position: sticky;
        top: 20px;
      }

      .sidebar-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #2c3e50;

        .title-icon {
          color: #1890ff;
          font-size: 16px;
        }
      }

      .report-menu {
        border: none;

        :deep(.ant-menu-item-group-title) {
          font-weight: 600;
          color: #666;
          padding-left: 16px;
        }

        :deep(.ant-menu-item) {
          margin: 4px 0;
          border-radius: 6px;

          &:hover {
            background: #f0f2f5;
          }

          &.ant-menu-item-selected {
            background: #e6f7ff;
            border-color: #1890ff;

            .anticon {
              color: #1890ff;
            }
          }
        }

        :deep(.ant-menu-divider) {
          margin: 12px 0;
        }
      }
    }

    .report-content {
      flex: 1;
      min-width: 0;
    }

    .dashboard-content {
      // 原有的仪表板样式保持不变
      // ...existing styles...
    }

    .dynamic-report-content {
      .breadcrumb-section {
        .ant-breadcrumb {
          font-size: 14px;
          
          a {
            color: #1890ff;
            text-decoration: none;
            
            &:hover {
              text-decoration: underline;
            }
          }
        }
      }

      .empty-report {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 400px;
        background: #fafafa;
        border-radius: 8px;
      }
    }

    // ...existing styles...
    .year-selector-section {
      .year-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }

      .year-selector-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .year-label {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #2c3e50;

          .year-icon {
            color: #1890ff;
            font-size: 16px;
          }
        }

        .year-select {
          width: 120px;
        }

        .year-tip {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #999;

          .tip-icon {
            color: #1890ff;
          }
        }
      }
    }

    .overview-section {
      .stat-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: transform 0.2s ease, box-shadow 0.2s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        :deep(.ant-statistic-title) {
          color: #666;
          font-weight: 500;
          margin-bottom: 8px;
        }

        :deep(.ant-statistic-content) {
          color: #2c3e50;
          font-weight: 600;
        }

        .stat-icon {
          font-size: 20px;
          margin-right: 8px;
        }

        .stat-extra {
          margin-top: 12px;
          padding-top: 12px;
          border-top: 1px solid #f0f0f0;

          .extra-text {
            font-size: 12px;
            color: #999;
          }
        }

        &.contract-card {
          .stat-icon {
            color: #1890ff;
          }

          :deep(.ant-card-body) {
            background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
          }
        }

        &.revenue-card {
          .stat-icon {
            color: #52c41a;
          }

          :deep(.ant-card-body) {
            background: linear-gradient(135deg, #f6ffed 0%, #f0fff4 100%);
          }
        }

        &.payment-card {
          .stat-icon {
            color: #faad14;
          }

          :deep(.ant-card-body) {
            background: linear-gradient(135deg, #fffbe6 0%, #fffbf0 100%);
          }
        }

        &.opportunity-card {
          .stat-icon {
            color: #722ed1;
          }

          :deep(.ant-card-body) {
            background: linear-gradient(135deg, #f9f0ff 0%, #faf5ff 100%);
          }
        }
      }
    }

    .charts-section {
      .chart-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .chart-container {
          height: 300px;
          width: 100%;
        }
      }
    }

    .quick-actions-section {
      .actions-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }

      .action-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        border: 1px solid #e8eaec;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: #1890ff;
          background: #f0f9ff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
        }

        .action-icon {
          font-size: 24px;
          color: #1890ff;
          flex-shrink: 0;
        }

        .action-content {
          flex: 1;

          .action-title {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
          }

          .action-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
          }
        }
      }
    }

    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #2c3e50;

      .title-icon {
        color: #1890ff;
        font-size: 16px;
      }
    }

    .mb-6 {
      margin-bottom: 24px;
    }

    .mt-6 {
      margin-top: 24px;
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .report-dashboard {
      .dashboard-layout {
        flex-direction: column;
      }

      .report-sidebar {
        width: 100%;

        .sidebar-card {
          position: static;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .report-dashboard {
      .overview-section {
        .stat-card {
          margin-bottom: 16px;
        }
      }

      .charts-section {
        .chart-card {
          margin-bottom: 16px;

          .chart-container {
            height: 250px;
          }
        }
      }

      .quick-actions-section {
        .action-item {
          padding: 12px;

          .action-icon {
            font-size: 20px;
          }

          .action-content {
            .action-title {
              font-size: 13px;
            }

            .action-desc {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
</style>
