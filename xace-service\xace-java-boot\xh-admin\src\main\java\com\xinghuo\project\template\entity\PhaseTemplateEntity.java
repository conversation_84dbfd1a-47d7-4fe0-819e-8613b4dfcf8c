package com.xinghuo.project.template.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 阶段库实体类 (标准阶段/里程碑模板)
 * 对应数据库表：zz_proj_tpl_phase
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_tpl_phase")
public class PhaseTemplateEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 阶段编码 (如: PL00000004)
     */
    @TableField("code")
    private String code;

    /**
     * 阶段名称 (如: 总结与评审)
     */
    @TableField("name")
    private String name;

    /**
     * 阶段描述 (如: 总结项目成果...)
     */
    @TableField("description")
    private String description;

    /**
     * 标准工期(天)
     */
    @TableField("std_duration")
    private Integer stdDuration;

    /**
     * 默认阶段完成审批流程ID (关联审批模板表)
     */
    @TableField("default_approval_id")
    private String defaultApprovalId;

    /**
     * 默认检查单模板ID (关联检查单模板表)
     */
    @TableField("default_checklist_id")
    private String defaultChecklistId;

    /**
     * 状态 (1:启用, 0:禁用)
     */
    @TableField("status")
    private Integer status;
}
