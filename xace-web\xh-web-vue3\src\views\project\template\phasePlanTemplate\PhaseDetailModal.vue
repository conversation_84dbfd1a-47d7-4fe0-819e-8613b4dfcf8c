<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { phaseDetailFormSchema } from './phasePlanTemplate.data';
  import { useMessage } from '/@/hooks/web/useMessage';

  defineOptions({ name: 'PhaseDetailModal' });

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  const isUpdate = ref(false);
  const currentIndex = ref(-1);

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    baseColProps: { span: 24 },
    schemas: phaseDetailFormSchema,
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;
    currentIndex.value = data?.index ?? -1;

    if (unref(isUpdate) && data.record) {
      setFieldsValue({
        ...data.record,
      });
    } else {
      // 新增时设置默认值
      setFieldsValue({
        duration: 1,
        canCut: 1,
      });
    }
  });

  const getTitle = computed(() => {
    return !unref(isUpdate) ? '添加阶段' : '编辑阶段';
  });

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      // 发送成功事件
      emit('success', {
        record: values,
        isUpdate: unref(isUpdate),
        index: currentIndex.value,
      });

      closeModal();
    } catch (error) {
      createMessage.error('保存失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
