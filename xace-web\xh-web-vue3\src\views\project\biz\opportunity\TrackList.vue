<template>
  <div>
    <a-spin :spinning="loading">
      <a-empty v-if="trackList.length === 0" description="暂无跟踪记录" />

      <a-timeline v-else>
        <a-timeline-item v-for="(item, index) in trackList" :key="index" color="blue">
          <template #dot>
            <MessageOutlined />
          </template>

          <div class="track-item">
            <div class="track-header">
              <span class="track-time">{{ item.createTime }}</span>
              <span class="track-user">记录人: {{ item.createUserName || '未知' }}</span>
            </div>

            <div class="track-content">
              <div class="track-message">{{ item.content }}</div>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, PropType } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { MessageOutlined } from '@ant-design/icons-vue';
  import { getBusinessWeeklogListByBusinessId, BusinessWeeklogModel } from '/@/api/project/businessWeeklog';

  const props = defineProps({
    businessId: {
      type: String as PropType<string>,
      required: true,
    },
  });

  const { createMessage } = useMessage();
  const trackList = ref<BusinessWeeklogModel[]>([]);
  const loading = ref(false);

  // 加载跟踪记录列表
  async function loadTrackList() {
    try {
      loading.value = true;

      // 调用API获取商机跟踪记录
      const data = await getBusinessWeeklogListByBusinessId(props.businessId);
      trackList.value = data;
    } catch (error) {
      console.error('获取跟踪记录列表失败:', error);
      createMessage.error('获取跟踪记录列表失败');
    } finally {
      loading.value = false;
    }
  }

  // 暴露方法给父组件
  defineExpose({
    loadTrackList,
  });

  onMounted(() => {
    loadTrackList();
  });
</script>

<style lang="less" scoped>
.track-item {
  margin-bottom: 16px;

  .track-header {
    margin-bottom: 8px;

    .track-time {
      color: #999;
      margin-right: 16px;
    }

    .track-user {
      color: #666;
    }
  }

  .track-content {
    background-color: #f0f7ff;
    padding: 12px;
    border-radius: 4px;

    .track-message {
      white-space: pre-wrap;
    }
  }
}
</style>
