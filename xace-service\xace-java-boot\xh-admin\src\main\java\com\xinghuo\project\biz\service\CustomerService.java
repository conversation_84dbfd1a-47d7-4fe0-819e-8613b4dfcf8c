package com.xinghuo.project.biz.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.biz.entity.CustomerEntity;
import com.xinghuo.project.biz.model.CustomerPagination;

import java.util.List;

/**
 * 客户服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface CustomerService extends BaseService<CustomerEntity> {

    /**
     * 分页查询客户列表
     *
     * @param pagination 查询条件
     * @return 客户列表
     */
    List<CustomerEntity> getList(CustomerPagination pagination);

    /**
     * 根据客户类型查询客户列表
     *
     * @param custType 客户类型
     * @return 客户列表
     */
    List<CustomerEntity> getListByCustType(String custType);

    /**
     * 根据业务线查询客户列表
     *
     * @param custLine 业务线
     * @return 客户列表
     */
    List<CustomerEntity> getListByCustLine(String custLine);

    /**
     * 根据负责人查询客户列表
     *
     * @param leader 负责人
     * @return 客户列表
     */
    List<CustomerEntity> getListByLeader(String leader);

    /**
     * 根据ID查询客户信息
     *
     * @param id 客户ID
     * @return 客户信息
     */
    CustomerEntity getInfo(String id);

    /**
     * 创建客户
     *
     * @param entity 客户信息
     * @return 客户ID
     */
    String create(CustomerEntity entity);

    /**
     * 更新客户
     *
     * @param id 客户ID
     * @param entity 更新信息
     */
    void update(String id, CustomerEntity entity);

    /**
     * 删除客户
     *
     * @param id 客户ID
     */
    void delete(String id);

    /**
     * 检查客户名称是否存在
     *
     * @param name 客户名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByName(String name, String excludeId);

    /**
     * 获取客户选择列表
     *
     * @return 客户选择列表
     */
    List<CustomerEntity> getSelectList();
}
