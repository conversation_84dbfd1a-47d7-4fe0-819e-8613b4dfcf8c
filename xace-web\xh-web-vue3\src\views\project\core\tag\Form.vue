<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #tagColor="{ model, field }">
        <xh-color-picker v-model:value="model[field]" size="small" :predefine="predefineColors" />
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import { createTag, updateTag, getTagInfo } from '/@/api/project/tag';
  import { useMessage } from '/@/hooks/web/useMessage';
  // 不需要使用 useI18n
  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'reload']);
  const isUpdate = ref(false);
  const tagId = ref('');

  // 预定义颜色列表
  const predefineColors = [
    '#ff4d4f', // 红色
    '#ff7a45', // 橙红色
    '#fa8c16', // 橙色
    '#faad14', // 黄色
    '#a0d911', // 青柠色
    '#52c41a', // 绿色
    '#13c2c2', // 青色
    '#1890ff', // 蓝色
    '#2f54eb', // 靛蓝色
    '#722ed1', // 紫色
    '#eb2f96', // 玫红色
    '#bfbfbf', // 灰色
  ];

  // 表单配置
  const formSchemas: FormSchema[] = [
    {
      field: 'tagName',
      label: '标签名称',
      component: 'Input',
      componentProps: { placeholder: '请输入标签名称', maxlength: 50 },
      rules: [
        { required: true, trigger: 'blur', message: '请输入标签名称' },
        { max: 50, message: '标签名称最多为50个字符', trigger: 'blur' },
      ],
    },
    {
      field: 'tagColor',
      label: '标签颜色',
      component: 'Input',
      slot: 'tagColor',
      defaultValue: '#1890ff',
      rules: [{ required: true, trigger: 'change', message: '请选择标签颜色' }],
    },
    {
      field: 'description',
      label: '标签描述',
      component: 'Textarea',
      componentProps: { placeholder: '请输入标签描述', maxlength: 200, showCount: true, rows: 4 },
      rules: [{ max: 200, message: '标签描述最多为200个字符', trigger: 'blur' }],
    },
    {
      field: 'scope',
      label: '标签范围',
      component: 'Select',
      defaultValue: 'global',
      componentProps: {
        options: [
          { fullName: '全局可见', id: 'global' },
          { fullName: '特定用户可见', id: 'user_specific' },
        ],
        placeholder: '请选择标签范围',
      },
      rules: [{ required: true, trigger: 'change', message: '请选择标签范围' }],
    },
  ];

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 80,
    schemas: formSchemas,
    showActionButtonGroup: false,
  });

  // 注册模态框
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async data => {
    resetFields();
    setModalProps({ confirmLoading: false });

    isUpdate.value = !!data?.isUpdate;
    if (unref(isUpdate)) {
      tagId.value = data.record.id;
      const tagInfo = await getTagInfo(data.record.id);
      setFieldsValue({
        ...tagInfo,
      });
    }
  });

  // 获取标题
  const getTitle = computed(() => {
    return unref(isUpdate) ? '编辑标签' : '新增标签';
  });

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      if (unref(isUpdate)) {
        await updateTag(tagId.value, values);
        createMessage.success('更新成功');
      } else {
        await createTag(values);
        createMessage.success('新增成功');
      }

      closeModal();
      emit('reload');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
