<template>
  <BasicModal 
    v-bind="$attrs" 
    @register="registerModal" 
    title="从阶段库选择" 
    @ok="handleSubmit"
    width="800px"
  >
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-input-search
          v-model:value="searchKeyword"
          placeholder="搜索阶段名称或编码"
          style="width: 300px"
          @search="handleSearch"
        />
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, useTable } from '/@/components/Table';
  import { standardPhaseColumns } from './phasePlanTemplate.data';
  import { getPhaseTemplateSelectList } from '/@/api/project/phaseTemplate';
  import { useMessage } from '/@/hooks/web/useMessage';

  defineOptions({ name: 'PhaseLibraryModal' });

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  const searchKeyword = ref('');

  const [registerTable, { reload, getSelectRows }] = useTable({
    title: '标准阶段库',
    api: getPhaseTemplateSelectList,
    rowKey: 'id',
    columns: standardPhaseColumns,
    useSearchForm: false,
    showTableSetting: false,
    bordered: true,
    rowSelection: {
      type: 'checkbox',
    },
    pagination: {
      pageSize: 10,
    },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false });
    searchKeyword.value = '';
    reload();
  });

  function handleSearch() {
    reload({
      keyword: searchKeyword.value,
    });
  }

  async function handleSubmit() {
    const selectedRows = getSelectRows();
    if (selectedRows.length === 0) {
      createMessage.warning('请选择要添加的阶段');
      return;
    }

    try {
      setModalProps({ confirmLoading: true });
      
      // 发送成功事件，传递选中的阶段
      emit('success', selectedRows);
      
      createMessage.success(`成功添加 ${selectedRows.length} 个阶段`);
      closeModal();
    } catch (error) {
      createMessage.error('添加失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
