package com.xinghuo.project.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.biz.dao.CustomerMapper;
import com.xinghuo.project.biz.entity.CustomerEntity;
import com.xinghuo.project.biz.model.CustomerPagination;
import com.xinghuo.project.biz.service.CustomerService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 客户服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class CustomerServiceImpl extends BaseServiceImpl<CustomerMapper, CustomerEntity> implements CustomerService {

    @Resource
    private CustomerMapper customerMapper;

    @Override
    public List<CustomerEntity> getList(CustomerPagination pagination) {
        QueryWrapper<CustomerEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<CustomerEntity> lambda = queryWrapper.lambda();

        // 根据客户名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(CustomerEntity::getName, pagination.getName());
        }

        // 根据客户类型精确查询
        if (StrXhUtil.isNotEmpty(pagination.getCustType())) {
            lambda.eq(CustomerEntity::getCustType, pagination.getCustType());
        }

        // 根据业务线精确查询
        if (StrXhUtil.isNotEmpty(pagination.getCustLine())) {
            lambda.eq(CustomerEntity::getCustLine, pagination.getCustLine());
        }

        // 根据负责人精确查询
        if (StrXhUtil.isNotEmpty(pagination.getLeader())) {
            lambda.eq(CustomerEntity::getLeader, pagination.getLeader());
        }

        // 根据关键字搜索客户名称
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.like(CustomerEntity::getName, keyword);
        }

        // 排除已删除的记录
        lambda.eq(CustomerEntity::getDeleteMark, 0);

        // 排序
        lambda.orderByAsc(CustomerEntity::getSortCode);
        lambda.orderByDesc(CustomerEntity::getCreatorTime);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public List<CustomerEntity> getListByCustType(String custType) {
        return customerMapper.selectByCustType(custType);
    }

    @Override
    public List<CustomerEntity> getListByCustLine(String custLine) {
        return customerMapper.selectByCustLine(custLine);
    }

    @Override
    public List<CustomerEntity> getListByLeader(String leader) {
        return customerMapper.selectByLeader(leader);
    }

    @Override
    public CustomerEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(CustomerEntity entity) {
        // 生成客户ID
        String id = RandomUtil.snowId();
        entity.setId(id);

        // 设置删除标记
        entity.setDeleteMark(0);

        // 设置排序码
        if (entity.getSortCode() == null) {
            entity.setSortCode(System.currentTimeMillis());
        }

        // 保存客户
        this.save(entity);

        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, CustomerEntity entity) {
        CustomerEntity oldEntity = this.getById(id);
        if (oldEntity == null) {
            throw new RuntimeException("客户不存在");
        }

        // 设置ID
        entity.setId(id);

        // 更新客户
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        CustomerEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("客户不存在");
        }

        // 逻辑删除
        entity.setDeleteMark(1);
        this.updateById(entity);
    }

    @Override
    public boolean isExistByName(String name, String excludeId) {
        int count = customerMapper.checkNameExists(name, excludeId);
        return count > 0;
    }

    @Override
    public List<CustomerEntity> getSelectList() {
        LambdaQueryWrapper<CustomerEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerEntity::getDeleteMark, 0);
        queryWrapper.orderByAsc(CustomerEntity::getSortCode);
        queryWrapper.orderByAsc(CustomerEntity::getName);
        return this.list(queryWrapper);
    }
}
