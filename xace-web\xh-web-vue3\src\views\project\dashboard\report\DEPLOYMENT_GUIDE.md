# 项目报表模块部署指南

## 🚀 快速开始

### 1. 前端部署
前端代码已经完成，无需额外安装依赖。

**已包含的文件：**
- ✅ `index.vue` - 报表中心主页
- ✅ `invoice/index.vue` - 已开票未收款报表
- ✅ `payment/index.vue` - 收款进度统计（占位）
- ✅ `payable/index.vue` - 付款统计报表（占位）
- ✅ `route.ts` - 路由配置
- ✅ API接口文件

### 2. 后端部署

**已包含的文件：**
- ✅ `ProjReportController.java` - 控制器
- ✅ `ProjReportService.java` - 服务接口
- ✅ `ProjReportServiceImpl.java` - 服务实现
- ✅ 所有VO模型类

**部署步骤：**
1. 确保后端服务正常运行
2. 数据库表结构已存在（使用现有的合同、收款等表）
3. 重启后端服务以加载新的Controller

### 3. 菜单配置

参考 `MENU_CONFIG.md` 文件，在后端管理系统中配置菜单：

```sql
-- 主菜单
INSERT INTO base_menu (id, parent_id, en_code, full_name, icon, url_address, type, sort_code, enabled_mark) 
VALUES ('report_main', '0', 'project.report', '项目报表', 'icon-ym icon-ym-chart', 'project/report', 1, 50, 1);

-- 报表中心
INSERT INTO base_menu (id, parent_id, en_code, full_name, icon, url_address, type, sort_code, enabled_mark) 
VALUES ('report_dashboard', 'report_main', 'project.report.dashboard', '报表中心', 'icon-ym icon-ym-dashboard', 'project/report/index', 2, 1, 1);

-- 已开票未收款统计
INSERT INTO base_menu (id, parent_id, en_code, full_name, icon, url_address, type, sort_code, enabled_mark) 
VALUES ('report_invoice', 'report_main', 'project.report.invoice', '已开票未收款统计', 'icon-ym icon-ym-invoice', 'project/report/invoice', 2, 2, 1);
```

### 4. 权限配置

为相应角色分配菜单权限：

```sql
-- 为管理员角色分配权限
INSERT INTO base_role_authorize (id, role_id, item_type, item_id) VALUES 
('auth_report_1', 'admin_role_id', 'menu', 'report_main'),
('auth_report_2', 'admin_role_id', 'menu', 'report_dashboard'),
('auth_report_3', 'admin_role_id', 'menu', 'report_invoice');
```

## 📋 功能验证

### 1. 报表中心主页
- **访问路径**: `/project/report`
- **验证内容**:
  - ✅ 统计概览卡片显示正常
  - ✅ 左侧导航菜单正常
  - ✅ 图表数据加载正常
  - ✅ 快速操作区域正常

### 2. 已开票未收款报表
- **访问路径**: `/project/report/invoice`
- **验证内容**:
  - ✅ 查询条件正常
  - ✅ 数据列表显示正常
  - ✅ 汇总统计正常
  - ✅ 导出功能正常

### 3. API接口测试

**仪表板接口：**
```bash
# 获取仪表板数据
GET /project/report/dashboard/data

# 获取收款趋势
GET /project/report/dashboard/payment-trend

# 获取合同状态分布
GET /project/report/dashboard/contract-status

# 获取部门排行
GET /project/report/dashboard/department-rank

# 获取商机转化漏斗
GET /project/report/dashboard/conversion-funnel
```

**报表接口：**
```bash
# 获取已开票未收款明细
POST /project/report/invoice/list

# 获取项目经理汇总
POST /project/report/invoice/summary

# 导出Excel
POST /project/report/invoice/export
```

## 🔧 故障排除

### 1. 页面无法访问
**问题**: 菜单不显示或页面404
**解决方案**:
1. 检查菜单配置是否正确
2. 检查用户权限是否分配
3. 检查前端组件路径是否正确

### 2. 图表不显示
**问题**: 图表区域空白
**解决方案**:
1. 检查ECharts是否正确加载
2. 检查API接口是否返回数据
3. 检查浏览器控制台错误信息

### 3. 数据不显示
**问题**: 接口调用失败或数据为空
**解决方案**:
1. 检查后端服务是否正常
2. 检查数据库连接是否正常
3. 检查SQL查询是否正确

### 4. 导出功能异常
**问题**: 导出按钮无响应或下载失败
**解决方案**:
1. 检查后端导出接口是否正常
2. 检查文件下载权限
3. 考虑集成EasyExcel完善导出功能

## 📈 性能优化建议

### 1. 前端优化
- 图表数据缓存
- 懒加载大数据量表格
- 图片和图标优化

### 2. 后端优化
- 数据库查询优化
- 添加适当的索引
- 考虑数据缓存

### 3. 用户体验优化
- 加载状态提示
- 错误信息友好化
- 响应式设计完善

## 🔮 后续扩展

### 1. 新增报表
1. 在对应目录创建Vue组件
2. 添加API接口
3. 配置菜单和权限
4. 更新文档

### 2. 功能增强
- 数据钻取功能
- 自定义报表模板
- 定时报表生成
- 移动端适配

### 3. 集成建议
- 集成EasyExcel优化导出
- 集成Redis缓存
- 集成消息队列处理大数据量

## 📞 技术支持

如遇到问题，请检查：
1. 浏览器控制台错误信息
2. 后端服务日志
3. 数据库连接状态
4. 菜单和权限配置

**常用调试命令：**
```bash
# 查看前端构建
npm run dev

# 查看后端日志
tail -f logs/application.log

# 检查数据库连接
mysql -u username -p database_name
```
