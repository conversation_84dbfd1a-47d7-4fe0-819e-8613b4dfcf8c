package com.xinghuo.project.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 项目与标签的关联实体类
 */
@Data
@TableName("zz_project_tag_rel")
public class ProjectTagRelEntity {

    @TableId("f_id")
    private String id;
    
    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 标签ID
     */
    @TableField("tag_id")
    private String tagId;

    /**
     * 打标人ID
     */
    @TableField("assigned_by")
    private String assignedBy;

    /**
     * 打标时间
     */
    @TableField("assigned_at")
    private Date assignedAt;
}
