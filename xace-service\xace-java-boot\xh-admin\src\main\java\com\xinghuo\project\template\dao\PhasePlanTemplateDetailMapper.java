package com.xinghuo.project.template.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.template.entity.PhasePlanTemplateDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 阶段计划模板明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface PhasePlanTemplateDetailMapper extends XHBaseMapper<PhasePlanTemplateDetailEntity> {

    /**
     * 根据模板ID查询阶段明细列表
     *
     * @param phasePlanTplId 模板ID
     * @return 阶段明细列表
     */
    List<PhasePlanTemplateDetailEntity> selectByTemplateId(@Param("phasePlanTplId") String phasePlanTplId);

    /**
     * 根据模板ID删除所有阶段明细
     *
     * @param phasePlanTplId 模板ID
     * @return 删除数量
     */
    int deleteByTemplateId(@Param("phasePlanTplId") String phasePlanTplId);

    /**
     * 批量插入阶段明细
     *
     * @param details 阶段明细列表
     * @return 插入数量
     */
    int batchInsert(@Param("details") List<PhasePlanTemplateDetailEntity> details);

    /**
     * 更新阶段序号
     *
     * @param id 阶段明细ID
     * @param seqNo 序号
     * @return 更新数量
     */
    int updateSeqNo(@Param("id") String id, @Param("seqNo") Integer seqNo);

    /**
     * 批量更新阶段序号
     *
     * @param details 阶段明细列表（包含ID和序号）
     * @return 更新数量
     */
    int batchUpdateSeqNo(@Param("details") List<PhasePlanTemplateDetailEntity> details);

    /**
     * 获取模板下一个序号
     *
     * @param phasePlanTplId 模板ID
     * @return 下一个序号
     */
    Integer getNextSeqNo(@Param("phasePlanTplId") String phasePlanTplId);

    /**
     * 检查阶段名称在模板内是否重复
     *
     * @param phasePlanTplId 模板ID
     * @param name 阶段名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkNameExistsInTemplate(@Param("phasePlanTplId") String phasePlanTplId, 
                                  @Param("name") String name, 
                                  @Param("excludeId") String excludeId);

    /**
     * 根据阶段编码查询标准阶段库中的信息
     *
     * @param code 阶段编码
     * @return 标准阶段信息
     */
    PhasePlanTemplateDetailEntity selectStandardPhaseByCode(@Param("code") String code);

    /**
     * 复制模板的所有阶段明细到新模板
     *
     * @param sourceTemplateId 源模板ID
     * @param targetTemplateId 目标模板ID
     * @return 复制数量
     */
    int copyPhaseDetails(@Param("sourceTemplateId") String sourceTemplateId, 
                        @Param("targetTemplateId") String targetTemplateId);
}
