package com.xinghuo.project.portfolio.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.portfolio.entity.ProgramEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目群Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface ProgramMapper extends XHBaseMapper<ProgramEntity> {

    /**
     * 根据项目群经理ID查询项目群列表
     *
     * @param managerId 项目群经理ID
     * @return 项目群列表
     */
    List<ProgramEntity> selectByManagerId(@Param("managerId") String managerId);

    /**
     * 根据项目群类型ID查询项目群列表
     *
     * @param typeId 项目群类型ID
     * @return 项目群列表
     */
    List<ProgramEntity> selectByTypeId(@Param("typeId") String typeId);

    /**
     * 根据状态查询项目群列表
     *
     * @param status 状态
     * @return 项目群列表
     */
    List<ProgramEntity> selectByStatus(@Param("status") String status);

    /**
     * 检查项目群编码是否存在
     *
     * @param code 项目群编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkCodeExists(@Param("code") String code, @Param("excludeId") String excludeId);
}
