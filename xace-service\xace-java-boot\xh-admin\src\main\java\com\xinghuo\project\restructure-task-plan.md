# 项目管理模块重构任务计划

## 阶段一：剩余实体与模型类迁移（预计耗时：2天）

### 任务1：迁移DTO类到新包结构
- 将 `com.xinghuo.project.model.*` 下的DTO类迁移到对应领域包的dto子包
- 按照命名规范重命名DTO类
- 更新字段引用和注解

### 任务2：迁移VO类到新包结构
- 将 `com.xinghuo.project.model.*` 下的VO类迁移到对应领域包的vo子包
- 按照命名规范重命名VO类
- 更新字段引用和注解

### 任务3：迁移枚举类和常量类
- 将剩余的枚举类迁移到对应领域包的enums子包
- 将常量类迁移到对应领域包的constants子包
- 更新引用关系

### 任务4：验证所有实体和模型类迁移完成
- 检查是否有遗漏的实体类或模型类
- 确保所有包结构符合设计要求
- 确保所有实体类继承了正确的基类

## 阶段二：更新Service和DAO层（预计耗时：3天）

### 任务5：创建新的Mapper接口
- 在对应领域包的dao子包中创建新的Mapper接口
- 继承XHBaseMapper并指定新的实体类
- 将原有的查询方法迁移到新接口

### 任务6：创建新的Service接口
- 在对应领域包的service子包中创建新的Service接口
- 继承BaseService并指定新的实体类
- 将原有的业务方法定义迁移到新接口

### 任务7：实现新的Service实现类
- 在对应领域包的service.impl子包中创建新的实现类
- 继承ExtendedBaseServiceImpl并指定新的Mapper和实体类
- 将原有的业务逻辑迁移到新实现类
- 更新依赖注入，使用新的Service和Mapper

### 任务8：更新MyBatis XML映射文件
- 在resources/mapper目录下创建对应的新XML文件
- 更新namespace指向新的Mapper接口
- 更新resultMap引用新的实体类
- 更新SQL语句中的表名和字段名

## 阶段三：更新Controller层（预计耗时：2天）

### 任务9：创建新的Controller类
- 在对应领域包的controller子包中创建新的Controller类
- 使用@RestController、@RequestMapping等注解配置路由
- 注入新的Service接口
- 将原有的接口方法迁移到新Controller

### 任务10：更新接口参数和返回值
- 将接口参数类型更新为新的DTO类
- 将返回值类型更新为新的VO类
- 确保所有接口遵循RESTful API设计规范
- 添加适当的Swagger文档注解

### 任务11：处理依赖注入和跨包引用
- 更新Controller中对其他Service的依赖注入
- 处理跨领域的业务调用关系
- 确保所有引用关系正确更新

## 阶段四：测试与验证（预计耗时：3天）

### 任务12：编写单元测试
- 为重构后的关键Service编写单元测试
- 测试基本的CRUD操作
- 测试复杂的业务逻辑

### 任务13：执行集成测试
- 测试Controller接口
- 验证数据库操作
- 验证跨模块调用

### 任务14：功能验证
- 验证主要业务流程
- 验证与其他模块的集成
- 验证性能和响应时间

## 阶段五：清理与完善（预计耗时：1天）

### 任务15：移除旧代码
- 确认新代码稳定运行后，移除旧的实体类
- 移除旧的Model类
- 移除旧的Controller、Service和Mapper类

### 任务16：代码优化
- 整理代码格式
- 添加或完善注释
- 解决静态代码分析工具发现的问题

### 任务17：文档更新
- 更新API文档
- 更新开发文档
- 编写重构总结报告

## 风险与应对策略

1. **表名与实体类不一致**
   - 使用@TableName注解明确指定表名
   - 确保XML中的SQL语句使用正确的表名

2. **旧接口依赖处理**
   - 可以考虑创建适配层或保留部分旧接口作为过渡
   - 使用继承或组合方式减少代码冗余

3. **数据迁移问题**
   - 编写测试脚本验证数据访问是否一致
   - 准备回滚方案

4. **多人协作冲突**
   - 明确分工，避免同一文件被多人同时修改
   - 频繁提交小改动，减少合并冲突
   - 定期同步进度，及时解决冲突

## 总体时间估计

- 总计预计耗时：11个工作日
- 建议添加20%的缓冲时间：约14个工作日
- 最终交付日期：自项目启动后约3周
