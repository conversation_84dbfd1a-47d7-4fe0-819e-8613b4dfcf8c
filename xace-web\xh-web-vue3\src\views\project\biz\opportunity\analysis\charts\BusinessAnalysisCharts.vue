<template>
  <div class="business-analysis-charts">
    <a-card :bordered="false" title="商机分析图表">
      <a-row :gutter="16" class="filter-row">
        <a-col :span="6">
          <a-form-item label="年份">
            <a-select v-model:value="queryParams.year" placeholder="请选择年份">
              <a-select-option :value="2024">2024年</a-select-option>
              <a-select-option :value="2025">2025年</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-button type="primary" @click="fetchData">
            <search-outlined />
            查询
          </a-button>
          <a-button style="margin-left: 8px" @click="resetQuery">
            <reload-outlined />
            重置
          </a-button>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <!-- 项目等级分布图 -->
        <a-col :span="12">
          <a-card :bordered="false" title="项目等级分布">
            <div ref="levelChartRef" style="height: 300px"></div>
          </a-card>
        </a-col>
        
        <!-- 商机状态分布图 -->
        <a-col :span="12">
          <a-card :bordered="false" title="商机状态分布">
            <div ref="statusChartRef" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-top: 16px">
        <!-- 月度商机趋势图 -->
        <a-col :span="24">
          <a-card :bordered="false" title="月度商机趋势">
            <div ref="trendChartRef" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-top: 16px">
        <!-- A+/A类商机金额对比 -->
        <a-col :span="24">
          <a-card :bordered="false" title="A+/A类商机金额对比">
            <div ref="compareChartRef" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, nextTick } from 'vue';
  import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getBusinessList } from '/@/api/project/business';
  import * as echarts from 'echarts/core';
  import { PieChart, BarChart, LineChart } from 'echarts/charts';
  import {
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
  } from 'echarts/components';
  import { CanvasRenderer } from 'echarts/renderers';

  // 注册必须的组件
  echarts.use([
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
    PieChart,
    BarChart,
    LineChart,
    CanvasRenderer,
  ]);

  const { createMessage } = useMessage();
  const loading = ref(false);

  // 图表DOM引用
  const levelChartRef = ref(null);
  const statusChartRef = ref(null);
  const trendChartRef = ref(null);
  const compareChartRef = ref(null);

  // 图表实例
  let levelChart = null;
  let statusChart = null;
  let trendChart = null;
  let compareChart = null;

  // 查询参数
  const queryParams = reactive({
    year: 2024,
  });

  // 获取数据
  async function fetchData() {
    loading.value = true;
    try {
      const { items } = await getBusinessList({
        year: queryParams.year,
        pageSize: 1000, // 获取足够多的数据进行分析
      });
      
      // 处理数据并渲染图表
      renderLevelChart(items);
      renderStatusChart(items);
      renderTrendChart(items);
      renderCompareChart(items);
      
    } catch (error) {
      console.error('获取商机数据失败:', error);
      createMessage.error('获取商机数据失败');
    } finally {
      loading.value = false;
    }
  }

  // 重置查询条件
  function resetQuery() {
    queryParams.year = 2024;
    fetchData();
  }

  // 渲染项目等级分布图
  function renderLevelChart(data) {
    // 统计各等级数量
    const levelCount = {
      'A+': 0,
      'A': 0,
      'B': 0,
      'C': 0,
      'D': 0,
    };
    
    data.forEach(item => {
      if (levelCount[item.projectLevel] !== undefined) {
        levelCount[item.projectLevel]++;
      }
    });
    
    // 转换为图表数据
    const chartData = Object.keys(levelCount).map(key => ({
      name: key,
      value: levelCount[key],
    }));
    
    // 初始化图表
    if (!levelChart) {
      levelChart = echarts.init(levelChartRef.value);
    }
    
    // 设置图表配置
    levelChart.setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: Object.keys(levelCount),
      },
      series: [
        {
          name: '项目等级',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: chartData,
          itemStyle: {
            color: function(params) {
              const colorMap = {
                'A+': '#ff4d4f',
                'A': '#fa8c16',
                'B': '#1890ff',
                'C': '#52c41a',
                'D': '#d9d9d9',
              };
              return colorMap[params.name] || '#1890ff';
            },
          },
        },
      ],
    });
  }

  // 渲染商机状态分布图
  function renderStatusChart(data) {
    // 统计各状态数量
    const statusCount = {
      '跟踪中': 0,
      '方案报价中': 0,
      '商务谈判中': 0,
      '已签': 0,
      '已废弃': 0,
      '明年跟踪': 0,
    };
    
    data.forEach(item => {
      if (statusCount[item.status] !== undefined) {
        statusCount[item.status]++;
      }
    });
    
    // 转换为图表数据
    const chartData = Object.keys(statusCount).map(key => ({
      name: key,
      value: statusCount[key],
    }));
    
    // 初始化图表
    if (!statusChart) {
      statusChart = echarts.init(statusChartRef.value);
    }
    
    // 设置图表配置
    statusChart.setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: Object.keys(statusCount),
      },
      series: [
        {
          name: '商机状态',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: chartData,
          itemStyle: {
            color: function(params) {
              const colorMap = {
                '跟踪中': '#1890ff',
                '方案报价中': '#13c2c2',
                '商务谈判中': '#fa8c16',
                '已签': '#52c41a',
                '已废弃': '#ff4d4f',
                '明年跟踪': '#722ed1',
              };
              return colorMap[params.name] || '#1890ff';
            },
          },
        },
      ],
    });
  }

  // 渲染月度商机趋势图
  function renderTrendChart(data) {
    // 按月统计商机数量
    const monthData = Array(12).fill(0);
    
    data.forEach(item => {
      if (item.createTime) {
        const month = new Date(item.createTime).getMonth();
        monthData[month]++;
      }
    });
    
    // 初始化图表
    if (!trendChart) {
      trendChart = echarts.init(trendChartRef.value);
    }
    
    // 设置图表配置
    trendChart.setOption({
      tooltip: {
        trigger: 'axis',
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          name: '商机数量',
          type: 'line',
          data: monthData,
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#1890ff',
          },
          lineStyle: {
            width: 3,
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(24, 144, 255, 0.5)',
              },
              {
                offset: 1,
                color: 'rgba(24, 144, 255, 0.1)',
              },
            ]),
          },
        },
      ],
    });
  }

  // 渲染A+/A类商机金额对比图
  function renderCompareChart(data) {
    // 按项目等级统计金额
    const aPlusData = [];
    const aClassData = [];
    
    // 获取所有部门
    const departments = [...new Set(data.map(item => item.deptName))].filter(Boolean);
    
    // 统计每个部门的A+和A类商机金额
    departments.forEach(dept => {
      const aPlusMoney = data
        .filter(item => item.projectLevel === 'A+' && item.deptName === dept)
        .reduce((sum, item) => sum + (item.yearMoney || 0), 0);
        
      const aClassMoney = data
        .filter(item => item.projectLevel === 'A' && item.deptName === dept)
        .reduce((sum, item) => sum + (item.yearMoney || 0), 0);
        
      aPlusData.push(aPlusMoney);
      aClassData.push(aClassMoney);
    });
    
    // 初始化图表
    if (!compareChart) {
      compareChart = echarts.init(compareChartRef.value);
    }
    
    // 设置图表配置
    compareChart.setOption({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {
        data: ['A+类商机', 'A类商机'],
      },
      xAxis: {
        type: 'category',
        data: departments.length ? departments : ['暂无数据'],
      },
      yAxis: {
        type: 'value',
        name: '金额(万元)',
      },
      series: [
        {
          name: 'A+类商机',
          type: 'bar',
          data: aPlusData,
          itemStyle: {
            color: '#ff4d4f',
          },
        },
        {
          name: 'A类商机',
          type: 'bar',
          data: aClassData,
          itemStyle: {
            color: '#fa8c16',
          },
        },
      ],
    });
  }

  // 窗口大小变化时重新调整图表大小
  function handleResize() {
    levelChart?.resize();
    statusChart?.resize();
    trendChart?.resize();
    compareChart?.resize();
  }

  // 组件挂载时初始化
  onMounted(() => {
    fetchData();
    window.addEventListener('resize', handleResize);
  });

  // 组件卸载时清理
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    levelChart?.dispose();
    statusChart?.dispose();
    trendChart?.dispose();
    compareChart?.dispose();
  });
</script>

<style lang="less" scoped>
.business-analysis-charts {
  .filter-row {
    margin-bottom: 16px;
  }
}
</style>
