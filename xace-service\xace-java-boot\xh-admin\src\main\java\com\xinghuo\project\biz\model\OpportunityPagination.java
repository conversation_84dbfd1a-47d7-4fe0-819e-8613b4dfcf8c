package com.xinghuo.project.biz.model;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 商机分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商机分页查询参数")
public class OpportunityPagination extends Pagination {

    /**
     * 商机编号
     */
    @Schema(description = "商机编号")
    private String businessNo;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String projectName;

    /**
     * 客户单位ID
     */
    @Schema(description = "客户单位ID")
    private String custId;

    /**
     * 项目等级
     */
    @Schema(description = "项目等级")
    private String projectLevel;

    /**
     * 项目负责人ID
     */
    @Schema(description = "项目负责人ID")
    private String projectLeader;

    /**
     * 商机状态
     */
    @Schema(description = "商机状态")
    private String status;

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    private String projType;

    /**
     * 所属分部ID
     */
    @Schema(description = "所属分部ID")
    private String deptId;

    /**
     * 研发分部ID
     */
    @Schema(description = "研发分部ID")
    private String yfDeptId;

    /**
     * 预计落地月份开始
     */
    @Schema(description = "预计落地月份开始")
    private String evaSignMonthStart;

    /**
     * 预计落地月份结束
     */
    @Schema(description = "预计落地月份结束")
    private String evaSignMonthEnd;

    /**
     * 预计启动日期开始
     */
    @Schema(description = "预计启动日期开始")
    private Date startDateStart;

    /**
     * 预计启动日期结束
     */
    @Schema(description = "预计启动日期结束")
    private Date startDateEnd;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始")
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束")
    private Date createTimeEnd;

    /**
     * 商机标签
     */
    @Schema(description = "商机标签")
    private String businessTag;

    /**
     * 工时填写状态
     */
    @Schema(description = "工时填写状态")
    private String workStatus;

    /**
     * 关键字搜索（项目名称或商机编号）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}
