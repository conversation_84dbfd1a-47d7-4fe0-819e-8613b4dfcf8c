package com.xinghuo.project.biz.model;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 合同分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "合同分页查询参数")
public class ContractPagination extends Pagination {

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String cno;

    /**
     * 合同名称
     */
    @Schema(description = "合同名称")
    private String name;

    /**
     * 客户单位ID
     */
    @Schema(description = "客户单位ID")
    private String custId;

    /**
     * 最终用户ID
     */
    @Schema(description = "最终用户ID")
    private String finalUserId;

    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID")
    private String ownId;

    /**
     * 合同状态
     */
    @Schema(description = "合同状态")
    private String contractStatus;

    /**
     * 收款状态
     */
    @Schema(description = "收款状态")
    private String moneyStatus;

    /**
     * 所属部门ID
     */
    @Schema(description = "所属部门ID")
    private String deptId;

    /**
     * 合同年度
     */
    @Schema(description = "合同年度")
    private Integer signYear;

    /**
     * 签订日期开始
     */
    @Schema(description = "签订日期开始")
    private Date signDateStart;

    /**
     * 签订日期结束
     */
    @Schema(description = "签订日期结束")
    private Date signDateEnd;

    /**
     * 合同开始日期开始
     */
    @Schema(description = "合同开始日期开始")
    private Date cstartDateStart;

    /**
     * 合同开始日期结束
     */
    @Schema(description = "合同开始日期结束")
    private Date cstartDateEnd;

    /**
     * 合同结束日期开始
     */
    @Schema(description = "合同结束日期开始")
    private Date cendDateStart;

    /**
     * 合同结束日期结束
     */
    @Schema(description = "合同结束日期结束")
    private Date cendDateEnd;

    /**
     * 是否续签
     */
    @Schema(description = "是否续签")
    private Integer isContinue;

    /**
     * 是否归档
     */
    @Schema(description = "是否归档")
    private Integer isArchive;

    /**
     * 类型状态
     */
    @Schema(description = "类型状态")
    private String typeStatus;

    /**
     * 外采状态
     */
    @Schema(description = "外采状态")
    private String externalStatus;

    /**
     * 分配状态
     */
    @Schema(description = "分配状态")
    private String assignStatus;

    /**
     * 工时填写状态
     */
    @Schema(description = "工时填写状态")
    private Integer workStatus;

    /**
     * 关键字搜索（合同名称或编号）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}
