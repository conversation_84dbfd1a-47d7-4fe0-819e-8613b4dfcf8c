package com.xinghuo.project.template.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.template.entity.WorkProductPlanTemplateDetailEntity;
import com.xinghuo.project.template.entity.WorkProductPlanTemplateEntity;
import com.xinghuo.project.template.model.WorkProductPlanTemplatePagination;
import com.xinghuo.project.template.model.dto.WorkProductPlanTemplateDTO;

import java.util.List;
import java.util.Map;

/**
 * 交付物计划模板服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface WorkProductPlanTemplateService extends BaseService<WorkProductPlanTemplateEntity> {

    /**
     * 获取交付物计划模板列表
     *
     * @param pagination 分页参数
     * @return 模板列表
     */
    List<WorkProductPlanTemplateDTO> getList(WorkProductPlanTemplatePagination pagination);

    /**
     * 根据知识状态获取模板列表
     *
     * @param knStatusId 知识状态ID
     * @return 模板列表
     */
    List<WorkProductPlanTemplateEntity> getListByKnStatus(String knStatusId);

    /**
     * 获取模板详情（包含交付物明细）
     *
     * @param id 模板ID
     * @return 模板详情
     */
    WorkProductPlanTemplateDTO getDetailInfo(String id);

    /**
     * 获取模板基本信息
     *
     * @param id 模板ID
     * @return 模板信息
     */
    WorkProductPlanTemplateEntity getInfo(String id);

    /**
     * 创建交付物计划模板
     *
     * @param templateDTO 模板信息（包含交付物明细）
     * @return 模板ID
     */
    String create(WorkProductPlanTemplateDTO templateDTO);

    /**
     * 更新交付物计划模板
     *
     * @param id 模板ID
     * @param templateDTO 模板信息（包含交付物明细）
     */
    void update(String id, WorkProductPlanTemplateDTO templateDTO);

    /**
     * 删除交付物计划模板
     *
     * @param id 模板ID
     */
    void delete(String id);

    /**
     * 批量删除交付物计划模板
     *
     * @param ids 模板ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 更新模板知识状态
     *
     * @param id 模板ID
     * @param knStatusId 知识状态ID
     */
    void updateKnStatus(String id, String knStatusId);

    /**
     * 批量更新知识状态
     *
     * @param ids 模板ID列表
     * @param knStatusId 知识状态ID
     */
    void batchUpdateKnStatus(List<String> ids, String knStatusId);

    /**
     * 发布模板
     *
     * @param id 模板ID
     */
    void publish(String id);

    /**
     * 归档模板
     *
     * @param id 模板ID
     */
    void archive(String id);

    /**
     * 检查模板名称是否存在
     *
     * @param name 模板名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByName(String name, String excludeId);

    /**
     * 获取模板选择列表
     *
     * @param keyword 关键字
     * @param knStatusId 知识状态ID
     * @return 模板列表
     */
    List<WorkProductPlanTemplateDTO> getSelectList(String keyword, String knStatusId);

    /**
     * 复制模板
     *
     * @param id 源模板ID
     * @param newName 新模板名称
     * @return 新模板ID
     */
    String copy(String id, String newName);

    /**
     * 从标准交付物库添加交付物
     *
     * @param templateId 模板ID
     * @param workProductLibraryIds 标准交付物库ID列表
     * @return 添加的交付物明细列表
     */
    List<WorkProductPlanTemplateDetailEntity> addWorkProductsFromLibrary(String templateId, List<String> workProductLibraryIds);

    /**
     * 更新交付物顺序
     *
     * @param templateId 模板ID
     * @param workProductOrders 交付物顺序列表（ID和序号的映射）
     */
    void updateWorkProductOrder(String templateId, List<Map<String, Object>> workProductOrders);

    /**
     * 获取模板统计信息
     *
     * @param params 查询参数
     * @return 统计信息
     */
    List<Map<String, Object>> getTemplateStatistics(Map<String, Object> params);

    /**
     * 根据项目模板ID查询关联的交付物计划模板
     *
     * @param projectTplId 项目模板ID
     * @return 交付物计划模板列表
     */
    List<WorkProductPlanTemplateEntity> getByProjectTemplateId(String projectTplId);

    /**
     * 更新模板与项目模板的关联关系
     *
     * @param templateId 模板ID
     * @param projectTemplateIds 项目模板ID列表
     */
    void updateProjectTemplateRelations(String templateId, List<String> projectTemplateIds);

    /**
     * 从项目交付物计划创建模板
     *
     * @param projectId 项目ID
     * @param templateName 模板名称
     * @param description 模板描述
     * @return 新模板ID
     */
    String createFromProject(String projectId, String templateName, String description);

    /**
     * 应用模板到项目
     *
     * @param templateId 模板ID
     * @param projectId 项目ID
     * @return 应用结果
     */
    Map<String, Object> applyToProject(String templateId, String projectId);

    /**
     * 获取模板使用情况
     *
     * @param id 模板ID
     * @return 使用情况统计
     */
    Map<String, Object> getTemplateUsageInfo(String id);

    /**
     * 根据阶段模板ID查询关联的交付物
     *
     * @param stageTemplateId 阶段模板ID
     * @return 交付物明细列表
     */
    List<WorkProductPlanTemplateDetailEntity> getWorkProductsByStageTemplate(String stageTemplateId);

    /**
     * 根据活动模板ID查询关联的交付物
     *
     * @param activityTemplateId 活动模板ID
     * @return 交付物明细列表
     */
    List<WorkProductPlanTemplateDetailEntity> getWorkProductsByActivityTemplate(String activityTemplateId);
}
