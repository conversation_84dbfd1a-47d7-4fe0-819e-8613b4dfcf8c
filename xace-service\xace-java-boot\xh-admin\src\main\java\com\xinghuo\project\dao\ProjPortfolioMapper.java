package com.xinghuo.project.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.entity.ProjPortfolioEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目组合Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface ProjPortfolioMapper extends XHBaseMapper<ProjPortfolioEntity> {

    /**
     * 根据负责人ID查询组合列表
     *
     * @param ownerId 负责人ID
     * @return 组合列表
     */
    List<ProjPortfolioEntity> selectByOwnerId(@Param("ownerId") String ownerId);

    /**
     * 根据组合类型ID查询组合列表
     *
     * @param typeId 组合类型ID
     * @return 组合列表
     */
    List<ProjPortfolioEntity> selectByTypeId(@Param("typeId") String typeId);

    /**
     * 根据状态查询组合列表
     *
     * @param status 状态
     * @return 组合列表
     */
    List<ProjPortfolioEntity> selectByStatus(@Param("status") String status);

    /**
     * 检查组合编码是否存在
     *
     * @param code 组合编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkCodeExists(@Param("code") String code, @Param("excludeId") String excludeId);
}
