package com.xinghuo.project.biz.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.biz.entity.OpportunityEntity;
import com.xinghuo.project.biz.model.OpportunityPagination;
import com.xinghuo.project.biz.model.OpportunityStatusForm;
import com.xinghuo.project.biz.service.OpportunityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 商机管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "商机管理", description = "商机管理相关接口")
@RestController
@RequestMapping("/api/project/biz/opportunity")
public class OpportunityController {

    @Resource
    private OpportunityService opportunityService;

    /**
     * 获取商机列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取商机列表")
    public ActionResult<PageListVO<OpportunityEntity>> list(@RequestBody OpportunityPagination pagination) {
        List<OpportunityEntity> list = opportunityService.getList(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 根据客户ID获取商机列表
     */
    @GetMapping("/getListByCustId/{custId}")
    @Operation(summary = "根据客户ID获取商机列表")
    public ActionResult<List<OpportunityEntity>> getListByCustId(
            @Parameter(description = "客户ID") @PathVariable String custId) {
        List<OpportunityEntity> list = opportunityService.getListByCustId(custId);
        return ActionResult.success(list);
    }

    /**
     * 获取商机详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取商机详情")
    public ActionResult<OpportunityEntity> getInfo(
            @Parameter(description = "商机ID") @PathVariable String id) {
        OpportunityEntity entity = opportunityService.getInfo(id);
        return ActionResult.success(entity);
    }

    /**
     * 创建商机
     */
    @PostMapping("/create")
    @Operation(summary = "创建商机")
    public ActionResult<String> create(@RequestBody @Valid OpportunityEntity entity) {
        String id = opportunityService.create(entity);
        return ActionResult.success("创建成功", id);
    }

    /**
     * 更新商机
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新商机")
    public ActionResult<String> update(
            @Parameter(description = "商机ID") @PathVariable String id,
            @RequestBody @Valid OpportunityEntity entity) {
        opportunityService.update(id, entity, true);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除商机
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除商机")
    public ActionResult<String> delete(
            @Parameter(description = "商机ID") @PathVariable String id) {
        opportunityService.delete(id);
        return ActionResult.success("删除成功");
    }

    /**
     * 更新商机状态
     */
    @PutMapping("/updateStatus/{id}")
    @Operation(summary = "更新商机状态")
    public ActionResult<String> updateStatus(
            @Parameter(description = "商机ID") @PathVariable String id,
            @RequestBody @Valid OpportunityStatusForm form) {
        opportunityService.updateStatus(id, form);
        return ActionResult.success("状态更新成功");
    }

    /**
     * 更新最后跟踪记录
     */
    @PutMapping("/updateLastNote/{id}")
    @Operation(summary = "更新最后跟踪记录")
    public ActionResult<String> updateLastNote(
            @Parameter(description = "商机ID") @PathVariable String id,
            @RequestParam String lastNote) {
        opportunityService.updateLastNote(id, lastNote);
        return ActionResult.success("跟踪记录更新成功");
    }

    /**
     * 更新工时填写状态
     */
    @PutMapping("/updateWorkStatus/{id}")
    @Operation(summary = "更新工时填写状态")
    public ActionResult<String> updateWorkStatus(
            @Parameter(description = "商机ID") @PathVariable String id,
            @RequestParam Integer workStatus) {
        opportunityService.updateWorkStatus(id, workStatus);
        return ActionResult.success("工时状态更新成功");
    }

    /**
     * 商机转合同
     */
    @PostMapping("/convertToContract/{id}")
    @Operation(summary = "商机转合同")
    public ActionResult<String> convertToContract(
            @Parameter(description = "商机ID") @PathVariable String id,
            @RequestParam String projectNo) {
        String contractId = opportunityService.convertToContract(id, projectNo);
        return ActionResult.success("转换成功", contractId);
    }

    /**
     * 获取销售漏斗数据
     */
    @PostMapping("/getSalesFunnelData")
    @Operation(summary = "获取销售漏斗数据")
    public ActionResult<List<Map<String, Object>>> getSalesFunnelData(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> data = opportunityService.getSalesFunnelData(params);
        return ActionResult.success(data);
    }

    /**
     * 获取商机预测数据
     */
    @PostMapping("/getBusinessForecastData")
    @Operation(summary = "获取商机预测数据")
    public ActionResult<List<Map<String, Object>>> getBusinessForecastData(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> data = opportunityService.getBusinessForecastData(params);
        return ActionResult.success(data);
    }

    /**
     * 获取赢单/输单分析数据
     */
    @PostMapping("/getWinLoseAnalysisData")
    @Operation(summary = "获取赢单/输单分析数据")
    public ActionResult<List<Map<String, Object>>> getWinLoseAnalysisData(@RequestBody Map<String, Object> params) {
        List<Map<String, Object>> data = opportunityService.getWinLoseAnalysisData(params);
        return ActionResult.success(data);
    }
}
