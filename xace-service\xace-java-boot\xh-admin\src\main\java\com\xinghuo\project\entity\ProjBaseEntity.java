package com.xinghuo.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目基础信息实体类
 * 对应数据库表：zz_proj_base
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_base")
public class ProjBaseEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 项目编码，可自动生成
     */
    @TableField("code")
    private String code;

    /**
     * 项目名称
     */
    @TableField("name")
    private String name;

    /**
     * 项目类型ID (关联字典表, 核心分类字段)
     */
    @TableField("type_id")
    private String typeId;

    /**
     * 项目经理ID (关联用户表)
     */
    @TableField("manager_id")
    private String managerId;

    /**
     * 所属项目群ID (关联 zz_proj_program.f_id)
     */
    @TableField("program_id")
    private String programId;

    /**
     * 项目状态ID (关联字典表)
     */
    @TableField("status_id")
    private String statusId;

    /**
     * 项目健康状态 (green, yellow, red)
     */
    @TableField("health_status")
    private String healthStatus;

    /**
     * 项目总体进度% (由PM手动更新)
     */
    @TableField("progress")
    private Integer progress;

    /**
     * 计划开始日期
     */
    @TableField("plan_start_date")
    private Date planStartDate;

    /**
     * 计划结束日期
     */
    @TableField("plan_end_date")
    private Date planEndDate;

    /**
     * 实际开始日期
     */
    @TableField("actual_start_date")
    private Date actualStartDate;

    /**
     * 实际结束日期
     */
    @TableField("actual_end_date")
    private Date actualEndDate;

    /**
     * 项目描述
     */
    @TableField("description")
    private String description;

    /**
     * 预算-人力成本
     */
    @TableField("budget_cost_labor")
    private BigDecimal budgetCostLabor;

    /**
     * 预算-采购成本
     */
    @TableField("budget_cost_purchase")
    private BigDecimal budgetCostPurchase;

    /**
     * 预算-差旅成本
     */
    @TableField("budget_cost_travel")
    private BigDecimal budgetCostTravel;

    /**
     * 实际-人力成本
     */
    @TableField("actual_cost_labor")
    private BigDecimal actualCostLabor;

    /**
     * 实际-采购成本
     */
    @TableField("actual_cost_purchase")
    private BigDecimal actualCostPurchase;

    /**
     * 实际-差旅成本
     */
    @TableField("actual_cost_travel")
    private BigDecimal actualCostTravel;
}
