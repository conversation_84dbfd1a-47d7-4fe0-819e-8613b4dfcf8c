import { RouteRecordRaw } from 'vue-router';

const portfolioRoutes: RouteRecordRaw[] = [
  {
    path: '/project/portfolio',
    name: 'ProjectPortfolio',
    meta: {
      title: '项目组合管理',
      icon: 'AppstoreOutlined',
    },
    children: [
      {
        path: 'portfolio',
        name: 'PortfolioManagement',
        component: () => import('./portfolio/index.vue'),
        meta: {
          title: '项目组合',
          icon: 'FolderOutlined',
        },
      },
      {
        path: 'program',
        name: 'ProgramManagement',
        component: () => import('./program/index.vue'),
        meta: {
          title: '项目群',
          icon: 'ClusterOutlined',
        },
      },
    ],
  },
];

export default portfolioRoutes;
