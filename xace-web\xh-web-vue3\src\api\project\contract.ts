import { defHttp } from '/@/utils/http/axios';
import { ListResult } from '/@/api/model/baseModel';

/**
 * 合同管理API
 */

// API URL前缀 - 更新为新的架构路径
const API_PREFIX = '/api/project/biz/contract';

/**
 * 合同对象接口
 */
export interface ContractModel {
  id: string;
  cId: string;
  name: string;
  cno: string;
  custId: string;
  custName?: string;
  finalUserId: string;
  finalUserName?: string;
  ownId: string;
  ownName?: string;
  reportFrequency: string;
  amount: number;
  ysAmount: number;
  yearYsAmount: number;
  externalAmount: number;
  moneyStatus: string;
  contractStatus: string;
  typeStatus: string;
  note: string;
  deptId: string;
  deptName?: string;
  linkman: string;
  linkTelephone: string;
  signDate: string;
  bidDate: string;
  commencementDate: string;
  initialCheckDate: string;
  finalCheckDate: string;
  auditDate: string;
  signYear: number;
  cstartDate: string;
  cendDate: string;
  mstartDate: string;
  mendDate: string;
  estProbit: number;
  actProbit: number;
  estProbitRatio: number;
  actProbitRatio: number;
  evaCostAmount: number;
  actCostAmount: number;
  evaExternalAmount: number;
  actExternalAmount: number;
  unsignExternalAmount: number;
  externalStatus: string;
  yfYbAmount: number;
  yfEbAmount: number;
  yfJfAmount: number;
  yfOtherAmount: number;
  outYbAmount: number;
  outEbAmount: number;
  outJfAmount: number;
  outOtherAmount: number;
  unsignOutYbAmount: number;
  unsignOutEbAmount: number;
  unsignOutJfAmount: number;
  unsignOutOtherAmount: number;
  outYfYbAmount: number;
  outYfEbAmount: number;
  outYfJfAmount: number;
  outYfOtherAmount: number;
  isContinue: number;
  isContinueText?: string;
  contractStatusText?: string;
  moneyStatusText?: string;
  typeStatusText?: string;
  externalStatusText?: string;
  // 外部单位/干系人信息
  svDeptId?: string;
  svDeptName?: string;
  svLinkman?: string;
  svTelephone?: string;
  reviewDeptId?: string;
  reviewDeptName?: string;
  reviewLinkman?: string;
  reviewTelephone?: string;
  dbDeptId?: string;
  dbDeptName?: string;
  dbLinkman?: string;
  dbTelephone?: string;
  smDeptId?: string;
  smDeptName?: string;
  smLinkman?: string;
  smTelephone?: string;
  jsDeptId?: string;
  jsDeptName?: string;
  jsLinkman?: string;
  jsTelephone?: string;
  createUserId: string;
  createTime: string;
  lastModifiedUserId: string;
  updateTime: string;
  // 新增字段
  signYear?: number; // 合同年度
  newCid?: string; // 续签合同ID
}

/**
 * 合同表单接口
 */
export interface ContractFormModel {
  name: string;
  cno: string;
  custId?: string;
  finalUserId?: string;
  ownId: string;
  reportFrequency?: string;
  amount: number;
  externalAmount?: number;
  moneyStatus?: string;
  contractStatus?: string;
  typeStatus?: string;
  externalStatus?: string;
  note?: string;
  deptId: string;
  linkman?: string;
  linkTelephone?: string;
  signDate?: string;
  bidDate?: string;
  commencementDate?: string;
  initialCheckDate?: string;
  finalCheckDate?: string;
  auditDate?: string;
  cstartDate?: string;
  cendDate?: string;
  mstartDate?: string;
  mendDate?: string;
  evaExternalAmount?: number;
  evaCostAmount?: number;
  actExternalAmount?: number;
  actCostAmount?: number;
  unsignExternalAmount?: number;
  estProbit?: number;
  actProbit?: number;
  estProbitRatio?: number;
  actProbitRatio?: number;
  yfYbAmount?: number;
  yfEbAmount?: number;
  yfJfAmount?: number;
  yfOtherAmount?: number;
  outYbAmount?: number;
  outEbAmount?: number;
  outJfAmount?: number;
  outOtherAmount?: number;
  unsignOutYbAmount?: number;
  unsignOutEbAmount?: number;
  unsignOutJfAmount?: number;
  unsignOutOtherAmount?: number;
  outYfYbAmount?: number;
  outYfEbAmount?: number;
  outYfJfAmount?: number;
  outYfOtherAmount?: number;
}

/**
 * 合同查询参数接口
 */
export interface ContractQueryParams {
  name?: string;
  cno?: string;
  custId?: string;
  ownId?: string;
  contractStatus?: string;
  moneyStatus?: string;
  signYear?: number;
  minAmount?: number;
  maxAmount?: number;
  signDateStart?: string;
  signDateEnd?: string;
  deptId?: string;
  keyword?: string;
  pageSize?: number;
  currentPage?: number;
}

/**
 * 合同日期更新接口
 */
export interface ContractDateUpdateModel {
  dateType: string;
  newDate: string;
  note?: string;
}

/**
 * 获取合同列表
 * @param params 查询参数
 * @returns 合同列表
 */
export const getContractList = (params?: ContractQueryParams) => {
  return defHttp.post<ListResult<ContractModel>>({
    url: `${API_PREFIX}/getList`,
    data: params,
  });
};

/**
 * 根据客户ID获取合同列表
 * @param customerId 客户ID
 * @returns 合同列表
 */
export const getContractListByCustomerId = (customerId: string) => {
  return defHttp.get<ContractModel[]>({
    url: `${API_PREFIX}/getListByCustId/${customerId}`,
  });
};

/**
 * 获取合同详情
 * @param id 合同ID
 * @returns 合同详情
 */
export const getContractInfo = (id: string) => {
  return defHttp.get<ContractModel>({
    url: `${API_PREFIX}/getInfo/${id}`,
  });
};

/**
 * 创建合同
 * @param params 合同创建参数
 * @returns 操作结果
 */
export const createContract = (params: ContractFormModel) => {
  return defHttp.post<void>({
    url: `${API_PREFIX}/create`,
    data: params,
  });
};

/**
 * 更新合同
 * @param id 合同ID
 * @param params 合同更新参数
 * @returns 操作结果
 */
export const updateContract = (id: string, params: ContractFormModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/update/${id}`,
    data: params,
  });
};

/**
 * 删除合同
 * @param id 合同ID
 * @returns 操作结果
 */
export const deleteContract = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 更新合同状态
 * @param id 合同ID
 * @param status 合同状态
 * @returns 操作结果
 */
export const updateContractStatus = (id: string, status: string) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}/status/${status}`,
  });
};

/**
 * 更新合同日期
 * @param id 合同ID
 * @param params 日期更新参数
 * @returns 操作结果
 */
export const updateContractDate = (id: string, params: ContractDateUpdateModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}/date`,
    data: params,
  });
};

/**
 * 合同选择器选项接口
 */
export interface ContractSelectorOption {
  id: string;
  fullName: string;
  cNo: string;
  name: string;
}

/**
 * 获取合同选择器列表
 * @param keyword 关键字搜索
 * @returns 合同选择器列表
 */
export const getContractSelector = (keyword?: string) => {
  return defHttp.get<ContractSelectorOption[]>({
    url: `${API_PREFIX}/selector`,
    params: { keyword },
  });
};
