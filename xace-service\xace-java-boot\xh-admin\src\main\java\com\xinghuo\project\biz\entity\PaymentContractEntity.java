package com.xinghuo.project.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目采购合同实体类
 * 对应数据库表：zz_proj_paycontract
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode
@TableName("zz_proj_paycontract")
public class PaymentContractEntity {

    /**
     * 采购合同ID
     */
    @TableId("pc_id")
    private String pcId;

    /**
     * 合同名称
     */
    @TableField("name")
    private String name;

    /**
     * 采购合同编号
     */
    @TableField("c_no")
    private String cno;

    /**
     * 收款合同ID
     */
    @TableField("c_id")
    private String cid;

    /**
     * 供应商ID
     */
    @TableField("suppiler_id")
    private String suppilerId;

    /**
     * 负责人ID
     */
    @TableField("own_id")
    private String ownId;

    /**
     * 合同金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 签订日期
     */
    @TableField("sign_date")
    private Date signDate;

    /**
     * 签署年份
     */
    @TableField("sign_year")
    private Integer signYear;

    /**
     * 预计签订日期
     */
    @TableField("est_sign_date")
    private Date estSignDate;

    /**
     * 已付金额
     */
    @TableField("yf_amount")
    private BigDecimal yfAmount;

    /**
     * 开发一部金额
     */
    @TableField("kfyb_amount")
    private BigDecimal kfybAmount;

    /**
     * 开发二部金额
     */
    @TableField("kfeb_amount")
    private BigDecimal kfebAmount;

    /**
     * 综合金额
     */
    @TableField("other_amount")
    private BigDecimal otherAmount;

    /**
     * 收款状态
     */
    @TableField("money_status")
    private String moneyStatus;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 创建用户
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最后修改人
     */
    @TableField("last_modified_user_id")
    private String lastModifiedUserId;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 租户ID
     */
    @TableField("f_tenantid")
    private String tenantId;

    /**
     * 流程ID
     */
    @TableField("f_flowid")
    private String flowId;

    /**
     * 删除标志
     */
    @TableField("f_deletemark")
    private Integer deleteMark;
}
