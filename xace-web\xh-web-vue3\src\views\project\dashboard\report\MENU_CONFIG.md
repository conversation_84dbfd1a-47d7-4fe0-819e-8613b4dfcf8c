# 项目报表菜单配置说明

## 概述

本项目使用动态路由系统，所有菜单和路由都是从后端数据库配置生成的。要启用报表功能，需要在后端系统中配置相应的菜单项。

## 菜单配置结构

### 1. 主菜单：项目报表
```sql
INSERT INTO base_menu (
  id, parent_id, en_code, full_name, icon, url_address, 
  type, sort_code, enabled_mark, description
) VALUES (
  'report_main', '0', 'project.report', '项目报表', 
  'icon-ym icon-ym-chart', 'project/report', 
  1, 50, 1, '项目报表中心'
);
```

### 2. 子菜单配置

#### 2.1 报表中心（主页面）
```sql
INSERT INTO base_menu (
  id, parent_id, en_code, full_name, icon, url_address, 
  type, sort_code, enabled_mark, description
) VALUES (
  'report_dashboard', 'report_main', 'project.report.dashboard', '报表中心', 
  'icon-ym icon-ym-dashboard', 'project/report/index', 
  2, 1, 1, '报表统计概览'
);
```

#### 2.2 已开票未收款统计
```sql
INSERT INTO base_menu (
  id, parent_id, en_code, full_name, icon, url_address, 
  type, sort_code, enabled_mark, description
) VALUES (
  'report_invoice', 'report_main', 'project.report.invoice', '已开票未收款统计', 
  'icon-ym icon-ym-invoice', 'project/report/invoice', 
  2, 2, 1, '已开票未收款明细统计'
);
```

#### 2.3 收款进度统计（预留）
```sql
INSERT INTO base_menu (
  id, parent_id, en_code, full_name, icon, url_address, 
  type, sort_code, enabled_mark, description
) VALUES (
  'report_payment', 'report_main', 'project.report.payment', '收款进度统计', 
  'icon-ym icon-ym-payment', 'project/report/payment', 
  2, 3, 0, '收款进度统计报表'
);
```

#### 2.4 付款统计报表（预留）
```sql
INSERT INTO base_menu (
  id, parent_id, en_code, full_name, icon, url_address, 
  type, sort_code, enabled_mark, description
) VALUES (
  'report_payable', 'report_main', 'project.report.payable', '付款统计报表', 
  'icon-ym icon-ym-payable', 'project/report/payable', 
  2, 4, 0, '付款统计报表'
);
```

## 字段说明

| 字段名 | 说明 | 示例值 |
|--------|------|--------|
| id | 菜单唯一标识 | report_main |
| parent_id | 父菜单ID，顶级菜单为'0' | 0 |
| en_code | 英文编码，用于路由生成 | project.report |
| full_name | 菜单显示名称 | 项目报表 |
| icon | 菜单图标类名 | icon-ym icon-ym-chart |
| url_address | 前端组件路径 | project/report |
| type | 菜单类型：1=目录，2=页面 | 1 |
| sort_code | 排序号 | 50 |
| enabled_mark | 是否启用：1=启用，0=禁用 | 1 |
| description | 菜单描述 | 项目报表中心 |

## 组件路径映射

| 菜单路径 | 前端组件文件 | 功能说明 |
|----------|-------------|----------|
| project/report | /views/project/report/index.vue | 报表中心主页 |
| project/report/invoice | /views/project/report/invoice/index.vue | 已开票未收款统计 |
| project/report/payment | /views/project/report/payment/index.vue | 收款进度统计 |
| project/report/payable | /views/project/report/payable/index.vue | 付款统计报表 |

## 权限配置

### 1. 角色权限
需要为相应的角色分配报表菜单权限：

```sql
-- 为管理员角色分配报表权限
INSERT INTO base_role_authorize (
  id, role_id, item_type, item_id
) VALUES 
('auth_report_1', 'admin_role_id', 'menu', 'report_main'),
('auth_report_2', 'admin_role_id', 'menu', 'report_dashboard'),
('auth_report_3', 'admin_role_id', 'menu', 'report_invoice');
```

### 2. 用户权限
确保用户具有相应的角色权限。

## 图标配置

项目使用的图标类名：
- `icon-ym icon-ym-chart` - 图表图标
- `icon-ym icon-ym-dashboard` - 仪表板图标
- `icon-ym icon-ym-invoice` - 发票图标
- `icon-ym icon-ym-payment` - 收款图标
- `icon-ym icon-ym-payable` - 付款图标

## 启用步骤

1. **执行SQL脚本**：在数据库中执行上述菜单配置SQL
2. **分配权限**：为相应角色分配菜单权限
3. **重新登录**：用户重新登录以获取最新菜单
4. **验证功能**：访问报表菜单验证功能正常

## 注意事项

1. **菜单ID唯一性**：确保菜单ID在系统中唯一
2. **路径正确性**：url_address必须与前端组件路径匹配
3. **权限完整性**：确保用户具有访问权限
4. **排序合理性**：sort_code决定菜单显示顺序
5. **启用状态**：enabled_mark控制菜单是否显示

## 扩展说明

当需要添加新的报表时：
1. 在 `/views/project/report/` 下创建对应的组件
2. 在数据库中添加相应的菜单配置
3. 分配必要的权限
4. 更新本文档

## 故障排除

### 菜单不显示
1. 检查菜单是否启用（enabled_mark=1）
2. 检查用户是否有权限
3. 检查父子菜单关系是否正确

### 页面无法访问
1. 检查url_address路径是否正确
2. 检查前端组件文件是否存在
3. 检查路由配置是否正确

### 图标不显示
1. 检查图标类名是否正确
2. 确认图标资源已加载
