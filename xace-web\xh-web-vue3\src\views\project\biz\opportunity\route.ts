import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

// 商机管理路由配置
export default {
  path: '/project/business',
  name: 'ProjectBusiness',
  component: LAYOUT,
  redirect: '/project/business/index',
  meta: {
    orderNo: 20,
    icon: 'icon-ym icon-ym-business',
    title: t('商机管理'),
    defaultTitle: '商机管理',
  },
  children: [
    {
      path: 'index',
      name: 'ProjectBusinessIndex',
      component: () => import('/@/views/project/business/index.vue'),
      meta: {
        title: t('商机列表'),
        defaultTitle: '商机列表',
        icon: 'icon-ym icon-ym-business',
      },
    },
    {
      path: 'detail/:id',
      name: 'ProjectBusinessDetail',
      component: () => import('/@/views/project/business/Detail.vue'),
      meta: {
        title: t('商机详情'),
        defaultTitle: '商机详情',
        hideMenu: true,
      },
    },
    {
      path: 'analysis',
      name: 'ProjectBusinessAnalysis',
      component: () => import('/@/views/project/business/analysis/index.vue'),
      meta: {
        title: t('商机分析'),
        defaultTitle: '商机分析',
        icon: 'icon-ym icon-ym-chart',
      },
    },
  ],
};
