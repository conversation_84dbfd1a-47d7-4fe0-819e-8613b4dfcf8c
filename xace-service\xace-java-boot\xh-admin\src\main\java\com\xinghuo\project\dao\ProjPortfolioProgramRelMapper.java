package com.xinghuo.project.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.entity.ProjPortfolioProgramRelEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组合-项目群关联表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface ProjPortfolioProgramRelMapper extends XHBaseMapper<ProjPortfolioProgramRelEntity> {

    /**
     * 根据组合ID查询关联的项目群列表
     *
     * @param portfolioId 组合ID
     * @return 关联记录列表
     */
    List<ProjPortfolioProgramRelEntity> selectByPortfolioId(@Param("portfolioId") String portfolioId);

    /**
     * 根据项目群ID查询关联的组合列表
     *
     * @param programId 项目群ID
     * @return 关联记录列表
     */
    List<ProjPortfolioProgramRelEntity> selectByProgramId(@Param("programId") String programId);

    /**
     * 检查组合和项目群的关联关系是否存在
     *
     * @param portfolioId 组合ID
     * @param programId 项目群ID
     * @return 数量
     */
    int checkRelationExists(@Param("portfolioId") String portfolioId, @Param("programId") String programId);

    /**
     * 删除组合的所有项目群关联
     *
     * @param portfolioId 组合ID
     * @return 删除数量
     */
    int deleteByPortfolioId(@Param("portfolioId") String portfolioId);

    /**
     * 删除项目群的所有组合关联
     *
     * @param programId 项目群ID
     * @return 删除数量
     */
    int deleteByProgramId(@Param("programId") String programId);

    /**
     * 批量插入组合-项目群关联关系
     *
     * @param relations 关联关系列表
     * @return 插入数量
     */
    int batchInsert(@Param("relations") List<ProjPortfolioProgramRelEntity> relations);
}
