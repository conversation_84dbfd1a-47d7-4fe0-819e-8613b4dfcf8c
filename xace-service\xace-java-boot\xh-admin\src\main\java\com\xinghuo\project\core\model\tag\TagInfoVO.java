package com.xinghuo.project.core.model.tag;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 标签详情视图对象
 */
@Data
@Schema(description = "标签详情视图对象")
public class TagInfoVO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private String id;

    /**
     * 标签名称
     */
    @Schema(description = "标签名称")
    private String tagName;

    /**
     * 标签颜色 (例如: "#FF0000" 代表红色)
     */
    @Schema(description = "标签颜色 (例如: \"#FF0000\" 代表红色)")
    private String tagColor;

    /**
     * 标签描述
     */
    @Schema(description = "标签描述")
    private String description;

    /**
     * 标签范围 ('global': 所有用户可见, 'user_specific': 特定用户或组可见)
     */
    @Schema(description = "标签范围 ('global': 所有用户可见, 'user_specific': 特定用户或组可见)")
    private String scope;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    private String updateBy;
}
