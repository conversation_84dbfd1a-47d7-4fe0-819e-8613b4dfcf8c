package com.xinghuo.project.biz.model;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商分页查询参数")
public class SupplierPagination extends Pagination {

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String name;

    /**
     * 联系人
     */
    @Schema(description = "联系人")
    private String linkman;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String telephone;

    /**
     * 关键字搜索（供应商名称或联系人）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}
