<template>
  <div class="program-container">
    <div class="program-header">
      <h2>项目群管理</h2>
      <a-button type="primary" @click="handleAdd">
        <template #icon><PlusOutlined /></template>
        新增项目群
      </a-button>
    </div>

    <div class="program-search">
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <a-form-item label="项目群编码" name="code">
          <a-input v-model:value="searchForm.code" placeholder="请输入项目群编码" />
        </a-form-item>
        <a-form-item label="项目群名称" name="name">
          <a-input v-model:value="searchForm.name" placeholder="请输入项目群名称" />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-select v-model:value="searchForm.status" placeholder="请选择状态" style="width: 120px">
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="planning">规划中</a-select-option>
            <a-select-option value="executing">执行中</a-select-option>
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="suspended">已暂停</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit">查询</a-button>
          <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <div class="program-table">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">查看</a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
              <a-popconfirm title="确定要删除这个项目群吗？" @confirm="handleDelete(record)">
                <a-button type="link" size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 新增/编辑弹窗 -->
    <ProgramFormModal
      v-model:visible="formModalVisible"
      :form-data="currentRecord"
      @success="handleFormSuccess"
    />

    <!-- 详情弹窗 -->
    <ProgramDetailModal
      v-model:visible="detailModalVisible"
      :program-id="currentRecord?.id"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { getProgramList, deleteProgram } from '/@/api/project/program';
import ProgramFormModal from './components/ProgramFormModal.vue';
import ProgramDetailModal from './components/ProgramDetailModal.vue';

// 响应式数据
const loading = ref(false);
const dataSource = ref([]);
const formModalVisible = ref(false);
const detailModalVisible = ref(false);
const currentRecord = ref(null);

// 搜索表单
const searchForm = reactive({
  code: '',
  name: '',
  status: '',
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
});

// 表格列配置
const columns = [
  {
    title: '项目群编码',
    dataIndex: 'code',
    key: 'code',
    width: 120,
  },
  {
    title: '项目群名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
  },
  {
    title: '项目群经理',
    dataIndex: 'managerName',
    key: 'managerName',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '预算',
    dataIndex: 'budget',
    key: 'budget',
    width: 120,
    customRender: ({ text }) => text ? `¥${text.toLocaleString()}` : '-',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 150,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
  },
];

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm,
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    };
    const result = await getProgramList(params);
    dataSource.value = result.data.list;
    pagination.total = result.data.pagination.total;
  } catch (error) {
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    code: '',
    name: '',
    status: '',
  });
  pagination.current = 1;
  loadData();
};

const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadData();
};

const handleAdd = () => {
  currentRecord.value = null;
  formModalVisible.value = true;
};

const handleEdit = (record) => {
  currentRecord.value = record;
  formModalVisible.value = true;
};

const handleView = (record) => {
  currentRecord.value = record;
  detailModalVisible.value = true;
};

const handleDelete = async (record) => {
  try {
    await deleteProgram(record.id);
    message.success('删除成功');
    loadData();
  } catch (error) {
    message.error('删除失败');
  }
};

const handleFormSuccess = () => {
  formModalVisible.value = false;
  loadData();
};

const getStatusColor = (status) => {
  const colorMap = {
    planning: 'blue',
    executing: 'green',
    completed: 'success',
    suspended: 'warning',
  };
  return colorMap[status] || 'default';
};

const getStatusText = (status) => {
  const textMap = {
    planning: '规划中',
    executing: '执行中',
    completed: '已完成',
    suspended: '已暂停',
  };
  return textMap[status] || status;
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.program-container {
  padding: 16px;
}

.program-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.program-search {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.program-table {
  background: white;
  border-radius: 6px;
}
</style>
