<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="getTitle"
    width="85%"
    @ok="handleSubmit"
    showFooter
    :maskClosable="false"
    :keyboard="false"
    class="contract-edit-drawer"
  >
    <div class="contract-edit-container">
      <a-spin :spinning="loading" tip="加载中...">
        <a-tabs
          v-model:activeKey="activeTab"
          type="card"
          class="contract-tabs"
          :tabBarGutter="8"
        >
          <!-- 基本信息标签页 - 合同管理员可编辑 -->
          <a-tab-pane key="basic" class="tab-pane-content" v-if="permissions.canEditBasicInfo || !isUpdate">
            <template #tab>
              <span class="tab-title">
                <FileTextOutlined class="tab-icon" />
                基本信息
              </span>
            </template>
            <a-card :bordered="false" class="form-card">
              <template #title>
                <div class="card-title">
                  <InfoCircleOutlined class="title-icon" />
                  合同基础信息
                </div>
              </template>
              <BasicForm @register="registerBasicForm" class="contract-form">
                <template #custId="{ model, field }">
                  <CustomerSelect
                    v-model:value="model[field]"
                    custType="JIAFANG"
                    placeholder="请选择客户单位"
                    @change="handleCustomerChange"
                  />
                </template>
                <template #finalUserId="{ model, field }">
                  <CustomerSelect
                    v-model:value="model[field]"
                    custType="JIAFANG"
                    placeholder="请选择最终用户"
                  />
                </template>
                <template #ownId="{ model, field }">
                  <XhUserSelect
                    v-model:value="model[field]"
                    placeholder="请选择项目负责人"
                  />
                </template>
                <template #deptId="{ model, field }">
                  <XhDepSelect
                    v-model:value="model[field]"
                    placeholder="请选择所属部门"
                  />
                </template>
              </BasicForm>
            </a-card>
          </a-tab-pane>

          <!-- 商务信息标签页 - 合同管理员可编辑 -->
          <a-tab-pane key="business" class="tab-pane-content" v-if="permissions.canEditBusinessInfo || !isUpdate">
            <template #tab>
              <span class="tab-title">
                <DollarOutlined class="tab-icon" />
                商务分配信息
              </span>
            </template>
            <a-card :bordered="false" class="form-card">
              <template #title>
                <div class="card-title">
                  <DollarCircleOutlined class="title-icon" />
                  商务财务信息
                </div>
              </template>
              <BasicForm @register="registerBusinessForm" class="contract-form" />
            </a-card>
          </a-tab-pane>

          <!-- 交付信息标签页 - 项目经理可编辑 -->
          <a-tab-pane key="delivery" class="tab-pane-content" v-if="permissions.canEditDeliveryInfo">
            <template #tab>
              <span class="tab-title">
                <CalendarOutlined class="tab-icon" />
                交付信息
              </span>
            </template>
            <a-card :bordered="false" class="form-card">
              <template #title>
                <div class="card-title">
                  <ScheduleOutlined class="title-icon" />
                  项目交付时间
                </div>
              </template>
              <BasicForm @register="registerDeliveryForm" class="contract-form" />
            </a-card>
          </a-tab-pane>

          <!-- 项目管理信息标签页 - 项目经理可编辑 -->
          <a-tab-pane key="development" class="tab-pane-content" v-if="permissions.canEditProjectInfo">
            <template #tab>
              <span class="tab-title">
                <CodeOutlined class="tab-icon" />
                项目管理信息
              </span>
            </template>
            <a-card :bordered="false" class="form-card">
              <template #title>
                <div class="card-title">
                  <ToolOutlined class="title-icon" />
                  研发联系信息
                </div>
              </template>
              <BasicForm @register="registerDevelopmentForm" class="contract-form" />
            </a-card>
          </a-tab-pane>

          <!-- 外部单位/干系人信息标签页 - 项目经理可编辑 -->
          <a-tab-pane key="partners" class="tab-pane-content" v-if="permissions.canEditProjectInfo">
            <template #tab>
              <span class="tab-title">
                <TeamOutlined class="tab-icon" />
                外部单位
              </span>
            </template>
            <a-card :bordered="false" class="form-card">
              <template #title>
                <div class="card-title">
                  <ContactsOutlined class="title-icon" />
                  外部单位及干系人信息
                </div>
              </template>
              <BasicForm @register="registerPartnersForm" class="contract-form">
                <template #svDeptInfo="{ model, field }">
                  <CustomerLinkmanSelect
                    v-model:value="model[field]"
                    custType="JIANLI"
                    customerPlaceholder="请选择监理单位"
                    linkmanPlaceholder="请选择联系人"
                    @change="handleSvDeptChange"
                  />
                </template>
                <template #reviewDeptInfo="{ model, field }">
                  <CustomerLinkmanSelect
                    v-model:value="model[field]"
                    custType="CEPING"
                    customerPlaceholder="请选择三方测评单位"
                    linkmanPlaceholder="请选择联系人"
                    @change="handleReviewDeptChange"
                  />
                </template>
                <template #dbDeptInfo="{ model, field }">
                  <CustomerLinkmanSelect
                    v-model:value="model[field]"
                    custType="DENGBAO"
                    customerPlaceholder="请选择等保单位"
                    linkmanPlaceholder="请选择联系人"
                    @change="handleDbDeptChange"
                  />
                </template>
                <template #smDeptInfo="{ model, field }">
                  <CustomerLinkmanSelect
                    v-model:value="model[field]"
                    custType="SHANGMI"
                    customerPlaceholder="请选择商密评测单位"
                    linkmanPlaceholder="请选择联系人"
                    @change="handleSmDeptChange"
                  />
                </template>
                <template #jsDeptInfo="{ model, field }">
                  <CustomerLinkmanSelect
                    v-model:value="model[field]"
                    custType="JIESUAN"
                    customerPlaceholder="请选择结算单位"
                    linkmanPlaceholder="请选择联系人"
                    @change="handleJsDeptChange"
                  />
                </template>
              </BasicForm>
            </a-card>
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { computed, ref, unref, watch, nextTick } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import { createContract, updateContract } from '/@/api/project/contract';
  import { getUserRoleInContract } from '/@/api/project/contractMember';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { XhUserSelect, XhDepSelect } from '/@/components/Xh/Organize';
  import { CustomerSelect, CustomerLinkmanSelect } from '../components';
  import { useBaseStore } from '/@/store/modules/base';
  import { ref as vueRef, onMounted } from 'vue';
  import {
    FileTextOutlined,
    DollarOutlined,
    CalendarOutlined,
    CodeOutlined,
    TeamOutlined,
    InfoCircleOutlined,
    DollarCircleOutlined,
    ScheduleOutlined,
    ToolOutlined,
    ContactsOutlined,
  } from '@ant-design/icons-vue';

  const { createMessage } = useMessage();
  const baseStore = useBaseStore();
  const emit = defineEmits(['register', 'reload']);
  const isUpdate = ref(false);
  const contractId = ref('');
  const selectedCustomerId = ref('');
  const activeTab = ref('basic');
  const loading = ref(false);

  // 数据字典选项
  const contractTypeOptions = ref<Array<{ fullName: string; id: string }>>([]);
  const contractStatusOptions = ref<Array<{ fullName: string; id: string }>>([]);
  const moneyStatusOptions = ref<Array<{ fullName: string; id: string }>>([]);

  // 加载数据字典选项
  async function loadDictOptions() {
    try {
      // 加载合同类型字典
      const contractTypeData = await baseStore.getDictionaryData('contractType');
      if (Array.isArray(contractTypeData)) {
        contractTypeOptions.value = contractTypeData.map(item => ({
          fullName: item.fullName,
          id: item.enCode,
        }));
      }

      // 加载合同状态字典
      const contractStatusData = await baseStore.getDictionaryData('contractStatus');
      if (Array.isArray(contractStatusData)) {
        contractStatusOptions.value = contractStatusData.map(item => ({
          fullName: item.fullName,
          id: item.enCode,
        }));
      }

      // 加载收款状态字典
      const moneyStatusData = await baseStore.getDictionaryData('HSZ');
      if (Array.isArray(moneyStatusData)) {
        moneyStatusOptions.value = moneyStatusData.map(item => ({
          fullName: item.fullName,
          id: item.enCode,
        }));
      }
    } catch (error) {
      console.warn('加载数据字典失败:', error);
      // 使用默认选项作为备选
      contractTypeOptions.value = [
        { fullName: '新签', id: '1' },
        { fullName: '续签', id: '2' },
        { fullName: '变更', id: '3' },
      ];
      contractStatusOptions.value = [
        { fullName: '草稿', id: '1' },
        { fullName: '执行中', id: '2' },
        { fullName: '已完成', id: '3' },
        { fullName: '已终止', id: '4' },
        { fullName: '已归档', id: '5' },
      ];
      moneyStatusOptions.value = [
        { fullName: '未收款', id: '0' },
        { fullName: '部分收款', id: '1' },
        { fullName: '已结清', id: '2' },
      ];
    }
  }

  // 组件挂载时加载字典数据
  onMounted(() => {
    loadDictOptions();
  });

  // 当前合同状态
  const contractStatus = ref('');

  // 用户角色定义
  const userRole = ref('contract_admin'); // contract_admin, pmo, project_manager, team_member

  // 获取用户在合同中的角色
  async function loadUserRole(contractId: string) {
    if (!contractId) {
      userRole.value = 'contract_admin'; // 新建合同时默认为合同管理员
      return;
    }

    try {
      // 获取当前用户ID（这里需要根据实际的用户状态管理来获取）
      const currentUserId = baseStore.userInfo?.userId || '';
      if (currentUserId) {
        const role = await getUserRoleInContract(contractId, currentUserId);
        userRole.value = role || 'team_member'; // 默认为项目组成员
      }
    } catch (error) {
      console.warn('获取用户角色失败:', error);
      userRole.value = 'team_member'; // 默认为项目组成员
    }
  }

  // 权限判断函数
  const permissions = computed(() => {
    const role = unref(userRole);
    const status = unref(contractStatus);
    const isCreate = !unref(isUpdate);

    return {
      // 合同管理员和PMO权限：基础信息 + 商务信息
      canEditBasicInfo: (role === 'contract_admin' || role === 'pmo') && (isCreate || status === '1'), // 草稿状态
      canEditBusinessInfo: (role === 'contract_admin' || role === 'pmo') && (isCreate || status === '1'),

      // 项目经理权限：项目管理信息 + 交付信息
      canEditProjectInfo: (role === 'project_manager' || role === 'contract_admin') && (isCreate || ['1', '2'].includes(status)), // 草稿或执行中
      canEditDeliveryInfo: (role === 'project_manager' || role === 'contract_admin') && (isCreate || ['1', '2'].includes(status)),

      // 只读权限
      isReadOnly: role === 'team_member' || ['3', '4', '5'].includes(status), // 项目组成员或已完成/终止/归档状态
    };
  });

  // 兼容原有的商务权限判断
  const hasBusinessPermission = computed(() => {
    return permissions.value.canEditBusinessInfo;
  });

  // 处理客户单位选择变更
  function handleCustomerChange(value: string, _selectedData: any) {
    selectedCustomerId.value = value;
    // 可以在这里处理其他逻辑，比如设置联系人等
  }

  // 处理监理单位选择变更
  function handleSvDeptChange(value: any) {
    console.log('监理单位选择变更:', value);
    if (value && value.customerId) {
      setPartnersFieldsValue({
        svDeptId: value.customerId,
        svLinkman: value.linkman,
        svTelephone: value.telephone,
      });
    }
  }

  // 处理三方测评单位选择变更
  function handleReviewDeptChange(value: any) {
    console.log('三方测评单位选择变更:', value);
    if (value && value.customerId) {
      setPartnersFieldsValue({
        reviewDeptId: value.customerId,
        reviewLinkman: value.linkman,
        reviewTelephone: value.telephone,
      });
    }
  }

  // 处理等保单位选择变更
  function handleDbDeptChange(value: any) {
    console.log('等保单位选择变更:', value);
    if (value && value.customerId) {
      setPartnersFieldsValue({
        dbDeptId: value.customerId,
        dbLinkman: value.linkman,
        dbTelephone: value.telephone,
      });
    }
  }

  // 处理商密评测单位选择变更
  function handleSmDeptChange(value: any) {
    console.log('商密评测单位选择变更:', value);
    if (value && value.customerId) {
      setPartnersFieldsValue({
        smDeptId: value.customerId,
        smLinkman: value.linkman,
        smTelephone: value.telephone,
      });
    }
  }

  // 处理结算单位选择变更
  function handleJsDeptChange(value: any) {
    console.log('结算单位选择变更:', value);
    if (value && value.customerId) {
      setPartnersFieldsValue({
        jsDeptId: value.customerId,
        jsLinkman: value.linkman,
        jsTelephone: value.telephone,
      });
    }
  }

  // 基本信息表单配置 - 合同管理员权限
  const basicFormSchemas = computed<FormSchema[]>(() => [
    {
      field: 'name',
      label: '合同名称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入合同名称',
        maxlength: 500,
        disabled: !permissions.value.canEditBasicInfo,
      },
      rules: [
        { required: true, trigger: 'blur', message: '请输入合同名称' },
        { max: 500, message: '合同名称最多为500个字符', trigger: 'blur' },
      ],
      colProps: { span: 12 },
    },
    {
      field: 'cno',
      label: '合同编号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入合同编号',
        maxlength: 50,
        disabled: !permissions.value.canEditBasicInfo,
      },
      rules: [
        { required: true, trigger: 'blur', message: '请输入合同编号' },
        { max: 50, message: '合同编号最多为50个字符', trigger: 'blur' },
      ],
      colProps: { span: 12 },
    },
    {
      field: 'custId',
      label: '客户单位',
      component: 'Input',
      slot: 'custId',
      required: true,
      componentProps: {
        disabled: !permissions.value.canEditBasicInfo,
      },
      colProps: { span: 12 },
    },
    {
      field: 'finalUserId',
      label: '最终用户',
      component: 'Input',
      slot: 'finalUserId',
      componentProps: {
        disabled: !permissions.value.canEditBasicInfo,
      },
      colProps: { span: 12 },
    },
    {
      field: 'ownId',
      label: '项目负责人',
      component: 'Input',
      slot: 'ownId',
      required: true,
      componentProps: {
        disabled: !permissions.value.canEditBasicInfo,
      },
      colProps: { span: 12 },
    },
    {
      field: 'deptId',
      label: '所属部门',
      component: 'Input',
      slot: 'deptId',
      required: true,
      componentProps: {
        disabled: !permissions.value.canEditBasicInfo,
      },
      colProps: { span: 12 },
    },
  ]);

  // 商务信息表单配置 - 合同管理员权限
  const businessFormSchemas = computed<FormSchema[]>(() => [
    {
      field: 'typeStatus',
      label: '合同类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择合同类型',
        options: contractTypeOptions,
        disabled: !permissions.value.canEditBusinessInfo,
      },
      colProps: { span: 12 },
    },
    {
      field: 'amount',
      label: '部门合同金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入部门合同金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !permissions.value.canEditBusinessInfo,
      },
      rules: [{ required: true, trigger: 'blur', message: '请输入部门合同金额' }],
      colProps: { span: 12 },
    },
    {
      field: 'evaCostAmount',
      label: '费用预测',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入费用预测',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !permissions.value.canEditBusinessInfo,
      },
      colProps: { span: 12 },
    },
    {
      field: 'actCostAmount',
      label: '实际费用',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入实际费用',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !permissions.value.canEditBusinessInfo,
      },
      colProps: { span: 12 },
    },
    {
      field: 'evaExternalAmount',
      label: '采购费用预测',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入采购费用预测',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !permissions.value.canEditBusinessInfo,
      },
      colProps: { span: 12 },
    },
    {
      field: 'actExternalAmount',
      label: '实际采购金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入实际采购金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !permissions.value.canEditBusinessInfo,
      },
      colProps: { span: 12 },
    },
    {
      field: 'unsignExternalAmount',
      label: '待签外采金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入待签外采金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'estProbit',
      label: '预估毛利',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入预估毛利',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'estProbitRatio',
      label: '预估毛利率(%)',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入预估毛利率',
        min: 0,
        max: 100,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'actProbit',
      label: '实际毛利',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入实际毛利',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'actProbitRatio',
      label: '实际毛利率(%)',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入实际毛利率',
        min: 0,
        max: 100,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'contractStatus',
      label: '合同状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择合同状态',
        options: contractStatusOptions,
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'externalStatus',
      label: '是否外采',
      component: 'Select',
      componentProps: {
        placeholder: '请选择是否外采',
        options: [
          { fullName: '否', id: '0' },
          { fullName: '是', id: '1' },
        ],
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'moneyStatus',
      label: '收款状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择收款状态',
        options: moneyStatusOptions,
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'reportFrequency',
      label: '汇报频率',
      component: 'Select',
      componentProps: {
        placeholder: '请选择汇报频率',
        options: [
          { fullName: '每周', id: 'weekly' },
          { fullName: '每月', id: 'monthly' },
          { fullName: '每季度', id: 'quarterly' },
        ],
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    // 分部金额分配
    {
      field: 'yfYbAmount',
      label: '一部金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入一部金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'yfEbAmount',
      label: '二部金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入二部金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'yfJfAmount',
      label: '交付分部金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入交付分部金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'yfOtherAmount',
      label: '综合分配金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入综合分配金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    // 外采金额分配
    {
      field: 'outYbAmount',
      label: '一部外采金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入一部外采金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'outEbAmount',
      label: '二部外采金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入二部外采金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'outJfAmount',
      label: '交付外采金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入交付外采金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'outOtherAmount',
      label: '综合外采金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入综合外采金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    // 待签外采金额
    {
      field: 'unsignOutYbAmount',
      label: '待签一部外采',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入待签一部外采金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'unsignOutEbAmount',
      label: '待签二部外采',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入待签二部外采金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'unsignOutJfAmount',
      label: '待签交付外采',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入待签交付外采金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'unsignOutOtherAmount',
      label: '待签综合外采',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入待签综合外采金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    // 外采已付金额
    {
      field: 'outYfYbAmount',
      label: '一部外采已付',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入一部外采已付金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'outYfEbAmount',
      label: '二部外采已付',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入二部外采已付金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'outYfJfAmount',
      label: '交付外采已付',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入交付外采已付金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'outYfOtherAmount',
      label: '综合外采已付',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入综合外采已付金额',
        min: 0,
        precision: 2,
        style: 'width: 100%',
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
  ]);

  // 交付信息表单配置
  const deliveryFormSchemas: FormSchema[] = [
    {
      field: 'signDate',
      label: '合同签订日期',
      component: 'DatePicker',
      componentProps: { placeholder: '请选择合同签订日期', style: 'width: 100%' },
      colProps: { span: 12 },
    },
    {
      field: 'cstartDate',
      label: '合同开始日期',
      component: 'DatePicker',
      componentProps: { placeholder: '请选择合同开始日期', style: 'width: 100%' },
      colProps: { span: 12 },
    },
    {
      field: 'cendDate',
      label: '合同结束日期',
      component: 'DatePicker',
      componentProps: { placeholder: '请选择合同结束日期', style: 'width: 100%' },
      colProps: { span: 12 },
    },
    {
      field: 'mstartDate',
      label: '维保开始日期',
      component: 'DatePicker',
      componentProps: { placeholder: '请选择维保开始日期', style: 'width: 100%' },
      colProps: { span: 12 },
    },
    {
      field: 'mendDate',
      label: '维保结束日期',
      component: 'DatePicker',
      componentProps: { placeholder: '请选择维保结束日期', style: 'width: 100%' },
      colProps: { span: 12 },
    },
  ];

  // 研发信息表单配置
  const developmentFormSchemas: FormSchema[] = [
    {
      field: 'linkman',
      label: '联系人姓名',
      component: 'Input',
      componentProps: {
        placeholder: '请输入联系人姓名',
        maxlength: 50,
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'linktelephone',
      label: '联系电话',
      component: 'Input',
      componentProps: {
        placeholder: '请输入联系电话',
        maxlength: 50,
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      colProps: { span: 12 },
    },
    {
      field: 'note',
      label: '备注',
      component: 'Input',
      componentProps: {
        placeholder: '请输入备注',
        maxlength: 500,
        showCount: true,
        type: 'textarea',
        rows: 4,
        disabled: !unref(hasBusinessPermission) && unref(isUpdate),
      },
      rules: [{ max: 500, message: '备注最多为500个字符', trigger: 'blur' }],
      colProps: { span: 24 },
    },
  ];

  // 外部单位/干系人信息表单配置
  const partnersFormSchemas: FormSchema[] = [
    // 监理单位信息
    {
      field: 'svDeptInfo',
      label: '监理单位',
      component: 'Input',
      slot: 'svDeptInfo',
      colProps: { span: 24 },
    },
    // 三方测评单位信息
    {
      field: 'reviewDeptInfo',
      label: '三方测评单位',
      component: 'Input',
      slot: 'reviewDeptInfo',
      colProps: { span: 24 },
    },
    // 等保单位信息
    {
      field: 'dbDeptInfo',
      label: '等保单位',
      component: 'Input',
      slot: 'dbDeptInfo',
      colProps: { span: 24 },
    },
    // 商密评测单位信息
    {
      field: 'smDeptInfo',
      label: '商密评测单位',
      component: 'Input',
      slot: 'smDeptInfo',
      colProps: { span: 24 },
    },
    // 结算单位信息
    {
      field: 'jsDeptInfo',
      label: '结算单位',
      component: 'Input',
      slot: 'jsDeptInfo',
      colProps: { span: 24 },
    },
    // 隐藏字段，用于存储实际的值
    {
      field: 'svDeptId',
      label: '监理单位ID',
      component: 'Input',
      show: false,
    },
    {
      field: 'svLinkman',
      label: '监理单位联系人',
      component: 'Input',
      show: false,
    },
    {
      field: 'svTelephone',
      label: '监理单位联系电话',
      component: 'Input',
      show: false,
    },
    {
      field: 'reviewDeptId',
      label: '三方测评单位ID',
      component: 'Input',
      show: false,
    },
    {
      field: 'reviewLinkman',
      label: '三方测评联系人',
      component: 'Input',
      show: false,
    },
    {
      field: 'reviewTelephone',
      label: '三方测评联系电话',
      component: 'Input',
      show: false,
    },
    {
      field: 'dbDeptId',
      label: '等保单位ID',
      component: 'Input',
      show: false,
    },
    {
      field: 'dbLinkman',
      label: '等保单位联系人',
      component: 'Input',
      show: false,
    },
    {
      field: 'dbTelephone',
      label: '等保单位联系电话',
      component: 'Input',
      show: false,
    },
    {
      field: 'smDeptId',
      label: '商密评测单位ID',
      component: 'Input',
      show: false,
    },
    {
      field: 'smLinkman',
      label: '商密评测单位联系人',
      component: 'Input',
      show: false,
    },
    {
      field: 'smTelephone',
      label: '商密评测单位联系电话',
      component: 'Input',
      show: false,
    },
    {
      field: 'jsDeptId',
      label: '结算单位ID',
      component: 'Input',
      show: false,
    },
    {
      field: 'jsLinkman',
      label: '结算单位联系人',
      component: 'Input',
      show: false,
    },
    {
      field: 'jsTelephone',
      label: '结算单位联系电话',
      component: 'Input',
      show: false,
    },
  ];

  // 注册表单
  const [registerBasicForm, { resetFields: resetBasicFields, setFieldsValue: setBasicFieldsValue, validate: validateBasicForm }] = useForm({
    labelWidth: 120,
    schemas: basicFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  });

  const [registerBusinessForm, { resetFields: resetBusinessFields, setFieldsValue: setBusinessFieldsValue, validate: validateBusinessForm }] = useForm({
    labelWidth: 120,
    schemas: businessFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  });

  const [registerDeliveryForm, { resetFields: resetDeliveryFields, setFieldsValue: setDeliveryFieldsValue, validate: validateDeliveryForm }] = useForm({
    labelWidth: 120,
    schemas: deliveryFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  });

  const [registerDevelopmentForm, { resetFields: resetDevelopmentFields, setFieldsValue: setDevelopmentFieldsValue, validate: validateDevelopmentForm }] =
    useForm({
      labelWidth: 120,
      schemas: developmentFormSchemas,
      showActionButtonGroup: false,
      baseColProps: { span: 24 },
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    });

  // 注册外部单位/干系人信息表单
  const [registerPartnersForm, { resetFields: resetPartnersFields, setFieldsValue: setPartnersFieldsValue, validate: validatePartnersForm }] = useForm({
    labelWidth: 140,
    schemas: partnersFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  });

  // 注册抽屉
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    loading.value = true;
    resetBasicFields();
    resetBusinessFields();
    resetDeliveryFields();
    resetDevelopmentFields();
    resetPartnersFields();
    setDrawerProps({ confirmLoading: false });

    isUpdate.value = !!data?.isUpdate;

    try {
      if (unref(isUpdate)) {
        contractId.value = data.record.cId;
        contractStatus.value = data.record.contractStatus || '';

        // 加载用户在合同中的角色
        await loadUserRole(contractId.value);

        const formData = { ...data.record };

        // 设置基本表单数据
        setBasicFieldsValue(formData);
        setBusinessFieldsValue(formData);
        setDeliveryFieldsValue(formData);
        setDevelopmentFieldsValue(formData);

        // 等待下一个tick，确保表单完全渲染后再设置外部单位/干系人信息
        await nextTick();

      try {
        // 设置外部单位/干系人信息表单字段值（隐藏字段）
        await setPartnersFieldsValue({
          svDeptId: formData.svDeptId,
          svLinkman: formData.svLinkman,
          svTelephone: formData.svTelephone,
          reviewDeptId: formData.reviewDeptId,
          reviewLinkman: formData.reviewLinkman,
          reviewTelephone: formData.reviewTelephone,
          dbDeptId: formData.dbDeptId,
          dbLinkman: formData.dbLinkman,
          dbTelephone: formData.dbTelephone,
          smDeptId: formData.smDeptId,
          smLinkman: formData.smLinkman,
          smTelephone: formData.smTelephone,
          jsDeptId: formData.jsDeptId,
          jsLinkman: formData.jsLinkman,
          jsTelephone: formData.jsTelephone,
        });

        // 再等待一个tick，然后设置显示字段
        await nextTick();

        // 设置外部单位/干系人信息表单的显示值
        await setPartnersFieldsValue({
          svDeptInfo: formData.svDeptId
            ? {
                customerId: formData.svDeptId,
                linkman: formData.svLinkman,
                telephone: formData.svTelephone,
              }
            : undefined,
          reviewDeptInfo: formData.reviewDeptId
            ? {
                customerId: formData.reviewDeptId,
                linkman: formData.reviewLinkman,
                telephone: formData.reviewTelephone,
              }
            : undefined,
          dbDeptInfo: formData.dbDeptId
            ? {
                customerId: formData.dbDeptId,
                linkman: formData.dbLinkman,
                telephone: formData.dbTelephone,
              }
            : undefined,
          smDeptInfo: formData.smDeptId
            ? {
                customerId: formData.smDeptId,
                linkman: formData.smLinkman,
                telephone: formData.smTelephone,
              }
            : undefined,
          jsDeptInfo: formData.jsDeptId
            ? {
                customerId: formData.jsDeptId,
                linkman: formData.jsLinkman,
                telephone: formData.jsTelephone,
              }
            : undefined,
        });
      } catch (error) {
        console.warn('设置外部单位/干系人信息失败:', error);
        // 如果设置失败，不影响其他功能
      }

        // 设置选中的客户ID，触发联系人列表加载
        if (formData.custId) {
          selectedCustomerId.value = formData.custId;
        }
      } else {
        // 新建合同时，加载默认角色
        contractId.value = '';
        contractStatus.value = '';
        await loadUserRole('');
      }
    } catch (error) {
      console.warn('设置表单数据失败:', error);
      createMessage.error('加载合同数据失败');
    } finally {
      loading.value = false;
    }
  });

  // 监听基本表单中客户单位的变化
  watch(
    () => selectedCustomerId.value,
    newVal => {
      if (newVal) {
        // 当客户单位变化时，可以在这里添加其他逻辑
      }
    },
  );

  // 提交表单
  async function handleSubmit() {
    try {
      loading.value = true;
      setDrawerProps({ confirmLoading: true });

      // 验证所有表单
      const basicValues = await validateBasicForm();
      const businessValues = await validateBusinessForm();
      const deliveryValues = await validateDeliveryForm();
      const developmentValues = await validateDevelopmentForm();
      const partnersValues = await validatePartnersForm();

      // 合并所有表单数据
      const values = {
        ...basicValues,
        ...businessValues,
        ...deliveryValues,
        ...developmentValues,
        ...partnersValues,
      };

      // 删除辅助字段，这些字段只用于UI显示，不需要提交到后端
      delete values.svDeptInfo;
      delete values.reviewDeptInfo;
      delete values.dbDeptInfo;
      delete values.smDeptInfo;
      delete values.jsDeptInfo;

      if (unref(isUpdate)) {
        await updateContract(contractId.value, values);
        createMessage.success('合同更新成功！');
      } else {
        await createContract(values);
        createMessage.success('合同创建成功！');
      }

      closeDrawer();
      emit('reload');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
      createMessage.error('提交失败，请检查表单信息');
    } finally {
      loading.value = false;
      setDrawerProps({ confirmLoading: false });
    }
  }

  // 获取标题
  const getTitle = computed(() => {
    return unref(isUpdate) ? '编辑合同' : '新增合同';
  });
</script>

<style lang="less" scoped>
.contract-edit-drawer {
  :deep(.ant-drawer-body) {
    padding: 0;
    background: #f5f7fa;
  }

  :deep(.ant-drawer-header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;

    .ant-drawer-title {
      color: #fff;
      font-weight: 600;
      font-size: 16px;
    }

    .ant-drawer-close {
      color: rgba(255, 255, 255, 0.8);

      &:hover {
        color: #fff;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }
    }
  }
}

.contract-edit-container {
  height: 100%;
  padding: 0;
}

.contract-tabs {
  height: 100%;

  :deep(.ant-tabs-nav) {
    background: #fff;
    margin: 0;
    padding: 16px 24px 0;
    border-bottom: 1px solid #e8eaec;

    .ant-tabs-tab {
      border: 1px solid #e8eaec;
      border-radius: 8px 8px 0 0;
      margin-right: 8px;
      padding: 12px 20px;
      background: #fafbfc;
      transition: all 0.3s ease;

      &:hover {
        background: #f0f2f5;
        border-color: #d9d9d9;
      }

      &.ant-tabs-tab-active {
        background: #fff;
        border-color: #1890ff;
        border-bottom-color: #fff;

        .tab-title {
          color: #1890ff;
          font-weight: 600;
        }
      }
    }
  }

  :deep(.ant-tabs-content-holder) {
    background: #f5f7fa;
    padding: 0;
    height: calc(100% - 60px);
    overflow-y: auto;
  }

  :deep(.ant-tabs-tabpane) {
    height: 100%;
    padding: 0;
  }
}

.tab-pane-content {
  padding: 24px;
  height: 100%;
  overflow-y: auto;

  // 滚动条美化
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.tab-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  transition: color 0.3s ease;

  .tab-icon {
    font-size: 16px;
  }
}

.form-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8eaec;
  background: #fff;

  :deep(.ant-card-head) {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e8eaec;
    border-radius: 12px 12px 0 0;
    padding: 16px 24px;

    .ant-card-head-title {
      padding: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }

  :deep(.ant-card-body) {
    padding: 32px 24px;
  }
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #2c3e50;

  .title-icon {
    font-size: 18px;
    color: #1890ff;
  }
}

.contract-form {
  :deep(.ant-form-item) {
    margin-bottom: 24px;

    .ant-form-item-label {
      padding-bottom: 8px;

      label {
        font-weight: 500;
        color: #2c3e50;
        font-size: 14px;

        &.ant-form-item-required::before {
          color: #ff4d4f;
          font-size: 14px;
        }
      }
    }

    .ant-form-item-control-input {
      .ant-input,
      .ant-select-selector,
      .ant-input-number,
      .ant-picker {
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        transition: all 0.3s ease;

        &:hover {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }

        &:focus,
        &.ant-input-focused,
        &.ant-select-focused .ant-select-selector,
        &.ant-picker-focused {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }

      .ant-input-number {
        width: 100%;
      }

      .ant-picker {
        width: 100%;
      }
    }

    .ant-form-item-explain-error {
      color: #ff4d4f;
      font-size: 12px;
      margin-top: 4px;
    }
  }

  :deep(.ant-row) {
    margin-left: -12px;
    margin-right: -12px;

    .ant-col {
      padding-left: 12px;
      padding-right: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .contract-edit-drawer {
    :deep(.ant-drawer) {
      width: 95% !important;
    }
  }

  .tab-pane-content {
    padding: 16px;
  }

  .form-card {
    :deep(.ant-card-body) {
      padding: 24px 16px;
    }
  }
}

@media (max-width: 768px) {
  .contract-tabs {
    :deep(.ant-tabs-nav) {
      padding: 12px 16px 0;

      .ant-tabs-tab {
        padding: 8px 12px;
        margin-right: 4px;

        .tab-title {
          font-size: 12px;

          .tab-icon {
            font-size: 14px;
          }
        }
      }
    }
  }

  .tab-pane-content {
    padding: 12px;
  }

  .form-card {
    border-radius: 8px;

    :deep(.ant-card-head) {
      padding: 12px 16px;
      border-radius: 8px 8px 0 0;

      .ant-card-head-title {
        font-size: 14px;
      }
    }

    :deep(.ant-card-body) {
      padding: 20px 16px;
    }
  }

  .card-title {
    .title-icon {
      font-size: 16px;
    }
  }

  .contract-form {
    :deep(.ant-form-item) {
      margin-bottom: 16px;

      .ant-form-item-label {
        padding-bottom: 4px;

        label {
          font-size: 13px;
        }
      }
    }

    :deep(.ant-row) {
      margin-left: -8px;
      margin-right: -8px;

      .ant-col {
        padding-left: 8px;
        padding-right: 8px;
      }
    }
  }
}

// 加载状态样式
:deep(.ant-spin-container) {
  height: 100%;
}

:deep(.ant-spin-spinning) {
  .contract-tabs {
    opacity: 0.7;
    pointer-events: none;
  }
}


</style>
