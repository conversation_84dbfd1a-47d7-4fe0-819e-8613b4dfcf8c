package com.xinghuo.project.model.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目基础信息表单对象
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@Schema(description = "项目基础信息表单对象")
public class ProjBaseForm {

    /**
     * 主键ID（更新时使用）
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 项目编码
     */
    @Schema(description = "项目编码")
    @Size(max = 50, message = "项目编码长度不能超过50个字符")
    private String code;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    @NotBlank(message = "项目名称不能为空")
    @Size(max = 255, message = "项目名称长度不能超过255个字符")
    private String name;

    /**
     * 项目类型ID
     */
    @Schema(description = "项目类型ID")
    @NotBlank(message = "项目类型不能为空")
    @Size(max = 50, message = "项目类型ID长度不能超过50个字符")
    private String typeId;

    /**
     * 项目经理ID
     */
    @Schema(description = "项目经理ID")
    @NotBlank(message = "项目经理不能为空")
    @Size(max = 50, message = "项目经理ID长度不能超过50个字符")
    private String managerId;

    /**
     * 所属项目群ID
     */
    @Schema(description = "所属项目群ID")
    @Size(max = 36, message = "项目群ID长度不能超过36个字符")
    private String programId;

    /**
     * 项目状态ID
     */
    @Schema(description = "项目状态ID")
    @NotBlank(message = "项目状态不能为空")
    @Size(max = 50, message = "项目状态ID长度不能超过50个字符")
    private String statusId;

    /**
     * 项目健康状态
     */
    @Schema(description = "项目健康状态")
    @Size(max = 20, message = "项目健康状态长度不能超过20个字符")
    private String healthStatus;

    /**
     * 项目总体进度%
     */
    @Schema(description = "项目总体进度%")
    @Min(value = 0, message = "项目进度不能小于0")
    @Max(value = 100, message = "项目进度不能大于100")
    private Integer progress;

    /**
     * 计划开始日期
     */
    @Schema(description = "计划开始日期")
    private Date planStartDate;

    /**
     * 计划结束日期
     */
    @Schema(description = "计划结束日期")
    private Date planEndDate;

    /**
     * 实际开始日期
     */
    @Schema(description = "实际开始日期")
    private Date actualStartDate;

    /**
     * 实际结束日期
     */
    @Schema(description = "实际结束日期")
    private Date actualEndDate;

    /**
     * 项目描述
     */
    @Schema(description = "项目描述")
    private String description;

    /**
     * 预算-人力成本
     */
    @Schema(description = "预算-人力成本")
    @DecimalMin(value = "0.00", message = "预算人力成本不能小于0")
    private BigDecimal budgetCostLabor;

    /**
     * 预算-采购成本
     */
    @Schema(description = "预算-采购成本")
    @DecimalMin(value = "0.00", message = "预算采购成本不能小于0")
    private BigDecimal budgetCostPurchase;

    /**
     * 预算-差旅成本
     */
    @Schema(description = "预算-差旅成本")
    @DecimalMin(value = "0.00", message = "预算差旅成本不能小于0")
    private BigDecimal budgetCostTravel;

    /**
     * 实际-人力成本
     */
    @Schema(description = "实际-人力成本")
    @DecimalMin(value = "0.00", message = "实际人力成本不能小于0")
    private BigDecimal actualCostLabor;

    /**
     * 实际-采购成本
     */
    @Schema(description = "实际-采购成本")
    @DecimalMin(value = "0.00", message = "实际采购成本不能小于0")
    private BigDecimal actualCostPurchase;

    /**
     * 实际-差旅成本
     */
    @Schema(description = "实际-差旅成本")
    @DecimalMin(value = "0.00", message = "实际差旅成本不能小于0")
    private BigDecimal actualCostTravel;
}
