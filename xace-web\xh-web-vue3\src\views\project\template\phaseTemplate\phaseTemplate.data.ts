import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

export const columns: BasicColumn[] = [
  {
    title: '阶段编码',
    dataIndex: 'code',
    width: 120,
    fixed: 'left',
  },
  {
    title: '阶段名称',
    dataIndex: 'name',
    width: 200,
    fixed: 'left',
  },
  {
    title: '阶段描述',
    dataIndex: 'description',
    width: 300,
    ellipsis: true,
  },
  {
    title: '标准工期(天)',
    dataIndex: 'stdDuration',
    width: 120,
    align: 'center',
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }) => {
      const status = record.status;
      const color = status === 1 ? 'green' : 'red';
      const text = status === 1 ? '启用' : '禁用';
      return h(Tag, { color }, () => text);
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 150,
    format: 'date|YYYY-MM-DD HH:mm:ss',
  },
  {
    title: '创建人',
    dataIndex: 'creatorUserId',
    width: 120,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'keyword',
    label: '关键字',
    component: 'Input',
    componentProps: {
      placeholder: '请输入阶段编码或名称',
    },
    colProps: { span: 8 },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: '[createTimeStart, createTimeEnd]',
    label: '创建时间',
    component: 'RangePicker',
    colProps: { span: 8 },
  },
  {
    field: '[stdDurationMin, stdDurationMax]',
    label: '标准工期范围',
    component: 'InputGroup',
    slot: 'durationRange',
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'code',
    label: '阶段编码',
    component: 'Input',
    componentProps: {
      placeholder: '请输入阶段编码（可自动生成）',
    },
    colProps: { span: 12 },
    rules: [
      {
        pattern: /^[A-Z]{2}\d{8}$/,
        message: '阶段编码格式为：PL + 8位数字',
        trigger: 'blur',
      },
    ],
  },
  {
    field: 'name',
    label: '阶段名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入阶段名称',
      maxlength: 255,
    },
    colProps: { span: 12 },
  },
  {
    field: 'stdDuration',
    label: '标准工期(天)',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入标准工期',
      min: 1,
      max: 9999,
    },
    colProps: { span: 12 },
  },
  {
    field: 'status',
    label: '状态',
    component: 'RadioButtonGroup',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'defaultApprovalId',
    label: '默认审批流程',
    component: 'Select',
    componentProps: {
      placeholder: '请选择默认审批流程',
      // 这里可以动态加载审批流程选项
      options: [],
    },
    colProps: { span: 12 },
  },
  {
    field: 'defaultChecklistId',
    label: '默认检查单',
    component: 'Select',
    componentProps: {
      placeholder: '请选择默认检查单',
      // 这里可以动态加载检查单选项
      options: [],
    },
    colProps: { span: 12 },
  },
  {
    field: 'description',
    label: '阶段描述',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入阶段描述',
      rows: 4,
      maxlength: 1000,
      showCount: true,
    },
    colProps: { span: 24 },
  },
];

// 复制模板表单
export const copyFormSchema: FormSchema[] = [
  {
    field: 'newName',
    label: '新模板名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入新模板名称',
      maxlength: 255,
    },
  },
];
