package com.xinghuo.project.biz.entity.supplier;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目供应商实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_supplier")
public class SupplierEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 供应商名称
     */
    @TableField("name")
    private String name;

    /**
     * 联系人
     */
    @TableField("linkman")
    private String linkman;

    /**
     * 联系电话
     */
    @TableField("telephone")
    private String telephone;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 排序码
     */
    @TableField("sort_code")
    private Integer sortCode;
}
