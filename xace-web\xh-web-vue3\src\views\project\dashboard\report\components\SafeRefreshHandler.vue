<template>
  <div class="safe-refresh-handler">
    <slot />
    
    <!-- 刷新按钮 -->
    <a-float-button
      v-if="showRefreshButton"
      type="primary"
      :icon="h(ReloadOutlined)"
      tooltip="安全刷新"
      @click="handleSafeRefresh"
      :loading="refreshing"
      style="right: 24px; bottom: 80px;"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, h, onMounted, onBeforeUnmount } from 'vue';
  import { useRouter } from 'vue-router';
  import { ReloadOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { safeRefresh } from '../utils/routeGuard';

  interface Props {
    showRefreshButton?: boolean;
    autoRecovery?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    showRefreshButton: true,
    autoRecovery: true
  });

  const router = useRouter();
  const { createMessage } = useMessage();
  
  const refreshing = ref(false);
  let errorCount = 0;
  let errorTimer: NodeJS.Timeout | null = null;

  // 安全刷新处理
  async function handleSafeRefresh() {
    if (refreshing.value) return;
    
    try {
      refreshing.value = true;
      await safeRefresh(router);
      createMessage.success('页面刷新成功');
      errorCount = 0; // 重置错误计数
    } catch (error) {
      console.error('Safe refresh failed:', error);
      createMessage.error('页面刷新失败');
      
      // 如果刷新失败，尝试导航到报表首页
      try {
        await router.replace('/project/report/index');
      } catch (navError) {
        console.error('Fallback navigation failed:', navError);
        // 最后的兜底方案
        window.location.reload();
      }
    } finally {
      refreshing.value = false;
    }
  }

  // 监听全局错误
  function handleGlobalError(event: ErrorEvent) {
    const error = event.error;
    
    // 检查是否是Vue KeepAlive相关错误
    if (error?.message?.includes('Cannot destructure property') && 
        error?.message?.includes('vnode') && 
        error?.message?.includes('null')) {
      
      console.warn('Detected KeepAlive error, attempting auto recovery...');
      errorCount++;
      
      // 防止频繁错误导致的无限循环
      if (errorCount > 3) {
        console.error('Too many KeepAlive errors, stopping auto recovery');
        createMessage.error('页面出现异常，请手动刷新页面');
        return;
      }
      
      // 延迟自动恢复
      if (props.autoRecovery && !refreshing.value) {
        if (errorTimer) {
          clearTimeout(errorTimer);
        }
        
        errorTimer = setTimeout(() => {
          handleSafeRefresh();
        }, 1000); // 1秒后自动恢复
      }
      
      // 阻止错误继续传播
      event.preventDefault();
      event.stopPropagation();
    }
  }

  // 监听未捕获的Promise错误
  function handleUnhandledRejection(event: PromiseRejectionEvent) {
    const error = event.reason;
    
    if (error?.message?.includes('Cannot destructure property') && 
        error?.message?.includes('vnode')) {
      
      console.warn('Detected KeepAlive promise rejection, attempting recovery...');
      
      if (props.autoRecovery && !refreshing.value && errorCount <= 3) {
        errorCount++;
        
        if (errorTimer) {
          clearTimeout(errorTimer);
        }
        
        errorTimer = setTimeout(() => {
          handleSafeRefresh();
        }, 1000);
      }
      
      // 阻止错误继续传播
      event.preventDefault();
    }
  }

  // 键盘快捷键支持
  function handleKeydown(event: KeyboardEvent) {
    // Ctrl+R 或 F5 - 安全刷新
    if ((event.ctrlKey && event.key === 'r') || event.key === 'F5') {
      event.preventDefault();
      handleSafeRefresh();
    }
  }

  onMounted(() => {
    // 添加全局错误监听
    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('keydown', handleKeydown);
  });

  onBeforeUnmount(() => {
    // 清理事件监听
    window.removeEventListener('error', handleGlobalError);
    window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    window.removeEventListener('keydown', handleKeydown);
    
    if (errorTimer) {
      clearTimeout(errorTimer);
    }
  });

  // 暴露方法给父组件
  defineExpose({
    refresh: handleSafeRefresh,
    resetErrorCount: () => {
      errorCount = 0;
    }
  });
</script>

<style lang="less" scoped>
.safe-refresh-handler {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
