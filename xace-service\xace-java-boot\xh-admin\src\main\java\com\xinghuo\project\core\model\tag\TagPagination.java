package com.xinghuo.project.core.model.tag;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标签分页查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "标签分页查询对象")
public class TagPagination extends Pagination {

    /**
     * 标签名称
     */
    @Schema(description = "标签名称")
    private String tagName;

    /**
     * 标签范围 ('global': 所有用户可见, 'user_specific': 特定用户或组可见)
     */
    @Schema(description = "标签范围 ('global': 所有用户可见, 'user_specific': 特定用户或组可见)")
    private String scope;
}
