package com.xinghuo.project.execution.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目实体
 * 对应表：zz_proj_project
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_project")
public class ProjectEntity extends BaseEntity {

    /**
     * 项目编码
     */
    @TableField("code")
    private String code;

    /**
     * 项目名称
     */
    @TableField("name")
    private String name;

    /**
     * 项目标题（显示名称）
     */
    @TableField("title")
    private String title;

    /**
     * 项目描述
     */
    @TableField("description")
    private String description;

    /**
     * 项目模板ID
     */
    @TableField("schema_id")
    private String schemaId;

    /**
     * 项目状态
     * 0-未开始, 1-计划中, 2-进行中, 3-暂停, 4-已完成, 5-已取消, 6-已关闭
     */
    @TableField("status")
    private Integer status;

    /**
     * 工作流状态
     */
    @TableField("wf_status")
    private Integer wfStatus;

    /**
     * 备选状态
     * 1-正式, 2-候选, 3-备选
     */
    @TableField("alternative_status")
    private Integer alternativeStatus;

    /**
     * 项目经理用户ID
     */
    @TableField("manager_user_id")
    private String managerUserId;

    /**
     * 项目发起人用户ID
     */
    @TableField("sponsor_user_id")
    private String sponsorUserId;

    /**
     * 所属部门ID
     */
    @TableField("dept_id")
    private String deptId;

    /**
     * 项目组合ID
     */
    @TableField("portfolio_id")
    private String portfolioId;

    /**
     * 项目类型
     * 0-项目, 1-子项目, 2-项目集
     */
    @TableField("project_type")
    private Integer projectType;

    /**
     * 父项目ID
     */
    @TableField("parent_project_id")
    private String parentProjectId;

    /**
     * 项目优先级
     * 1-低, 2-中, 3-高, 4-紧急
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 计划开始时间
     */
    @TableField("plan_start_time")
    private Date planStartTime;

    /**
     * 计划结束时间
     */
    @TableField("plan_end_time")
    private Date planEndTime;

    /**
     * 实际开始时间
     */
    @TableField("actual_start_time")
    private Date actualStartTime;

    /**
     * 实际结束时间
     */
    @TableField("actual_end_time")
    private Date actualEndTime;

    /**
     * 预期结束时间
     */
    @TableField("expect_end_time")
    private Date expectEndTime;

    /**
     * 日历模板ID
     */
    @TableField("calendar_template_id")
    private String calendarTemplateId;

    /**
     * 日历开始时间
     */
    @TableField("calendar_start")
    private Date calendarStart;

    /**
     * 日历结束时间
     */
    @TableField("calendar_end")
    private Date calendarEnd;

    /**
     * 预算总额
     */
    @TableField("budget_total")
    private Double budgetTotal;

    /**
     * 预算分解方式
     * 0-不分解, 1-按阶段分解, 2-按活动分解
     */
    @TableField("budget_decomposition")
    private Integer budgetDecomposition;

    /**
     * 收入总额
     */
    @TableField("income_total")
    private Double incomeTotal;

    /**
     * 收入分解方式
     * 0-不分解, 1-按阶段分解, 2-按活动分解
     */
    @TableField("income_decomposition")
    private Integer incomeDecomposition;

    /**
     * 项目进度百分比
     */
    @TableField("progress_percent")
    private Double progressPercent;

    /**
     * 当前阶段名称
     */
    @TableField("current_phase_name")
    private String currentPhaseName;

    /**
     * 项目风险等级
     * 1-低, 2-中, 3-高
     */
    @TableField("risk_level")
    private Integer riskLevel;

    /**
     * 项目健康度
     * 1-绿色, 2-黄色, 3-红色
     */
    @TableField("health_status")
    private Integer healthStatus;

    /**
     * 是否从项目复制
     */
    @TableField("copy_from_project")
    private Boolean copyFromProject;

    /**
     * 复制源项目ID
     */
    @TableField("source_project_id")
    private String sourceProjectId;

    /**
     * 参考项目ID
     */
    @TableField("reference_id")
    private String referenceId;

    /**
     * 版本号
     */
    @TableField("version_no")
    private String versionNo;

    /**
     * 工作流版本
     */
    @TableField("wf_version")
    private String wfVersion;

    /**
     * 最后更新时间
     */
    @TableField("last_update_time")
    private Date lastUpdateTime;

    /**
     * 最后更新用户ID
     */
    @TableField("last_update_by")
    private String lastUpdateBy;

    /**
     * 是否异常完成
     */
    @TableField("abnormal_completed")
    private Boolean abnormalCompleted;

    /**
     * 项目标签（JSON格式存储）
     */
    @TableField("tags")
    private String tags;

    /**
     * 自定义字段1
     */
    @TableField("custom_field1")
    private String customField1;

    /**
     * 自定义字段2
     */
    @TableField("custom_field2")
    private String customField2;

    /**
     * 自定义字段3
     */
    @TableField("custom_field3")
    private String customField3;

    /**
     * 自定义字段4
     */
    @TableField("custom_field4")
    private String customField4;

    /**
     * 自定义字段5
     */
    @TableField("custom_field5")
    private String customField5;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;
}
