<template>
  <div class="error-boundary">
    <slot v-if="!hasError" />
    <div v-else class="error-fallback">
      <a-result
        status="error"
        title="页面出现错误"
        :sub-title="errorMessage"
      >
        <template #extra>
          <a-space>
            <a-button type="primary" @click="handleRetry">
              重试
            </a-button>
            <a-button @click="handleGoHome">
              返回首页
            </a-button>
          </a-space>
        </template>
      </a-result>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onErrorCaptured, nextTick } from 'vue';
  import { useRouter } from 'vue-router';
  import { useMessage } from '/@/hooks/web/useMessage';

  defineOptions({ name: 'ErrorBoundary' });

  const router = useRouter();
  const { createMessage } = useMessage();

  const hasError = ref(false);
  const errorMessage = ref('');

  // 捕获子组件错误
  onErrorCaptured((error: Error, instance, info) => {
    console.error('ErrorBoundary captured error:', error, info);
    
    // 检查是否是Vue内部的KeepAlive相关错误
    if (error.message?.includes('Cannot destructure property') && 
        error.message?.includes('vnode') && 
        error.message?.includes('null')) {
      
      // 这是KeepAlive的已知问题，尝试恢复
      console.warn('Detected KeepAlive vnode error, attempting recovery...');
      
      // 延迟处理，让Vue完成当前的更新周期
      nextTick(() => {
        try {
          // 强制刷新当前路由
          const currentRoute = router.currentRoute.value;
          router.replace({
            path: currentRoute.path,
            query: { ...currentRoute.query, _t: Date.now() }
          }).catch(() => {
            // 如果路由替换失败，回到报表首页
            router.replace('/project/report/index');
          });
        } catch (routeError) {
          console.error('Route recovery failed:', routeError);
          hasError.value = true;
          errorMessage.value = '页面状态异常，请刷新页面或联系管理员';
        }
      });
      
      // 阻止错误继续传播
      return false;
    }
    
    // 其他类型的错误正常处理
    hasError.value = true;
    errorMessage.value = error.message || '未知错误';
    
    // 发送错误信息到控制台
    createMessage.error('页面发生错误，请稍后重试');
    
    // 阻止错误继续传播到父组件
    return false;
  });

  // 重试处理
  function handleRetry() {
    hasError.value = false;
    errorMessage.value = '';
    
    // 强制重新渲染
    nextTick(() => {
      const currentRoute = router.currentRoute.value;
      router.replace({
        path: currentRoute.path,
        query: { ...currentRoute.query, _retry: Date.now() }
      });
    });
  }

  // 返回首页
  function handleGoHome() {
    hasError.value = false;
    errorMessage.value = '';
    router.push('/project/report/index');
  }

  // 暴露重置方法给父组件
  defineExpose({
    reset: () => {
      hasError.value = false;
      errorMessage.value = '';
    }
  });
</script>

<style lang="less" scoped>
.error-boundary {
  width: 100%;
  height: 100%;
  
  .error-fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    padding: 20px;
  }
}
</style>
