import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

export const columns: BasicColumn[] = [
  {
    title: '模板名称',
    dataIndex: 'name',
    width: 200,
    fixed: 'left',
  },
  {
    title: '模板描述',
    dataIndex: 'description',
    width: 300,
    ellipsis: true,
  },
  {
    title: '阶段数量',
    dataIndex: 'phaseCount',
    width: 100,
    align: 'center',
  },
  {
    title: '总工期(天)',
    dataIndex: 'totalDuration',
    width: 120,
    align: 'center',
  },
  {
    title: '知识状态',
    dataIndex: 'knStatusId',
    width: 100,
    customRender: ({ record }) => {
      const status = record.knStatusId;
      let color = 'default';
      let text = status;
      
      switch (status) {
        case 'draft':
          color = 'orange';
          text = '未发布';
          break;
        case 'published':
          color = 'green';
          text = '已发布';
          break;
        case 'archived':
          color = 'gray';
          text = '已归档';
          break;
      }
      
      return h(Tag, { color }, () => text);
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 150,
    format: 'date|YYYY-MM-DD HH:mm:ss',
  },
  {
    title: '创建人',
    dataIndex: 'creatorUserId',
    width: 120,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'keyword',
    label: '关键字',
    component: 'Input',
    componentProps: {
      placeholder: '请输入模板名称或描述',
    },
    colProps: { span: 8 },
  },
  {
    field: 'knStatusId',
    label: '知识状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未发布', value: 'draft' },
        { label: '已发布', value: 'published' },
        { label: '已归档', value: 'archived' },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: '[createTimeStart, createTimeEnd]',
    label: '创建时间',
    component: 'RangePicker',
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'name',
    label: '模板名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入模板名称',
      maxlength: 255,
    },
    colProps: { span: 24 },
  },
  {
    field: 'description',
    label: '模板描述',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入模板描述',
      rows: 4,
      maxlength: 1000,
      showCount: true,
    },
    colProps: { span: 24 },
  },
  {
    field: 'knStatusId',
    label: '知识状态',
    component: 'RadioButtonGroup',
    defaultValue: 'draft',
    componentProps: {
      options: [
        { label: '未发布', value: 'draft' },
        { label: '已发布', value: 'published' },
        { label: '已归档', value: 'archived' },
      ],
    },
    colProps: { span: 24 },
  },
  {
    field: 'projectTemplateIds',
    label: '关联项目模板',
    component: 'Select',
    componentProps: {
      mode: 'multiple',
      placeholder: '请选择关联的项目模板',
      // 这里可以动态加载项目模板选项
      options: [],
    },
    colProps: { span: 24 },
  },
  {
    field: 'tagIds',
    label: '标签',
    component: 'Select',
    componentProps: {
      mode: 'multiple',
      placeholder: '请选择标签',
      // 这里可以动态加载标签选项
      options: [],
    },
    colProps: { span: 24 },
  },
];

// 阶段明细表格列定义
export const phaseDetailColumns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'seqNo',
    width: 80,
    align: 'center',
    customRender: ({ record, index }) => {
      return record.seqNo || index + 1;
    },
  },
  {
    title: '阶段编码',
    dataIndex: 'code',
    width: 120,
  },
  {
    title: '阶段名称',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '阶段描述',
    dataIndex: 'description',
    width: 300,
    ellipsis: true,
  },
  {
    title: '工期(天)',
    dataIndex: 'duration',
    width: 100,
    align: 'center',
  },
  {
    title: '可裁剪',
    dataIndex: 'canCut',
    width: 100,
    align: 'center',
    customRender: ({ record }) => {
      return h(Tag, { color: record.canCut === 1 ? 'green' : 'red' }, () => 
        record.canCut === 1 ? '是' : '否'
      );
    },
  },
];

// 阶段明细表单
export const phaseDetailFormSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'code',
    label: '阶段编码',
    component: 'Input',
    componentProps: {
      placeholder: '请输入阶段编码（可选）',
    },
    colProps: { span: 12 },
  },
  {
    field: 'name',
    label: '阶段名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入阶段名称',
      maxlength: 255,
    },
    colProps: { span: 12 },
  },
  {
    field: 'duration',
    label: '工期(天)',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入工期',
      min: 1,
      max: 9999,
    },
    colProps: { span: 12 },
  },
  {
    field: 'canCut',
    label: '是否可裁剪',
    component: 'RadioButtonGroup',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: '可裁剪', value: 1 },
        { label: '不可裁剪', value: 0 },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'approvalSchemaId',
    label: '审批流程',
    component: 'Select',
    componentProps: {
      placeholder: '请选择审批流程',
      // 这里可以动态加载审批流程选项
      options: [],
    },
    colProps: { span: 12 },
  },
  {
    field: 'checkTemplateId',
    label: '检查单模板',
    component: 'Select',
    componentProps: {
      placeholder: '请选择检查单模板',
      // 这里可以动态加载检查单模板选项
      options: [],
    },
    colProps: { span: 12 },
  },
  {
    field: 'description',
    label: '阶段描述',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入阶段描述',
      rows: 4,
      maxlength: 1000,
      showCount: true,
    },
    colProps: { span: 24 },
  },
];

// 复制模板表单
export const copyFormSchema: FormSchema[] = [
  {
    field: 'newName',
    label: '新模板名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入新模板名称',
      maxlength: 255,
    },
  },
];

// 从标准阶段库选择阶段的表格列
export const standardPhaseColumns: BasicColumn[] = [
  {
    title: '阶段编码',
    dataIndex: 'code',
    width: 120,
  },
  {
    title: '阶段名称',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '阶段描述',
    dataIndex: 'description',
    width: 300,
    ellipsis: true,
  },
  {
    title: '标准工期(天)',
    dataIndex: 'stdDuration',
    width: 120,
    align: 'center',
  },
];
