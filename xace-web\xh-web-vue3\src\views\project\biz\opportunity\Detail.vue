<template>
  <div class="xh-content-wrapper">
    <div class="xh-content-wrapper-center">
      <div class="xh-content-wrapper-content">
        <PageWrapper :title="businessInfo?.projectName || '商机详情'" contentBackground contentClass="p-4">
          <template #extra>
            <a-button type="primary" @click="handleEdit" v-if="canEdit">编辑</a-button>
            <a-button type="primary" @click="handleUpdateStatus" v-if="canEdit">更新状态</a-button>
            <a-button type="primary" @click="handleAddTrack" v-if="canEdit">添加跟踪记录</a-button>
            <a-button type="primary" @click="handleConvertToContract" v-if="canConvert">转为合同</a-button>
            <a-button @click="goBack">返回</a-button>
          </template>

          <a-tabs v-model:activeKey="activeTab">
            <a-tab-pane key="basic" tab="基本信息">
              <Description title="商机基本信息" :bordered="true" :data="basicData" :column="2" size="middle" />

              <Divider />

              <Description title="项目简介" :bordered="true" :data="contentData" :column="1" size="middle" />

              <Divider />

              <Description title="财务预估" :bordered="true" :data="financeData" :column="2" size="middle" />
            </a-tab-pane>

            <a-tab-pane key="datalog" tab="变更历史">
              <DatalogList :businessId="businessId" />
            </a-tab-pane>
          </a-tabs>
        </PageWrapper>
      </div>
    </div>
    <FormDrawer @register="registerBusinessForm" @reload="loadBusinessInfo" />
    <StatusForm @register="registerStatusForm" @reload="loadBusinessInfo" />
    <TrackForm @register="registerTrackForm" @reload="loadBusinessInfo" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { PageWrapper } from '/@/components/Page';
  import { Description } from '/@/components/Description';
  import { Divider } from 'ant-design-vue';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getBusinessInfo, BusinessModel } from '/@/api/project/business';
  import { useDrawer } from '/@/components/Drawer';
  import FormDrawer from './FormDrawer.vue';
  import StatusForm from './StatusForm.vue';
  import TrackForm from './TrackForm.vue';
  import DatalogList from './DatalogList.vue';

  const route = useRoute();

  const router = useRouter();
  const { createMessage } = useMessage();
  const [registerBusinessForm, { openDrawer: openBusinessDrawer }] = useDrawer();
  const [registerStatusForm, { openModal: openStatusModal }] = useModal();
  const [registerTrackForm, { openModal: openTrackModal }] = useModal();
  const businessId = computed(() => route.params.id as string);
  const businessInfo = ref<BusinessModel | null>(null);
  const activeTab = ref('basic');

  // 是否可以编辑
  const canEdit = computed(() => {
    if (!businessInfo.value) return false;
    return businessInfo.value.status !== '已签' && businessInfo.value.status !== '已废弃';
  });

  // 是否可以转为合同
  const canConvert = computed(() => {
    if (!businessInfo.value) return false;
    return businessInfo.value.status === '商务谈判中';
  });

  // 基本信息展示数据
  const basicData = computed(() => {
    if (!businessInfo.value) return [];

    return [
      {
        field: '商机编号',
        label: '商机编号',
        value: businessInfo.value.businessNo || '-',
      },
      {
        field: '项目名称',
        label: '项目名称',
        value: businessInfo.value.projectName,
      },
      {
        field: '客户单位',
        label: '客户单位',
        value: businessInfo.value.custName || '-',
      },
      {
        field: '项目等级',
        label: '项目等级',
        value: businessInfo.value.projectLevel || '-',
      },
      {
        field: '项目负责人',
        label: '项目负责人',
        value: businessInfo.value.projectLeaderName || '-',
      },
      {
        field: '商机状态',
        label: '商机状态',
        value: businessInfo.value.status || '-',
      },
      {
        field: '项目类型',
        label: '项目类型',
        value: businessInfo.value.projType || '-',
      },
      {
        field: '详细项目类型',
        label: '详细项目类型',
        value: businessInfo.value.projectType || '-',
      },
      {
        field: '所属分部',
        label: '所属分部',
        value: businessInfo.value.deptName || '-',
      },
      {
        field: '研发分部',
        label: '研发分部',
        value: businessInfo.value.yfDeptName || '-',
      },
      {
        field: '市场负责人',
        label: '市场负责人',
        value: businessInfo.value.marketLinkman || '-',
      },
      {
        field: '售前负责人',
        label: '售前负责人',
        value: businessInfo.value.presaleLinkman || '-',
      },
      {
        field: '预计启动日期',
        label: '预计启动日期',
        value: businessInfo.value.startDate || '-',
      },
      {
        field: '预计落地月份',
        label: '预计落地月份',
        value: businessInfo.value.evaSignMonth || '-',
      },
      {
        field: '商机标签',
        label: '商机标签',
        value: businessInfo.value.businessTag || '-',
      },
      {
        field: '工时填写状态',
        label: '工时填写状态',
        value: businessInfo.value.workStatus || '-',
      },
      {
        field: '合同编号',
        label: '合同编号',
        value: businessInfo.value.projectNo || '-',
      },
      {
        field: '创建人',
        label: '创建人',
        value: businessInfo.value.createUserName || '-',
      },
      {
        field: '创建时间',
        label: '创建时间',
        value: businessInfo.value.createTime || '-',
      },
      {
        field: '最后修改人',
        label: '最后修改人',
        value: businessInfo.value.lastModifyUserName || '-',
      },
      {
        field: '最后修改时间',
        label: '最后修改时间',
        value: businessInfo.value.lastModifyTime || '-',
      },
    ];
  });

  // 项目简介展示数据
  const contentData = computed(() => {
    if (!businessInfo.value) return [];

    return [
      {
        field: '项目简介',
        label: '项目简介',
        value: businessInfo.value.projectContent || '-',
      },
      {
        field: '最后跟踪记录',
        label: '最后跟踪记录',
        value: businessInfo.value.lastNote || '-',
      },
    ];
  });

  // 财务预估展示数据
  const financeData = computed(() => {
    if (!businessInfo.value) return [];

    return [
      {
        field: '预计总金额',
        label: '预计总金额',
        value: formatAmount(businessInfo.value.yearMoney),
      },
      {
        field: '今年收款比例',
        label: '今年收款比例',
        value: businessInfo.value.yearMoneyRatio ? `${businessInfo.value.yearMoneyRatio}%` : '-',
      },
      {
        field: '预计首笔回款时间',
        label: '预计首笔回款时间',
        value: businessInfo.value.evaFirstMonth || '-',
      },
      {
        field: '预计首笔回款金额',
        label: '预计首笔回款金额',
        value: formatAmount(businessInfo.value.evaFirstAmount),
      },
      {
        field: '预计二笔回款时间',
        label: '预计二笔回款时间',
        value: businessInfo.value.evaSecondMonth || '-',
      },
      {
        field: '预计二笔回款金额',
        label: '预计二笔回款金额',
        value: formatAmount(businessInfo.value.evaSecondAmount),
      },
      {
        field: '预计首次外采时间',
        label: '预计首次外采时间',
        value: businessInfo.value.evaFirstexternalMonth || '-',
      },
      {
        field: '预计首次外采金额',
        label: '预计首次外采金额',
        value: formatAmount(businessInfo.value.evaFirstexternalAmount),
      },
      {
        field: '预计二次外采时间',
        label: '预计二次外采时间',
        value: businessInfo.value.evaSecondexternalMonth || '-',
      },
      {
        field: '预计二次外采金额',
        label: '预计二次外采金额',
        value: formatAmount(businessInfo.value.evaSecondexternalAmount),
      },
      {
        field: '预计外采总额',
        label: '预计外采总额',
        value: formatAmount(businessInfo.value.purchaseMoney),
      },
      {
        field: '预计毛利',
        label: '预计毛利',
        value: formatAmount(businessInfo.value.profitMoney),
      },
      {
        field: '各部门收入',
        label: '各部门收入',
        value: formatAmount(businessInfo.value.deptMoney),
      },
      {
        field: '一部收入',
        label: '一部收入',
        value: formatAmount(businessInfo.value.yfYbAmount),
      },
      {
        field: '二部收入',
        label: '二部收入',
        value: formatAmount(businessInfo.value.yfEbAmount),
      },
      {
        field: '一部外采',
        label: '一部外采',
        value: formatAmount(businessInfo.value.outYbAmount),
      },
      {
        field: '二部外采',
        label: '二部外采',
        value: formatAmount(businessInfo.value.outEbAmount),
      },
    ];
  });

  // 格式化金额
  function formatAmount(amount: number | undefined) {
    return amount ? amount.toLocaleString('zh-CN', { style: 'currency', currency: 'CNY' }) : '¥0.00';
  }

  // 加载商机信息
  async function loadBusinessInfo() {
    try {
      const data = await getBusinessInfo(businessId.value);
      businessInfo.value = data;
    } catch (error) {
      console.error('获取商机信息失败:', error);
      createMessage.error('获取商机信息失败');
    }
  }

  // 返回上一页
  function goBack() {
    router.back();
  }

  // 编辑商机信息
  function handleEdit() {
    openBusinessDrawer(true, {
      id: businessInfo.value?.id,
      isUpdate: true,
    });
  }

  // 更新商机状态
  function handleUpdateStatus() {
    openStatusModal(true, {
      record: businessInfo.value,
    });
  }

  // 添加跟踪记录
  function handleAddTrack() {
    openTrackModal(true, {
      record: businessInfo.value,
    });
  }

  // 转为合同功能已移除
  function handleConvertToContract() {
    createMessage.info('商机转合同功能已移除');
  }

  onMounted(() => {
    loadBusinessInfo();
  });
</script>
