<template>
  <div class="xh-content-wrapper">
    <div class="xh-content-wrapper-center">
      <div class="xh-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleCreate">新增合同</a-button>
          </template>

          <!-- 自定义搜索表单组件插槽 -->
          <template #custId="{ model, field }">
            <CustomerSelect v-model:value="model[field]" placeholder="请选择客户单位" />
          </template>
          <template #finalUserId="{ model, field }">
            <CustomerSelect v-model:value="model[field]" placeholder="请选择最终用户" />
          </template>
          <template #ownId="{ model, field }">
            <XhUserSelect v-model:value="model[field]" placeholder="请选择项目负责人" />
          </template>

          <!-- 表格单元格自定义渲染 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'amount'">
              {{ formatAmount(record.amount) }}
            </template>
            <template v-if="column.key === 'ysAmount'">
              {{ formatAmount(record.ysAmount) }}
            </template>
            <template v-if="column.key === 'evaCostAmount'">
              {{ formatAmount(record.evaCostAmount) }}
            </template>
            <template v-if="column.key === 'actCostAmount'">
              {{ formatAmount(record.actCostAmount) }}
            </template>
            <template v-if="column.key === 'evaExternalAmount'">
              {{ formatAmount(record.evaExternalAmount) }}
            </template>
            <template v-if="column.key === 'actExternalAmount'">
              {{ formatAmount(record.actExternalAmount) }}
            </template>
            <template v-if="column.key === 'unsignExternalAmount'">
              {{ formatAmount(record.unsignExternalAmount) }}
            </template>
            <template v-if="column.key === 'estProbit'">
              {{ formatAmount(record.estProbit) }}
            </template>
            <template v-if="column.key === 'actProbit'">
              {{ formatAmount(record.actProbit) }}
            </template>
            <template v-if="column.key === 'typeStatus'">
              <a-tag :color="getTypeStatusColor(record.typeStatus)">
                {{ record.typeStatusText || getTypeStatusText(record.typeStatus) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'contractStatus'">
              <a-tag :color="getContractStatusColor(record.contractStatus)">
                {{ record.contractStatusText || getContractStatusText(record.contractStatus) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'moneyStatus'">
              <a-tag :color="getMoneyStatusColor(record.moneyStatus)">
                {{ record.moneyStatusText || getMoneyStatusText(record.moneyStatus) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'externalStatus'">
              <a-tag :color="getExternalStatusColor(record.externalStatus)">
                {{ getExternalStatusText(record.externalStatus) }}
              </a-tag>
            </template>
            <template v-if="column.dataIndex === 'action'">
              <TableAction
                :actions="[
                  {
                    icon: 'clarity:info-standard-line',
                    label: '查看详情',
                    onClick: handleView.bind(null, record),
                  },
                  {
                    icon: 'clarity:note-edit-line',
                    label: '编辑',
                    onClick: handleEdit.bind(null, record),
                  },
                  {
                    icon: 'ant-design:delete-outlined',
                    color: 'error',
                    label: '删除',
                    popConfirm: {
                      title: '是否确认删除',
                      confirm: handleDelete.bind(null, record),
                    },
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <!-- 使用抽屉组件替代模态框 -->
    <EditDrawer @register="registerEditDrawer" @reload="reload" />
    <DetailDrawer @register="registerDetailDrawer" @reload="reload" @edit="handleEditFromDetail" />
  </div>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, FormSchema } from '/@/components/Table';
  import { getContractList, deleteContract } from '/@/api/project/contract';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useDrawer } from '/@/components/Drawer';
  import EditDrawer from './EditDrawer.vue';
  import DetailDrawer from './DetailDrawer.vue';
  import { formatToDate } from '/@/utils/dateUtil';
  import { XhUserSelect } from '/@/components/Xh/Organize';
  import { CustomerSelect } from '../components';
  import { translateDictFields } from '/@/utils/dict';

  const { createMessage } = useMessage();
  const [registerEditDrawer, { openDrawer: openEditDrawer }] = useDrawer();
  const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();

  // 合同类型映射
  const typeStatusMap = {
    '1': { text: '新签', color: 'blue' },
    '2': { text: '续签', color: 'green' },
    '3': { text: '变更', color: 'orange' },
  };

  // 合同状态映射
  const contractStatusMap = {
    '1': { text: '草稿', color: 'default' },
    '2': { text: '执行中', color: 'processing' },
    '3': { text: '已完成', color: 'success' },
    '4': { text: '已终止', color: 'error' },
    '5': { text: '已归档', color: 'warning' },
  };

  // 收款状态映射
  const moneyStatusMap = {
    '0': { text: '未收款', color: 'red' },
    '1': { text: '部分收款', color: 'orange' },
    '2': { text: '已结清', color: 'green' },
  };

  // 是否外采映射
  const externalStatusMap = {
    '0': { text: '否', color: 'default' },
    '1': { text: '是', color: 'blue' },
  };

  // 获取合同类型文本
  function getTypeStatusText(status: string | number) {
    const statusStr = String(status);
    return typeStatusMap[statusStr]?.text || status;
  }

  // 获取合同类型颜色
  function getTypeStatusColor(status: string | number) {
    const statusStr = String(status);
    return typeStatusMap[statusStr]?.color || 'default';
  }

  // 获取合同状态文本
  function getContractStatusText(status: string | number) {
    const statusStr = String(status);
    return contractStatusMap[statusStr]?.text || status;
  }

  // 获取合同状态颜色
  function getContractStatusColor(status: string | number) {
    const statusStr = String(status);
    return contractStatusMap[statusStr]?.color || 'default';
  }

  // 获取收款状态文本
  function getMoneyStatusText(status: string | number) {
    const statusStr = String(status);
    return moneyStatusMap[statusStr]?.text || status;
  }

  // 获取收款状态颜色
  function getMoneyStatusColor(status: string | number) {
    const statusStr = String(status);
    return moneyStatusMap[statusStr]?.color || 'default';
  }

  // 获取是否外采文本
  function getExternalStatusText(status: string | number) {
    const statusStr = String(status);
    return externalStatusMap[statusStr]?.text || status;
  }

  // 获取是否外采颜色
  function getExternalStatusColor(status: string | number) {
    const statusStr = String(status);
    return externalStatusMap[statusStr]?.color || 'default';
  }

  // 格式化金额
  function formatAmount(amount: number) {
    return amount ? amount.toLocaleString('zh-CN', { style: 'currency', currency: 'CNY' }) : '¥0.00';
  }

  // 表格列定义
  const columns = [
    {
      title: '合同财务编号',
      dataIndex: 'cno',
      width: 150,
      fixed: 'left' as const,
    },
    {
      title: '合同名称',
      dataIndex: 'name',
      width: 200,
    },
    {
      title: '甲方',
      dataIndex: 'custName',
      width: 150,
    },
    {
      title: '最终用户',
      dataIndex: 'finalUserName',
      width: 150,
    },
    {
      title: '项目经理',
      dataIndex: 'ownName',
      width: 100,
    },
    {
      title: '所属部门',
      dataIndex: 'deptName',
      width: 120,
    },
    {
      title: '合同类型',
      key: 'typeStatus',
      dataIndex: 'typeStatus',
      width: 100,
    },
    {
      title: '部门合同金额',
      key: 'amount',
      dataIndex: 'amount',
      width: 130,
      sorter: true,
    },
    {
      title: '已收金额',
      key: 'ysAmount',
      dataIndex: 'ysAmount',
      width: 120,
    },
    {
      title: '费用预测',
      key: 'evaCostAmount',
      dataIndex: 'evaCostAmount',
      width: 120,
    },
    {
      title: '实际费用',
      key: 'actCostAmount',
      dataIndex: 'actCostAmount',
      width: 120,
    },
    {
      title: '采购费用预测',
      key: 'evaExternalAmount',
      dataIndex: 'evaExternalAmount',
      width: 130,
    },
    {
      title: '实际采购金额',
      key: 'actExternalAmount',
      dataIndex: 'actExternalAmount',
      width: 130,
    },
    {
      title: '待签外采金额',
      key: 'unsignExternalAmount',
      dataIndex: 'unsignExternalAmount',
      width: 130,
    },
    {
      title: '预估毛利',
      key: 'estProbit',
      dataIndex: 'estProbit',
      width: 120,
    },
    {
      title: '预估毛利率',
      key: 'estProbitRatio',
      dataIndex: 'estProbitRatio',
      width: 120,
      customRender: ({ record }) => {
        return record.estProbitRatio ? `${record.estProbitRatio}%` : '-';
      },
    },
    {
      title: '实际毛利',
      key: 'actProbit',
      dataIndex: 'actProbit',
      width: 120,
    },
    {
      title: '实际毛利率',
      key: 'actProbitRatio',
      dataIndex: 'actProbitRatio',
      width: 120,
      customRender: ({ record }) => {
        return record.actProbitRatio ? `${record.actProbitRatio}%` : '-';
      },
    },
    {
      title: '一部金额',
      key: 'yfYbAmount',
      dataIndex: 'yfYbAmount',
      width: 120,
    },
    {
      title: '二部金额',
      key: 'yfEbAmount',
      dataIndex: 'yfEbAmount',
      width: 120,
    },
    {
      title: '交付分部金额',
      key: 'yfJfAmount',
      dataIndex: 'yfJfAmount',
      width: 130,
    },
    {
      title: '综合分配金额',
      key: 'yfOtherAmount',
      dataIndex: 'yfOtherAmount',
      width: 130,
    },
    {
      title: '交付外采金额',
      key: 'outJfAmount',
      dataIndex: 'outJfAmount',
      width: 130,
    },
    {
      title: '待签交付外采',
      key: 'unsignOutJfAmount',
      dataIndex: 'unsignOutJfAmount',
      width: 130,
    },
    {
      title: '交付外采已付',
      key: 'outYfJfAmount',
      dataIndex: 'outYfJfAmount',
      width: 130,
    },
    {
      title: '合同状态',
      key: 'contractStatus',
      dataIndex: 'contractStatus',
      width: 100,
    },
    {
      title: '收款状态',
      key: 'moneyStatus',
      dataIndex: 'moneyStatus',
      width: 100,
    },
    {
      title: '是否外采',
      key: 'externalStatus',
      dataIndex: 'externalStatus',
      width: 100,
    },
    {
      title: '签订日期',
      dataIndex: 'signDate',
      width: 120,
      sorter: true,
      customRender: ({ record }) => {
        return record.signDate ? formatToDate(record.signDate) : '-';
      },
    },
    {
      title: '中标日期',
      dataIndex: 'bidDate',
      width: 120,
      customRender: ({ record }) => {
        return record.bidDate ? formatToDate(record.bidDate) : '-';
      },
    },
    {
      title: '开工日期',
      dataIndex: 'commencementDate',
      width: 120,
      customRender: ({ record }) => {
        return record.commencementDate ? formatToDate(record.commencementDate) : '-';
      },
    },
    {
      title: '初验日期',
      dataIndex: 'initialCheckDate',
      width: 120,
      customRender: ({ record }) => {
        return record.initialCheckDate ? formatToDate(record.initialCheckDate) : '-';
      },
    },
    {
      title: '终验日期',
      dataIndex: 'finalCheckDate',
      width: 120,
      customRender: ({ record }) => {
        return record.finalCheckDate ? formatToDate(record.finalCheckDate) : '-';
      },
    },
  ];

  // 搜索表单配置
  const searchFormSchema: FormSchema[] = [
    {
      field: 'name',
      label: '合同名称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入合同名称',
        allowClear: true,
      },
      colProps: { span: 6 },
    },
    {
      field: 'cno',
      label: '合同编号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入合同编号',
        allowClear: true,
      },
      colProps: { span: 6 },
    },
    {
      field: 'custId',
      label: '客户单位',
      component: 'Input',
      slot: 'custId',
      componentProps: {
        placeholder: '请选择客户单位',
        allowClear: true,
      },
      colProps: { span: 6 },
    },
    {
      field: 'finalUserId',
      label: '最终用户',
      component: 'Input',
      slot: 'finalUserId',
      componentProps: {
        placeholder: '请选择最终用户',
        allowClear: true,
      },
      colProps: { span: 6 },
    },
    {
      field: 'ownId',
      label: '项目负责人',
      component: 'Input',
      slot: 'ownId',
      componentProps: {
        placeholder: '请选择项目负责人',
        allowClear: true,
      },
      colProps: { span: 6 },
    },
    {
      field: 'deptId',
      label: '所属部门',
      component: 'TreeSelect',
      componentProps: {
        placeholder: '请选择部门',
        allowClear: true,
        treeDefaultExpandAll: true,
        fieldNames: {
          label: 'name',
          value: 'id',
          children: 'children',
        },
      },
      colProps: { span: 6 },
    },
    {
      field: 'typeStatus',
      label: '合同类型',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '新签', id: '1' },
          { fullName: '续签', id: '2' },
          { fullName: '变更', id: '3' },
        ],
        placeholder: '请选择合同类型',
        allowClear: true,
      },
      colProps: { span: 6 },
    },
    {
      field: 'contractStatus',
      label: '合同状态',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '草稿', id: '1' },
          { fullName: '执行中', id: '2' },
          { fullName: '已完成', id: '3' },
          { fullName: '已终止', id: '4' },
          { fullName: '已归档', id: '5' },
        ],
        placeholder: '请选择合同状态',
        allowClear: true,
      },
      colProps: { span: 6 },
    },
    {
      field: 'moneyStatus',
      label: '收款状态',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '未收款', id: '0' },
          { fullName: '部分收款', id: '1' },
          { fullName: '已结清', id: '2' },
        ],
        placeholder: '请选择收款状态',
        allowClear: true,
      },
      colProps: { span: 6 },
    },
    {
      field: 'externalStatus',
      label: '是否外采',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '否', id: '0' },
          { fullName: '是', id: '1' },
        ],
        placeholder: '请选择是否外采',
        allowClear: true,
      },
      colProps: { span: 6 },
    },
    {
      field: 'signYear',
      label: '合同年度',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '2024', id: 2024 },
          { fullName: '2023', id: 2023 },
          { fullName: '2022', id: 2022 },
          { fullName: '2021', id: 2021 },
          { fullName: '2020', id: 2020 },
        ],
        placeholder: '请选择合同年度',
        allowClear: true,
      },
      colProps: { span: 6 },
    },
    {
      field: '[signDateStart, signDateEnd]',
      label: '签订日期',
      component: 'DatePicker',
      componentProps: {
        placeholder: ['开始日期', '结束日期'],
        allowClear: true,
        range: true,
      },
      colProps: { span: 6 },
    },
    {
      field: '[amountMin, amountMax]',
      label: '合同金额范围',
      component: 'InputNumber',
      componentProps: {
        placeholder: ['最小金额', '最大金额'],
        allowClear: true,
        style: { width: '100%' },
      },
      colProps: { span: 6 },
    },
    {
      field: 'isContinue',
      label: '续签状态',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '正常', id: 0 },
          { fullName: '已续签', id: 1 },
          { fullName: '不续签', id: 9 },
        ],
        placeholder: '请选择续签状态',
        allowClear: true,
      },
      colProps: { span: 6 },
    },
  ];

  // 注册表格
  const [registerTable, { reload }] = useTable({
    title: '合同列表',
    api: getContractList,
    rowKey: 'id',
    columns,
    formConfig: {
      labelWidth: 80,
      schemas: searchFormSchema,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    showIndexColumn: true,
    canResize: true,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
    beforeFetch: params => {
      // 处理日期范围
      if (params['[signDateStart, signDateEnd]'] && params['[signDateStart, signDateEnd]'].length === 2) {
        const [start, end] = params['[signDateStart, signDateEnd]'];
        params.signDateStart = formatToDate(start);
        params.signDateEnd = formatToDate(end);
        delete params['[signDateStart, signDateEnd]'];
      }

      // 处理金额范围
      if (params['[amountMin, amountMax]'] && params['[amountMin, amountMax]'].length === 2) {
        const [min, max] = params['[amountMin, amountMax]'];
        if (min) params.amountMin = min;
        if (max) params.amountMax = max;
        delete params['[amountMin, amountMax]'];
      }

      return params;
    },
    afterFetch: async data => {
      // 使用数据字典翻译字段
      if (data && Array.isArray(data)) {
        return await translateDictFields(data, {
          typeStatus: { dictType: 'contractType', targetField: 'typeStatusText' },
          contractStatus: { dictType: 'contractStatus', targetField: 'contractStatusText' },
          moneyStatus: { dictType: 'HSZ', targetField: 'moneyStatusText' },
        });
      }
      return data;
    },
  });

  // 新增合同
  function handleCreate() {
    openEditDrawer(true, {
      isUpdate: false,
    });
  }

  // 编辑合同
  function handleEdit(record: any) {
    openEditDrawer(true, {
      record,
      isUpdate: true,
    });
  }

  // 从详情页面编辑合同
  function handleEditFromDetail(record: any) {
    openEditDrawer(true, {
      record,
      isUpdate: true,
    });
  }

  // 查看合同详情
  function handleView(record: any) {
    openDetailDrawer(true, {
      contractId: record.id || record.cId,
    });
  }

  // 删除合同
  async function handleDelete(record: any) {
    try {
      await deleteContract(record.id || record.cId);
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      console.error('删除失败:', error);
    }
  }
</script>
