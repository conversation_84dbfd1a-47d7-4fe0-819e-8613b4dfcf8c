package com.xinghuo.project.portfolio.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组合-项目群关联表实体类
 * 对应数据库表：zz_proj_portfolio_program_rel
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_portfolio_program_rel")
public class PortfolioProgramRelEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 项目组合ID
     */
    @TableField("portfolio_id")
    private String portfolioId;

    /**
     * 项目群ID
     */
    @TableField("program_id")
    private String programId;
}
