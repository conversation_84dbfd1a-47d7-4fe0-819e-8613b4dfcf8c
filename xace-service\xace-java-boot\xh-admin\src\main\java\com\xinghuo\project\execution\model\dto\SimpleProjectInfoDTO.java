package com.xinghuo.project.execution.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 项目简要信息DTO
 * 对应API: /api/ppm/project/home/<USER>
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
public class SimpleProjectInfoDTO {

    /**
     * 项目ID
     */
    private String id;

    /**
     * 项目编码
     */
    private String code;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目模板ID
     */
    private Integer schemaId;

    /**
     * 项目状态
     */
    private Integer status;

    /**
     * 项目状态名称
     */
    private String statusName;

    /**
     * 项目经理用户ID
     */
    private String managerUserId;

    /**
     * 项目经理姓名
     */
    private String managerUserName;

    /**
     * 显示名称（项目名称 + 编码）
     */
    private String displayName;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 项目组合ID
     */
    private String refGroup;

    /**
     * 项目组合名称
     */
    private String refGroupName;

    /**
     * 预期结束时间
     */
    private Date expectEndTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 项目状态可更新的数据类型映射
     * key: 状态值, value: 可更新的数据类型列表
     */
    private Map<String, List<String>> projectStatusCanUpdateDataMap;

    /**
     * 是否异常完成
     */
    private Boolean abnormalCompleted;

    /**
     * 工作流状态
     */
    private Integer wfStatus;

    /**
     * 工作流状态名称
     */
    private String wfStatusName;

    /**
     * 备选状态
     */
    private Integer alternativeStatus;

    /**
     * 备选状态名称
     */
    private String alternativeStatusName;

    /**
     * 项目类型
     */
    private Integer projectType;

    /**
     * 项目类型名称
     */
    private String projectTypeName;

    /**
     * 项目优先级
     */
    private Integer priority;

    /**
     * 项目优先级名称
     */
    private String priorityName;

    /**
     * 当前阶段名称
     */
    private String currentPhaseName;

    /**
     * 项目进度百分比
     */
    private Double progressPercent;

    /**
     * 项目健康度
     */
    private Integer healthStatus;

    /**
     * 项目健康度名称
     */
    private String healthStatusName;

    /**
     * 项目风险等级
     */
    private Integer riskLevel;

    /**
     * 项目风险等级名称
     */
    private String riskLevelName;

    /**
     * 项目图标
     */
    private String icon;

    /**
     * 项目标签
     */
    private List<String> tags;

    /**
     * 项目权限列表
     */
    private List<String> permissions;

    /**
     * 扩展信息
     */
    private Map<String, Object> extendInfo;
}
