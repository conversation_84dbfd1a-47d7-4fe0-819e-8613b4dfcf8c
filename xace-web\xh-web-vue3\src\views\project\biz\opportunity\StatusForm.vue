<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="更新商机状态" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, unref, watch } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import { updateBusinessStatus, BusinessModel } from '/@/api/project/business';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'reload']);
  const businessId = ref('');
  const currentStatus = ref('');

  // 表单配置
  const formSchemas: FormSchema[] = [
    {
      field: 'status',
      label: '商机状态',
      component: 'Select',
      required: true,
      componentProps: {
        placeholder: '请选择商机状态',
        options: [
          { fullName: '跟踪中', id: '跟踪中' },
          { fullName: '方案报价中', id: '方案报价中' },
          { fullName: '商务谈判中', id: '商务谈判中' },
          { fullName: '已签', id: '已签' },
          { fullName: '已废弃', id: '已废弃' },
          { fullName: '明年跟踪', id: '明年跟踪' },
        ],
      },
      rules: [{ required: true, trigger: 'change', message: '请选择商机状态' }],
    },
    {
      field: 'projectNo',
      label: '合同编号',
      component: 'Input',
      componentProps: { placeholder: '请输入合同编号' },
      ifShow: ({ values }) => values.status === '已签',
      rules: [{ required: true, trigger: 'blur', message: '请输入合同编号', ifShow: ({ values }) => values.status === '已签' }],
    },
    {
      field: 'lastNote',
      label: '跟踪记录',
      component: 'InputTextArea',
      componentProps: { placeholder: '请输入跟踪记录', maxlength: 2000, showCount: true, rows: 4 },
      rules: [{ max: 2000, message: '跟踪记录最多为2000个字符', trigger: 'blur' }],
    },
  ];

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema }] = useForm({
    labelWidth: 100,
    schemas: formSchemas,
    showActionButtonGroup: false,
  });

  // 监听状态变化，动态调整表单
  watch(
    () => unref(currentStatus),
    (val) => {
      if (val) {
        updateSchema([
          {
            field: 'status',
            defaultValue: val,
          },
        ]);
      }
    },
    { immediate: true }
  );

  // 注册模态框
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });

    if (data.record) {
      businessId.value = data.record.id;
      currentStatus.value = data.record.status;
      
      setFieldsValue({
        status: data.record.status,
        projectNo: data.record.projectNo,
      });
    }
  });

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      await updateBusinessStatus(businessId.value, values);
      createMessage.success('状态更新成功');

      closeModal();
      emit('reload');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
