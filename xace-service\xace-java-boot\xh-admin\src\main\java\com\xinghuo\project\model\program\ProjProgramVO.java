package com.xinghuo.project.model.program;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 项目群视图对象
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@Schema(description = "项目群视图对象")
public class ProjProgramVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 项目群编码
     */
    @Schema(description = "项目群编码")
    private String code;

    /**
     * 项目群名称
     */
    @Schema(description = "项目群名称")
    private String name;

    /**
     * 项目群类型ID
     */
    @Schema(description = "项目群类型ID")
    private String typeId;

    /**
     * 项目群类型名称
     */
    @Schema(description = "项目群类型名称")
    private String typeName;

    /**
     * 项目群经理ID
     */
    @Schema(description = "项目群经理ID")
    private String managerId;

    /**
     * 项目群经理姓名
     */
    @Schema(description = "项目群经理姓名")
    private String managerName;

    /**
     * 项目群描述
     */
    @Schema(description = "项目群描述")
    private String description;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称")
    private String statusName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createdAt;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    private String createdBy;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名")
    private String createdByName;

    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间")
    private Date lastUpdatedAt;

    /**
     * 最后更新人ID
     */
    @Schema(description = "最后更新人ID")
    private String lastUpdatedBy;

    /**
     * 最后更新人姓名
     */
    @Schema(description = "最后更新人姓名")
    private String lastUpdatedByName;

    /**
     * 项目数量
     */
    @Schema(description = "项目数量")
    private Integer projectCount;
}
