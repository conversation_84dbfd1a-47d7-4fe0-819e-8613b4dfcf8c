# 项目报表模块

## 🎯 功能概述

项目报表模块提供了完整的数据统计和可视化功能，包括统计概览、数据图表、明细报表等。采用现代化的设计和响应式布局，支持多种报表类型和数据导出功能。

## 📁 目录结构

```
report/
├── README.md                    # 说明文档 ✅
├── MENU_CONFIG.md              # 菜单配置说明 ✅
├── route.ts                    # 路由配置 ✅
├── index.vue                   # 报表中心主页 ✅
├── invoice/                    # 已开票未收款报表 ✅
│   └── index.vue              # 主页面
├── payment/                    # 收款相关报表
│   └── index.vue              # 占位页面 ✅
├── payable/                    # 付款相关报表
│   └── index.vue              # 占位页面 ✅
├── contract/                   # 合同相关报表（预留）
├── opportunity/                # 商机相关报表（预留）
├── project/                    # 项目相关报表（预留）
├── milestone/                  # 里程碑报表（预留）
└── components/                 # 公共组件（预留）
    ├── ReportHeader.vue        # 报表头部组件
    ├── ExportButton.vue        # 导出按钮组件
    └── StatisticCard.vue       # 统计卡片组件
```

## ✅ 已实现功能

### 1. 报表中心主页 (`index.vue`)
- **路径**: `/project/report`
- **功能**:
  - 📊 **统计概览卡片**：合同总数、合同总金额、待收款金额、商机总数
  - 🧭 **左侧报表导航**：分类展示不同类型的报表链接
  - 📈 **数据可视化图表**：
    - 收款趋势分析（折线图）
    - 合同状态分布（饼图）
    - 部门收款排行（柱状图）
    - 商机转化漏斗（漏斗图）
  - ⚡ **快速操作区域**：常用报表的快速入口
- **特点**:
  - 现代化卡片设计
  - ECharts 数据可视化
  - 响应式布局
  - 真实数据驱动

### 2. 已开票未收款统计报表 (`invoice/`)
- **路径**: `/project/report/invoice`
- **功能**:
  - 🔍 按分部和项目经理查询已开票未收款数据
  - 📊 项目经理汇总统计
  - 📋 明细数据展示（包含所有字段）
  - 📤 Excel导出功能
- **特点**:
  - 完整的字段覆盖
  - 统计汇总功能
  - 现代化UI设计
  - 数据导出支持

## 🚀 后端支持

### 1. 仪表板API接口
- **控制器**: `ProjReportController.java`
- **服务**: `ProjReportService.java` + `ProjReportServiceImpl.java`
- **接口列表**:
  - `GET /project/report/dashboard/data` - 获取仪表板概览数据
  - `GET /project/report/dashboard/payment-trend` - 获取收款趋势数据
  - `GET /project/report/dashboard/contract-status` - 获取合同状态分布
  - `GET /project/report/dashboard/department-rank` - 获取部门收款排行
  - `GET /project/report/dashboard/conversion-funnel` - 获取商机转化漏斗

### 2. 已开票未收款API接口
- `POST /project/report/invoice/list` - 获取明细列表
- `POST /project/report/invoice/summary` - 获取汇总统计
- `POST /project/report/invoice/export` - 导出Excel报表
- `GET /project/report/invoice/departments` - 获取部门列表

### 3. 数据模型
- **VO类**: `DashboardDataVO`, `PaymentTrendVO`, `ContractStatusVO` 等
- **查询表单**: `InvoiceReportQueryForm`
- **统计模型**: `ManagerSummaryVO`

## 🔮 预留扩展

### 1. 合同相关报表 (`contract/`)
- 📋 合同签署统计
- 📊 合同执行进度报表
- 💰 合同金额分析
- 📈 合同趋势分析

### 2. 收款相关报表 (`payment/`)
- 💸 收款进度统计
- ⏰ 逾期收款报表
- 📈 收款趋势分析
- 🏦 银行收款统计

### 3. 商机相关报表 (`opportunity/`)
- 🎯 商机统计分析
- 🔄 商机转化率
- 📊 商机漏斗分析
- 👥 销售人员业绩

### 4. 项目相关报表 (`project/`)
- 📋 项目进度统计
- 🏁 里程碑完成情况
- ⏱️ 延期项目分析
- 💼 项目成本分析

## 开发规范

### 前端规范
1. **文件命名**: 使用小写字母和连字符
2. **组件结构**: 统一使用 PageWrapper + Card 布局
3. **样式规范**: 使用 Less 预处理器，遵循 BEM 命名规范
4. **API调用**: 统一使用 defHttp 进行接口调用

### 后端规范
1. **Controller**: 统一放在 `ProjReportController` 中
2. **Service**: 按功能模块拆分服务接口
3. **VO模型**: 使用明确的数据传输对象
4. **SQL查询**: 使用 JdbcTemplate 进行复杂查询

### 接口规范
1. **路径规范**: `/project/report/{module}/{action}`
2. **请求方式**: 查询使用 POST，导出使用 POST
3. **返回格式**: 统一使用 ActionResult 包装
4. **错误处理**: 统一异常处理和日志记录

## 使用示例

### 前端调用
```typescript
import { getInvoiceReportList } from '/@/api/project/report/invoice';

// 查询数据
const data = await getInvoiceReportList({
  dept: 'YB',
  xmjl: '张三'
});
```

### 后端接口
```java
@PostMapping("/invoice/list")
public ActionResult<List<InvoiceReportVO>> getInvoiceReportList(@RequestBody InvoiceReportQueryForm queryForm) {
    List<InvoiceReportVO> list = projReportService.getInvoiceReportList(queryForm);
    return ActionResult.success(list);
}
```

## 注意事项

1. **性能优化**: 大数据量查询时注意分页和索引优化
2. **权限控制**: 根据用户角色控制数据访问权限
3. **数据安全**: 敏感数据需要脱敏处理
4. **缓存策略**: 对于频繁查询的数据考虑缓存
5. **导出限制**: 大数据量导出需要异步处理

## 后续扩展计划

1. **图表展示**: 集成 ECharts 进行数据可视化
2. **定时报表**: 支持定时生成和发送报表
3. **报表模板**: 支持自定义报表模板
4. **数据钻取**: 支持多维度数据分析
5. **移动端适配**: 优化移动端显示效果
