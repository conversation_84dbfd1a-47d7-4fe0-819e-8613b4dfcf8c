<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="getTitle"
    width="90%"
    showFooter
    :canFullscreen="true"
    :maskClosable="false"
    :keyboard="false"
    class="business-detail-drawer">
    <template #footer>
      <a-space>
        <a-button @click="closeDrawer">
          <template #icon><CloseOutlined /></template>
          关闭
        </a-button>
      </a-space>
    </template>

    <div class="business-detail-container">
      <a-spin :spinning="loading" tip="加载中...">
        <!-- 商机概览卡片 -->
        <a-card :bordered="false" class="overview-card mb-4">
          <template #title>
            <div class="card-title">
              <ProjectOutlined class="title-icon" />
              商机概览 【{{ businessInfo?.businessNo || '-' }}】{{ businessInfo?.projectName || '-' }}
            </div>
          </template>
          <template #extra>
            <a-space>
              <a-tag :color="getProjectLevelColor(businessInfo?.projectLevel)" v-if="businessInfo?.projectLevel">
                {{ businessInfo.projectLevel }}
              </a-tag>
              <a-tag :color="getStatusColor(businessInfo?.status)" v-if="businessInfo?.status">
                {{ businessInfo.status }}
              </a-tag>
            </a-space>
          </template>

          <div class="overview-content">
            <!-- 金额信息 -->
            <div class="overview-item">
              <div class="overview-label">
                <DollarCircleOutlined class="label-icon" />
                软件部金额
              </div>
              <div class="overview-value amount">{{ formatAmount(businessInfo?.deptMoney) }}</div>
            </div>
            <div class="overview-item">
              <div class="overview-label">
                <MoneyCollectOutlined class="label-icon" />
                外采金额
              </div>
              <div class="overview-value amount external">{{ formatAmount(businessInfo?.purchaseMoney) }}</div>
            </div>

            <!-- 毛利信息 -->
            <div class="overview-item">
              <div class="overview-label">
                <BarChartOutlined class="label-icon" />
                毛利金额
              </div>
              <div class="overview-value amount profit">{{ formatAmount(businessInfo?.profitMoney) }}</div>
            </div>

            <!-- 项目负责人 -->
            <div class="overview-item">
              <div class="overview-label">
                <UserOutlined class="label-icon" />
                项目负责人
              </div>
              <div class="overview-value">{{ businessInfo?.projectLeaderName || '-' }}</div>
            </div>

            <!-- 客户单位 -->
            <div class="overview-item">
              <div class="overview-label">
                <TeamOutlined class="label-icon" />
                客户单位
              </div>
              <div class="overview-value">{{ businessInfo?.custName || '-' }}</div>
            </div>

            <!-- 预计落地月份 -->
            <div class="overview-item">
              <div class="overview-label">
                <CalendarOutlined class="label-icon" />
                预计落地月份
              </div>
              <div class="overview-value">{{ businessInfo?.evaSignMonth || '-' }}</div>
            </div>
          </div>
        </a-card>

        <a-tabs v-model:activeKey="activeTab" type="card" class="business-detail-tabs" :tabBarGutter="8" :key="`tabs-${businessId}`">
          <!-- 基本信息标签页 -->
          <a-tab-pane key="basic" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <InfoCircleOutlined class="tab-icon" />
                基本信息
              </span>
            </template>
            <a-card :bordered="false" class="detail-card">
              <template #title>
                <div class="card-title">
                  <UserOutlined class="title-icon" />
                  项目基础信息
                </div>
              </template>
              <div class="detail-grid">
                <div
                  class="detail-item"
                  :class="{
                    'name-field': item.field === '项目名称',
                    'description-field': item.field === '项目简介',
                  }"
                  v-for="item in basicData"
                  :key="item.field">
                  <div class="detail-label">
                    <ProjectOutlined class="label-icon" v-if="item.field === '项目名称'" />
                    <NumberOutlined class="label-icon" v-else-if="item.field === '项目编号'" />
                    <StarOutlined class="label-icon" v-else-if="item.field === '项目等级'" />
                    <TeamOutlined class="label-icon" v-else-if="item.field === '客户单位'" />
                    <UserOutlined class="label-icon" v-else-if="item.field.includes('负责人')" />
                    <BankOutlined class="label-icon" v-else-if="item.field === '所属分部'" />
                    <TagOutlined class="label-icon" v-else-if="item.field === '商机标签'" />
                    <FlagOutlined class="label-icon" v-else-if="item.field === '商机状态'" />
                    <AppstoreOutlined class="label-icon" v-else-if="item.field === '项目类型'" />
                    <FileTextOutlined class="label-icon" v-else />
                    {{ item.label }}
                  </div>
                  <div
                    class="detail-value"
                    :class="{
                      important: item.field === '项目名称' || item.field === '项目负责人',
                      description: item.field === '项目简介',
                    }">
                    {{ item.value }}
                  </div>
                </div>
              </div>
            </a-card>
          </a-tab-pane>

          <!-- 财务信息标签页 -->
          <a-tab-pane key="finance" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <DollarOutlined class="tab-icon" />
                财务信息
              </span>
            </template>
            <a-card :bordered="false" class="detail-card">
              <template #title>
                <div class="card-title">
                  <DollarCircleOutlined class="title-icon" />
                  财务金额信息
                </div>
              </template>
              <div class="detail-grid">
                <div class="detail-item" v-for="item in financeData" :key="item.field">
                  <div class="detail-label">
                    <DollarCircleOutlined class="label-icon" v-if="item.isAmount" />
                    <CalendarOutlined class="label-icon" v-else-if="item.isDate" />
                    <InfoCircleOutlined class="label-icon" v-else />
                    {{ item.label }}
                  </div>
                  <div
                    class="detail-value"
                    :class="{
                      amount: item.isAmount,
                      date: item.isDate,
                    }">
                    {{ item.value }}
                  </div>
                </div>
              </div>
            </a-card>
          </a-tab-pane>

          <!-- 外采信息标签页 -->
          <a-tab-pane key="external" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <ShoppingCartOutlined class="tab-icon" />
                外采信息
              </span>
            </template>
            <a-card :bordered="false" class="detail-card">
              <template #title>
                <div class="card-title">
                  <ShoppingOutlined class="title-icon" />
                  外采计划信息
                </div>
              </template>
              <div class="detail-grid">
                <div class="detail-item" v-for="item in externalData" :key="item.field">
                  <div class="detail-label">
                    <CalendarOutlined class="label-icon" v-if="item.isDate" />
                    <DollarCircleOutlined class="label-icon" v-else-if="item.isAmount" />
                    <InfoCircleOutlined class="label-icon" v-else />
                    {{ item.label }}
                  </div>
                  <div
                    class="detail-value"
                    :class="{
                      amount: item.isAmount,
                      date: item.isDate,
                    }">
                    {{ item.value }}
                  </div>
                </div>
              </div>
            </a-card>
          </a-tab-pane>

          <!-- 部门分配标签页 -->
          <a-tab-pane key="allocation" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <ApartmentOutlined class="tab-icon" />
                部门分配
              </span>
            </template>
            <a-card :bordered="false" class="detail-card">
              <template #title>
                <div class="card-title">
                  <BankOutlined class="title-icon" />
                  部门金额分配
                </div>
              </template>
              <div class="allocation-container">
                <!-- 一部分配 -->
                <div class="allocation-section">
                  <div class="allocation-title">
                    <BankOutlined class="title-icon" />
                    软件一部
                  </div>
                  <div class="allocation-grid">
                    <div class="allocation-item">
                      <div class="allocation-label">一部金额</div>
                      <div class="allocation-value amount">{{ formatAmount(businessInfo?.yfYbAmount) }}</div>
                    </div>
                    <div class="allocation-item">
                      <div class="allocation-label">一部外采</div>
                      <div class="allocation-value amount external">{{ formatAmount(businessInfo?.outYbAmount) }}</div>
                    </div>
                  </div>
                </div>

                <!-- 二部分配 -->
                <div class="allocation-section">
                  <div class="allocation-title">
                    <BankOutlined class="title-icon" />
                    软件二部
                  </div>
                  <div class="allocation-grid">
                    <div class="allocation-item">
                      <div class="allocation-label">二部金额</div>
                      <div class="allocation-value amount">{{ formatAmount(businessInfo?.yfEbAmount) }}</div>
                    </div>
                    <div class="allocation-item">
                      <div class="allocation-label">二部外采</div>
                      <div class="allocation-value amount external">{{ formatAmount(businessInfo?.outEbAmount) }}</div>
                    </div>
                  </div>
                </div>

                <!-- 综合部分配 -->
                <div class="allocation-section">
                  <div class="allocation-title">
                    <BankOutlined class="title-icon" />
                    综合部
                  </div>
                  <div class="allocation-grid">
                    <div class="allocation-item">
                      <div class="allocation-label">综合金额</div>
                      <div class="allocation-value amount">{{ formatAmount(businessInfo?.deptMoney) }}</div>
                    </div>
                    <div class="allocation-item">
                      <div class="allocation-label">综合外采</div>
                      <div class="allocation-value amount external">{{ formatAmount(businessInfo?.purchaseMoney) }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </a-card>
          </a-tab-pane>

          <!-- 跟踪记录标签页 -->
          <a-tab-pane key="track" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <MessageOutlined class="tab-icon" />
                跟踪记录
              </span>
            </template>
            <div class="action-bar mb-4">
              <a-space>
                <a-button type="primary" @click="handleAddTrack">
                  <template #icon><PlusOutlined /></template>
                  添加跟踪记录
                </a-button>
                <a-button @click="handleRefreshTrack">
                  <template #icon><ReloadOutlined /></template>
                  刷新
                </a-button>
              </a-space>
            </div>
            <TrackList ref="trackListRef" :business-id="businessId" />
          </a-tab-pane>

          <!-- 关联合同标签页 -->
          <a-tab-pane key="contract" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <FileTextOutlined class="tab-icon" />
                关联合同
              </span>
            </template>
            <ContractList :business-id="businessId" />
          </a-tab-pane>

          <!-- 商机标签记录标签页 -->
          <a-tab-pane key="datalog" class="tab-pane-content">
            <template #tab>
              <span class="tab-title">
                <TagOutlined class="tab-icon" />
                标签记录
              </span>
            </template>
            <DatalogList :business-id="businessId" />
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </div>

    <TrackFormDrawer @register="registerTrackDrawer" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { computed, ref, unref, nextTick } from 'vue';
  import { BasicDrawer, useDrawerInner, useDrawer } from '/@/components/Drawer';
  import { getBusinessInfo, BusinessModel } from '/@/api/project/business';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    ProjectOutlined,
    DollarCircleOutlined,
    MoneyCollectOutlined,
    BarChartOutlined,
    UserOutlined,
    TeamOutlined,
    CalendarOutlined,
    InfoCircleOutlined,
    DollarOutlined,
    ShoppingCartOutlined,
    ShoppingOutlined,
    ApartmentOutlined,
    BankOutlined,
    MessageOutlined,
    FileTextOutlined,
    TagOutlined,
    PlusOutlined,
    ReloadOutlined,
    CloseOutlined,
    NumberOutlined,
    StarOutlined,
    FlagOutlined,
    AppstoreOutlined,
  } from '@ant-design/icons-vue';

  import TrackFormDrawer from './TrackFormDrawer.vue';
  import TrackList from './TrackList.vue';
  import ContractList from './ContractList.vue';
  import DatalogList from './DatalogList.vue';

  import { Spin } from 'ant-design-vue';

  const ASpin = Spin;

  const { createMessage } = useMessage();
  const [registerTrackDrawer, { openDrawer: openTrackDrawer }] = useDrawer();
  const emit = defineEmits(['register', 'reload']);
  const businessId = ref('');
  const businessInfo = ref<Partial<BusinessModel>>({});
  const loading = ref(false);
  const activeTab = ref('basic');
  const trackListRef = ref(null);

  // 项目等级颜色映射
  const projectLevelColors = {
    'A+': 'red',
    A: 'orange',
    B: 'blue',
    C: 'green',
    D: 'gray',
  };

  // 商机状态颜色映射
  const statusColors = {
    跟踪中: 'blue',
    方案报价中: 'purple',
    商务谈判中: 'orange',
    已签: 'green',
    已废弃: 'red',
    明年跟踪: 'cyan',
  };

  // 获取项目等级颜色
  function getProjectLevelColor(level: string) {
    return projectLevelColors[level] || 'default';
  }

  // 获取商机状态颜色
  function getStatusColor(status: string) {
    return statusColors[status] || 'default';
  }

  // 格式化金额
  function formatAmount(amount: number | undefined) {
    return amount ? `${amount.toLocaleString('zh-CN')}万元` : '¥0.00';
  }

  // 获取标题
  const getTitle = computed(() => {
    return businessInfo.value?.projectName ? `商机详情 - ${businessInfo.value.projectName}` : '商机详情';
  });

  // 基本信息展示数据
  const basicData = computed(() => {
    if (!businessInfo.value) return [];

    return [
      {
        field: '项目名称',
        label: '项目名称',
        value: businessInfo.value.projectName || '-',
      },
      {
        field: '项目编号',
        label: '项目编号',
        value: businessInfo.value.businessNo || '-',
      },
      {
        field: '项目等级',
        label: '项目等级',
        value: businessInfo.value.projectLevel || '-',
      },
      {
        field: '客户单位',
        label: '客户单位',
        value: businessInfo.value.custName || '-',
      },
      {
        field: '市场负责人',
        label: '市场负责人',
        value: businessInfo.value.marketLinkman || '-',
      },
      {
        field: '售前负责人',
        label: '售前负责人',
        value: businessInfo.value.presaleLinkman || '-',
      },
      {
        field: '项目负责人',
        label: '项目负责人',
        value: businessInfo.value.projectLeaderName || '-',
      },
      {
        field: '所属分部',
        label: '所属分部',
        value: businessInfo.value.deptName || '-',
      },
      {
        field: '商机标签',
        label: '商机标签',
        value: businessInfo.value.businessTag || '-',
      },
      {
        field: '商机状态',
        label: '商机状态',
        value: businessInfo.value.status || '-',
      },
      {
        field: '项目类型',
        label: '项目类型',
        value: businessInfo.value.projectType || '-',
      },
      {
        field: '项目简介',
        label: '项目简介',
        value: businessInfo.value.projectContent || '暂无项目简介',
      },
    ];
  });

  // 财务信息展示数据
  const financeData = computed(() => {
    if (!businessInfo.value) return [];

    return [
      {
        field: '软件部金额',
        label: '软件部金额',
        value: formatAmount(businessInfo.value.deptMoney),
        isAmount: true,
      },
      {
        field: '外采金额',
        label: '外采金额',
        value: formatAmount(businessInfo.value.purchaseMoney),
        isAmount: true,
      },
      {
        field: '毛利',
        label: '毛利',
        value: formatAmount(businessInfo.value.profitMoney),
        isAmount: true,
      },
      {
        field: '预计落地月份',
        label: '预计落地月份',
        value: businessInfo.value.evaSignMonth || '-',
        isDate: true,
      },
      {
        field: '合同审核日期',
        label: '合同审核日期',
        value: businessInfo.value.checkDate || '-',
        isDate: true,
      },
      {
        field: '预计首比回款时间',
        label: '预计首比回款时间',
        value: businessInfo.value.evaFirstMonth || '-',
        isDate: true,
      },
      {
        field: '预计首比回款金额',
        label: '预计首比回款金额',
        value: formatAmount(businessInfo.value.evaFirstAmount),
        isAmount: true,
      },
      {
        field: '第二笔回款时间',
        label: '第二笔回款时间',
        value: businessInfo.value.evaSecondMonth || '-',
        isDate: true,
      },
      {
        field: '二笔回款金额',
        label: '二笔回款金额',
        value: formatAmount(businessInfo.value.evaSecondAmount),
        isAmount: true,
      },
    ];
  });

  // 外采信息展示数据
  const externalData = computed(() => {
    if (!businessInfo.value) return [];

    return [
      {
        field: '首次外采月份',
        label: '首次外采月份',
        value: businessInfo.value.evaFirstexternalMonth || '-',
        isDate: true,
      },
      {
        field: '首次外采金额',
        label: '首次外采金额',
        value: formatAmount(businessInfo.value.evaFirstexternalAmount),
        isAmount: true,
      },
      {
        field: '二次外采月份',
        label: '二次外采月份',
        value: businessInfo.value.evaSecondexternalMonth || '-',
        isDate: true,
      },
      {
        field: '二次外采金额',
        label: '二次外采金额',
        value: formatAmount(businessInfo.value.evaSecondexternalAmount),
        isAmount: true,
      },
    ];
  });

  // 注册抽屉
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    try {
      setDrawerProps({ confirmLoading: false, loading: true });

      // 重置状态
      businessInfo.value = {};
      activeTab.value = 'basic';

      businessId.value = data.id;
      console.log('DetailDrawer 接收到的数据:', data);
      console.log('商机ID:', businessId.value);

      if (businessId.value) {
        await loadBusinessInfo();
      } else {
        createMessage.error('商机ID不能为空');
        setDrawerProps({ loading: false });
      }
    } catch (error) {
      console.error('初始化抽屉失败:', error);
      createMessage.error('初始化失败');
      setDrawerProps({ loading: false });
    }
  });

  // 加载商机信息
  async function loadBusinessInfo() {
    if (!businessId.value) {
      console.warn('商机ID为空，跳过加载');
      return;
    }

    try {
      loading.value = true;
      setDrawerProps({ loading: true });

      console.log('开始加载商机信息，ID:', businessId.value);
      const dataRes = await getBusinessInfo(businessId.value);

      if (!dataRes) {
        throw new Error('获取商机数据为空');
      }

      console.log('获取到的商机数据:', dataRes);
      const data = dataRes.data;
      businessInfo.value = data;
      createMessage.success('商机信息加载成功');
    } catch (error) {
      console.error('获取商机信息失败:', error);
      createMessage.error(`获取商机信息失败: ${(error as any)?.message || '未知错误'}`);
      // 重置数据
      businessInfo.value = {};
    } finally {
      loading.value = false;
      setDrawerProps({ loading: false });
    }
  }

  // 添加跟踪记录
  function handleAddTrack() {
    openTrackDrawer(true, {
      businessId: businessId.value,
      businessName: businessInfo.value?.projectName,
      reload: handleRefreshTrack,
    });
  }

  // 刷新跟踪记录
  function handleRefreshTrack() {
    // 刷新跟踪记录列表
    if (trackListRef.value) {
      // 使用可选链避免类型错误
      trackListRef.value?.loadTrackList?.();
    }
  }
</script>

<style lang="less" scoped>
  .business-detail-drawer {
    :deep(.ant-drawer-body) {
      padding: 0;
      background: #f5f7fa;
    }

    :deep(.ant-drawer-header) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-bottom: none;

      .ant-drawer-title {
        color: #fff;
        font-weight: 600;
        font-size: 16px;
      }

      .ant-drawer-close {
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          color: #fff;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }
      }
    }

    :deep(.ant-drawer-footer) {
      background: #fff;
      border-top: 1px solid #e8eaec;
      padding: 16px 24px;
    }
  }

  .business-detail-container {
    height: 100%;
    padding: 24px;
    overflow-y: auto;
  }

  .overview-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaec;

    :deep(.ant-card-head) {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e8eaec;
      border-radius: 12px 12px 0 0;
      padding: 16px 24px;

      .ant-card-head-title {
        padding: 0;
        font-size: 18px;
        font-weight: 600;
      }
    }

    :deep(.ant-card-body) {
      padding: 24px;
    }
  }

  .overview-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;

    .overview-item {
      .overview-label {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;

        .label-icon {
          margin-right: 6px;
          font-size: 16px;
          color: #1890ff;
        }
      }

      .overview-value {
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;

        &.amount {
          font-size: 20px;
          color: #1890ff;
          font-weight: 700;

          &.external {
            color: #fa8c16;
          }

          &.profit {
            color: #52c41a;
          }
        }
      }
    }
  }

  .business-detail-tabs {
    height: calc(100% - 200px);

    :deep(.ant-tabs-nav) {
      background: #fff;
      margin: 0;
      padding: 16px 24px 0;
      border-bottom: 1px solid #e8eaec;
      border-radius: 12px 12px 0 0;

      .ant-tabs-tab {
        border: 1px solid #e8eaec;
        border-radius: 8px 8px 0 0;
        margin-right: 8px;
        padding: 12px 20px;
        background: #fafbfc;
        transition: all 0.3s ease;

        &:hover {
          background: #f0f2f5;
          border-color: #d9d9d9;
        }

        &.ant-tabs-tab-active {
          background: #fff;
          border-color: #1890ff;
          border-bottom-color: #fff;

          .tab-title {
            color: #1890ff;
            font-weight: 600;
          }
        }
      }
    }

    :deep(.ant-tabs-content-holder) {
      background: #f5f7fa;
      padding: 0;
      height: calc(100% - 60px);
      overflow-y: auto;
    }

    :deep(.ant-tabs-tabpane) {
      height: 100%;
      padding: 0;
    }
  }

  .tab-pane-content {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
  }

  .tab-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
    transition: color 0.3s ease;

    .tab-icon {
      font-size: 16px;
    }
  }

  .detail-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e8eaec;
    background: #fff;

    :deep(.ant-card-head) {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e8eaec;
      border-radius: 12px 12px 0 0;
      padding: 16px 24px;

      .ant-card-head-title {
        padding: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }

    :deep(.ant-card-body) {
      padding: 32px 24px;
    }
  }

  .card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2c3e50;

    .title-icon {
      font-size: 18px;
      color: #1890ff;
    }
  }

  .detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px 32px;

    .detail-item {
      .detail-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;
        display: flex;
        align-items: center;

        .label-icon {
          margin-right: 6px;
          font-size: 14px;
          color: #1890ff;
        }
      }

      .detail-value {
        font-size: 15px;
        font-weight: 500;
        color: #2c3e50;
        word-break: break-all;
        line-height: 1.5;

        &.amount {
          font-size: 16px;
          color: #1890ff;
          font-weight: 600;
        }

        &.date {
          color: #52c41a;
          font-weight: 500;
        }

        // 重要字段突出显示
        &.important {
          font-size: 16px;
          font-weight: 700;
          color: #1a1a1a;
        }

        // 描述类字段
        &.description {
          font-size: 14px;
          line-height: 1.6;
          color: #595959;
          background: #fafafa;
          padding: 12px;
          border-radius: 6px;
          border-left: 3px solid #1890ff;
        }
      }

      // 特殊字段样式
      &.name-field {
        grid-column: 1 / -1; // 占满整行

        .detail-value {
          font-size: 17px;
          font-weight: 700;
          color: #1a1a1a;
          background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
          padding: 16px;
          border-radius: 8px;
          border: 1px solid #e6f0ff;
        }
      }

      &.description-field {
        grid-column: 1 / -1; // 占满整行
      }
    }
  }

  .allocation-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;

    .allocation-section {
      background: #fff;
      border: 1px solid #e8eaec;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);

      .allocation-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e8eaec;

        .title-icon {
          font-size: 18px;
          color: #1890ff;
        }
      }

      .allocation-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;

        .allocation-item {
          .allocation-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
            font-weight: 500;
          }

          .allocation-value {
            font-size: 16px;
            font-weight: 600;
            padding: 12px;
            border-radius: 6px;
            text-align: center;

            &.amount {
              background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
              color: #1890ff;
              border: 1px solid #b7eb8f;

              &.external {
                background: linear-gradient(135deg, #fff7e6 0%, #fffbe6 100%);
                color: #fa8c16;
                border: 1px solid #ffd591;
              }
            }
          }
        }
      }
    }
  }

  .action-bar {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }

  .mb-4 {
    margin-bottom: 24px;
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .business-detail-drawer {
      :deep(.ant-drawer) {
        width: 95% !important;
      }
    }

    .business-detail-container {
      padding: 16px;
    }

    .overview-content {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .detail-grid {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px 24px;
    }

    .tab-pane-content {
      padding: 16px;
    }

    .detail-card {
      :deep(.ant-card-body) {
        padding: 24px 16px;
      }
    }

    .allocation-container {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 768px) {
    .business-detail-tabs {
      :deep(.ant-tabs-nav) {
        padding: 12px 16px 0;

        .ant-tabs-tab {
          padding: 8px 12px;
          margin-right: 4px;

          .tab-title {
            font-size: 12px;

            .tab-icon {
              font-size: 14px;
            }
          }
        }
      }
    }

    .tab-pane-content {
      padding: 12px;
    }

    .overview-content {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .detail-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .detail-card {
      border-radius: 8px;

      :deep(.ant-card-head) {
        padding: 12px 16px;
        border-radius: 8px 8px 0 0;

        .ant-card-head-title {
          font-size: 14px;
        }
      }

      :deep(.ant-card-body) {
        padding: 20px 16px;
      }
    }

    .card-title {
      .title-icon {
        font-size: 16px;
      }
    }

    .allocation-container {
      .allocation-section {
        .allocation-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }

  // 滚动条美化
  .business-detail-container,
  .tab-pane-content {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 标签样式
  :deep(.ant-tag) {
    border-radius: 6px;
    padding: 4px 12px;
    font-weight: 500;
    border: none;
  }
</style>
