package com.xinghuo.project.core.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.core.entity.TagEntity;
import com.xinghuo.project.core.model.tag.TagPagination;

import java.util.List;

/**
 * 标签服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface TagService extends BaseService<TagEntity> {

    /**
     * 分页查询标签列表
     *
     * @param pagination 查询条件
     * @return 标签列表
     */
    List<TagEntity> getList(TagPagination pagination);

    /**
     * 根据ID查询标签信息
     *
     * @param id 标签ID
     * @return 标签信息
     */
    TagEntity getInfo(String id);

    /**
     * 创建标签
     *
     * @param entity 标签信息
     * @return 标签ID
     */
    void create(TagEntity entity);

    /**
     * 更新标签
     *
     * @param id 标签ID
     * @param entity 更新信息
     */
    void update(String id, TagEntity entity);

    /**
     * 删除标签
     *
     * @param id 标签ID
     */
    void delete(String id);



    /**
     * 检查标签名称是否存在
     *
     * @param tagName 标签名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByTagName(String tagName, String excludeId);

    /**
     * 获取标签选择列表
     *
     * @param keyword 关键字
     * @return 标签列表
     */
    List<TagEntity> getSelectList(String keyword);
}
