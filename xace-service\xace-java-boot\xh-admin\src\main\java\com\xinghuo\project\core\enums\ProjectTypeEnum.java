package com.xinghuo.project.core.enums;

/**
 * 项目类型枚举
 */
public enum ProjectTypeEnum {
    
    PRODUCT_DEVELOPMENT("product_development", "产品研发项目"),
    CUSTOMER_PROJECT("customer_project", "客户定制项目"),
    INTERNAL_IMPROVEMENT("internal_improvement", "内部改进项目"),
    RESEARCH("research", "研究项目"),
    MAINTENANCE("maintenance", "维护项目"),
    CONSULTING("consulting", "咨询项目");
    
    private final String code;
    private final String name;
    
    ProjectTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static ProjectTypeEnum getByCode(String code) {
        for (ProjectTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
