package com.xinghuo.project.template.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.template.entity.WorkProductLibraryEntity;
import com.xinghuo.project.template.model.WorkProductLibraryPagination;
import com.xinghuo.project.template.model.dto.WorkProductLibraryDTO;
import com.xinghuo.project.template.service.WorkProductLibraryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 标准交付物库管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "标准交付物库管理", description = "标准交付物库管理相关接口")
@RestController
@RequestMapping("/api/project/template/workProductLibrary")
public class WorkProductLibraryController {

    @Resource
    private WorkProductLibraryService workProductLibraryService;

    /**
     * 获取交付物库列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取交付物库列表")
    public ActionResult<PageListVO<WorkProductLibraryEntity>> list(@RequestBody WorkProductLibraryPagination pagination) {
        try {
            List<WorkProductLibraryEntity> list = workProductLibraryService.getList(pagination);
            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(list, page);
        } catch (Exception e) {
            log.error("获取交付物库列表失败", e);
            return ActionResult.fail("获取交付物库列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据状态获取交付物列表
     */
    @GetMapping("/getListByStatus/{statusId}")
    @Operation(summary = "根据状态获取交付物列表")
    public ActionResult<List<WorkProductLibraryEntity>> getListByStatus(
            @Parameter(description = "状态ID") @PathVariable String statusId) {
        try {
            List<WorkProductLibraryEntity> list = workProductLibraryService.getByStatus(statusId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据状态获取交付物列表失败", e);
            return ActionResult.fail("获取交付物列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取交付物详情
     */
    @GetMapping("/getDetailInfo/{id}")
    @Operation(summary = "获取交付物详情")
    public ActionResult<WorkProductLibraryDTO> getDetailInfo(
            @Parameter(description = "交付物ID") @PathVariable String id) {
        try {
            WorkProductLibraryDTO dto = workProductLibraryService.getDetailInfo(id);
            if (dto == null) {
                return ActionResult.fail("交付物不存在");
            }
            return ActionResult.success(dto);
        } catch (Exception e) {
            log.error("获取交付物详情失败", e);
            return ActionResult.fail("获取交付物详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取交付物基本信息
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取交付物基本信息")
    public ActionResult<WorkProductLibraryEntity> getInfo(
            @Parameter(description = "交付物ID") @PathVariable String id) {
        try {
            WorkProductLibraryEntity entity = workProductLibraryService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("交付物不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("获取交付物基本信息失败", e);
            return ActionResult.fail("获取交付物基本信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据编码获取交付物
     */
    @GetMapping("/getByCode/{code}")
    @Operation(summary = "根据编码获取交付物")
    public ActionResult<WorkProductLibraryEntity> getByCode(
            @Parameter(description = "交付物编码") @PathVariable String code) {
        try {
            WorkProductLibraryEntity entity = workProductLibraryService.getByCode(code);
            if (entity == null) {
                return ActionResult.fail("交付物不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("根据编码获取交付物失败", e);
            return ActionResult.fail("获取交付物失败：" + e.getMessage());
        }
    }

    /**
     * 创建交付物
     */
    @PostMapping("/create")
    @Operation(summary = "创建交付物")
    public ActionResult<String> create(@RequestBody @Valid WorkProductLibraryDTO workProductDTO) {
        try {
            String id = workProductLibraryService.create(workProductDTO);
            return ActionResult.success("创建成功", id);
        } catch (Exception e) {
            log.error("创建交付物失败", e);
            return ActionResult.fail("创建交付物失败：" + e.getMessage());
        }
    }

    /**
     * 更新交付物
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新交付物")
    public ActionResult<String> update(
            @Parameter(description = "交付物ID") @PathVariable String id,
            @RequestBody @Valid WorkProductLibraryDTO workProductDTO) {
        try {
            workProductLibraryService.update(id, workProductDTO);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新交付物失败", e);
            return ActionResult.fail("更新交付物失败：" + e.getMessage());
        }
    }

    /**
     * 删除交付物
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除交付物")
    public ActionResult<String> delete(
            @Parameter(description = "交付物ID") @PathVariable String id) {
        try {
            workProductLibraryService.delete(id);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除交付物失败", e);
            return ActionResult.fail("删除交付物失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除交付物
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除交付物")
    public ActionResult<String> batchDelete(@RequestBody List<String> ids) {
        try {
            workProductLibraryService.batchDelete(ids);
            return ActionResult.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除交付物失败", e);
            return ActionResult.fail("批量删除交付物失败：" + e.getMessage());
        }
    }

    /**
     * 更新交付物状态
     */
    @PutMapping("/updateStatus/{id}")
    @Operation(summary = "更新交付物状态")
    public ActionResult<String> updateStatus(
            @Parameter(description = "交付物ID") @PathVariable String id,
            @RequestParam String statusId) {
        try {
            workProductLibraryService.updateStatus(id, statusId);
            return ActionResult.success("状态更新成功");
        } catch (Exception e) {
            log.error("更新交付物状态失败", e);
            return ActionResult.fail("更新交付物状态失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新状态
     */
    @PutMapping("/batchUpdateStatus")
    @Operation(summary = "批量更新状态")
    public ActionResult<String> batchUpdateStatus(
            @RequestBody List<String> ids,
            @RequestParam String statusId) {
        try {
            workProductLibraryService.batchUpdateStatus(ids, statusId);
            return ActionResult.success("批量更新状态成功");
        } catch (Exception e) {
            log.error("批量更新交付物状态失败", e);
            return ActionResult.fail("批量更新状态失败：" + e.getMessage());
        }
    }

    /**
     * 启用交付物
     */
    @PutMapping("/enable/{id}")
    @Operation(summary = "启用交付物")
    public ActionResult<String> enable(
            @Parameter(description = "交付物ID") @PathVariable String id) {
        try {
            workProductLibraryService.enable(id);
            return ActionResult.success("启用成功");
        } catch (Exception e) {
            log.error("启用交付物失败", e);
            return ActionResult.fail("启用交付物失败：" + e.getMessage());
        }
    }

    /**
     * 禁用交付物
     */
    @PutMapping("/disable/{id}")
    @Operation(summary = "禁用交付物")
    public ActionResult<String> disable(
            @Parameter(description = "交付物ID") @PathVariable String id) {
        try {
            workProductLibraryService.disable(id);
            return ActionResult.success("禁用成功");
        } catch (Exception e) {
            log.error("禁用交付物失败", e);
            return ActionResult.fail("禁用交付物失败：" + e.getMessage());
        }
    }

    /**
     * 复制交付物
     */
    @PostMapping("/copy/{id}")
    @Operation(summary = "复制交付物")
    public ActionResult<String> copy(
            @Parameter(description = "交付物ID") @PathVariable String id,
            @RequestParam String newName) {
        try {
            String newId = workProductLibraryService.copy(id, newName);
            return ActionResult.success("复制成功", newId);
        } catch (Exception e) {
            log.error("复制交付物失败", e);
            return ActionResult.fail("复制交付物失败：" + e.getMessage());
        }
    }

    /**
     * 检查交付物名称是否存在
     */
    @GetMapping("/checkNameExists")
    @Operation(summary = "检查交付物名称是否存在")
    public ActionResult<Boolean> checkNameExists(
            @RequestParam String name,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = workProductLibraryService.isExistByName(name, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查交付物名称失败", e);
            return ActionResult.fail("检查交付物名称失败：" + e.getMessage());
        }
    }

    /**
     * 检查交付物编码是否存在
     */
    @GetMapping("/checkCodeExists")
    @Operation(summary = "检查交付物编码是否存在")
    public ActionResult<Boolean> checkCodeExists(
            @RequestParam String code,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = workProductLibraryService.isExistByCode(code, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查交付物编码失败", e);
            return ActionResult.fail("检查交付物编码失败：" + e.getMessage());
        }
    }

    /**
     * 获取交付物选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取交付物选择列表")
    public ActionResult<List<WorkProductLibraryEntity>> getSelectList(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String statusId,
            @RequestParam(required = false) String typeId) {
        try {
            List<WorkProductLibraryEntity> list = workProductLibraryService.getSelectList(keyword, statusId, typeId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("获取交付物选择列表失败", e);
            return ActionResult.fail("获取交付物选择列表失败：" + e.getMessage());
        }
    }

    /**
     * 生成交付物编码
     */
    @GetMapping("/generateCode")
    @Operation(summary = "生成交付物编码")
    public ActionResult<String> generateCode() {
        try {
            String code = workProductLibraryService.generateCode();
            return ActionResult.success(code);
        } catch (Exception e) {
            log.error("生成交付物编码失败", e);
            return ActionResult.fail("生成交付物编码失败：" + e.getMessage());
        }
    }

    /**
     * 获取交付物统计信息
     */
    @PostMapping("/getWorkProductStatistics")
    @Operation(summary = "获取交付物统计信息")
    public ActionResult<List<Map<String, Object>>> getWorkProductStatistics(@RequestBody Map<String, Object> params) {
        try {
            List<Map<String, Object>> statistics = workProductLibraryService.getWorkProductStatistics(params);
            return ActionResult.success(statistics);
        } catch (Exception e) {
            log.error("获取交付物统计信息失败", e);
            return ActionResult.fail("获取交付物统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目模板ID查询关联的交付物
     */
    @GetMapping("/getByProjectTemplateId/{projectTplId}")
    @Operation(summary = "根据项目模板ID查询关联的交付物")
    public ActionResult<List<WorkProductLibraryEntity>> getByProjectTemplateId(
            @Parameter(description = "项目模板ID") @PathVariable String projectTplId) {
        try {
            List<WorkProductLibraryEntity> list = workProductLibraryService.getByProjectTemplateId(projectTplId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据项目模板ID查询关联的交付物失败", e);
            return ActionResult.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 更新交付物与项目模板的关联关系
     */
    @PutMapping("/updateProjectTemplateRelations/{workProductId}")
    @Operation(summary = "更新交付物与项目模板的关联关系")
    public ActionResult<String> updateProjectTemplateRelations(
            @Parameter(description = "交付物ID") @PathVariable String workProductId,
            @RequestBody List<String> projectTemplateIds) {
        try {
            workProductLibraryService.updateProjectTemplateRelations(workProductId, projectTemplateIds);
            return ActionResult.success("更新关联关系成功");
        } catch (Exception e) {
            log.error("更新交付物与项目模板的关联关系失败", e);
            return ActionResult.fail("更新关联关系失败：" + e.getMessage());
        }
    }

    /**
     * 获取交付物使用情况
     */
    @GetMapping("/getWorkProductUsageInfo/{id}")
    @Operation(summary = "获取交付物使用情况")
    public ActionResult<Map<String, Object>> getWorkProductUsageInfo(
            @Parameter(description = "交付物ID") @PathVariable String id) {
        try {
            Map<String, Object> usageInfo = workProductLibraryService.getWorkProductUsageInfo(id);
            return ActionResult.success(usageInfo);
        } catch (Exception e) {
            log.error("获取交付物使用情况失败", e);
            return ActionResult.fail("获取交付物使用情况失败：" + e.getMessage());
        }
    }

    /**
     * 根据类型获取交付物列表
     */
    @GetMapping("/getByType")
    @Operation(summary = "根据类型获取交付物列表")
    public ActionResult<List<WorkProductLibraryEntity>> getByType(
            @RequestParam String typeId,
            @RequestParam(required = false) String subTypeId) {
        try {
            List<WorkProductLibraryEntity> list = workProductLibraryService.getByType(typeId, subTypeId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据类型获取交付物列表失败", e);
            return ActionResult.fail("获取交付物列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据默认角色获取交付物列表
     */
    @GetMapping("/getByDefaultRole/{defaultRoleId}")
    @Operation(summary = "根据默认角色获取交付物列表")
    public ActionResult<List<WorkProductLibraryEntity>> getByDefaultRole(
            @Parameter(description = "默认角色ID") @PathVariable String defaultRoleId) {
        try {
            List<WorkProductLibraryEntity> list = workProductLibraryService.getByDefaultRole(defaultRoleId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据默认角色获取交付物列表失败", e);
            return ActionResult.fail("获取交付物列表失败：" + e.getMessage());
        }
    }
}
