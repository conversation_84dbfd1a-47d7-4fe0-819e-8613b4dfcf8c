package com.xinghuo.project.model.program;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 项目群表单对象
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@Schema(description = "项目群表单对象")
public class ProjProgramForm {

    /**
     * 主键ID（更新时使用）
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 项目群编码
     */
    @Schema(description = "项目群编码")
    @Size(max = 50, message = "项目群编码长度不能超过50个字符")
    private String code;

    /**
     * 项目群名称
     */
    @Schema(description = "项目群名称")
    @NotBlank(message = "项目群名称不能为空")
    @Size(max = 255, message = "项目群名称长度不能超过255个字符")
    private String name;

    /**
     * 项目群类型ID
     */
    @Schema(description = "项目群类型ID")
    @NotBlank(message = "项目群类型不能为空")
    @Size(max = 50, message = "项目群类型ID长度不能超过50个字符")
    private String typeId;

    /**
     * 项目群经理ID
     */
    @Schema(description = "项目群经理ID")
    @NotBlank(message = "项目群经理不能为空")
    @Size(max = 50, message = "项目群经理ID长度不能超过50个字符")
    private String managerId;

    /**
     * 项目群描述
     */
    @Schema(description = "项目群描述")
    private String description;

    /**
     * 状态
     */
    @Schema(description = "状态")
    @Size(max = 50, message = "状态长度不能超过50个字符")
    private String status;
}
