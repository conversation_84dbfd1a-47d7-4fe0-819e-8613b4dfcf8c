package com.xinghuo.project.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.entity.ProjPortfolioProgramRelEntity;

import java.util.List;

/**
 * 组合-项目群关联表服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ProjPortfolioProgramRelService extends BaseService<ProjPortfolioProgramRelEntity> {

    /**
     * 根据组合ID查询关联的项目群ID列表
     *
     * @param portfolioId 组合ID
     * @return 项目群ID列表
     */
    List<String> getProgramIdsByPortfolioId(String portfolioId);

    /**
     * 根据项目群ID查询关联的组合ID列表
     *
     * @param programId 项目群ID
     * @return 组合ID列表
     */
    List<String> getPortfolioIdsByProgramId(String programId);

    /**
     * 检查组合和项目群的关联关系是否存在
     *
     * @param portfolioId 组合ID
     * @param programId 项目群ID
     * @return 是否存在关联关系
     */
    boolean isRelationExists(String portfolioId, String programId);

    /**
     * 创建组合-项目群关联关系
     *
     * @param portfolioId 组合ID
     * @param programId 项目群ID
     * @return 创建结果
     */
    boolean createRelation(String portfolioId, String programId);

    /**
     * 删除组合-项目群关联关系
     *
     * @param portfolioId 组合ID
     * @param programId 项目群ID
     * @return 删除结果
     */
    boolean deleteRelation(String portfolioId, String programId);

    /**
     * 批量设置组合的项目群关联
     *
     * @param portfolioId 组合ID
     * @param programIds 项目群ID列表
     * @return 设置结果
     */
    boolean setPortfolioProgramRelations(String portfolioId, List<String> programIds);

    /**
     * 批量设置项目群的组合关联
     *
     * @param programId 项目群ID
     * @param portfolioIds 组合ID列表
     * @return 设置结果
     */
    boolean setProgramPortfolioRelations(String programId, List<String> portfolioIds);

    /**
     * 删除组合的所有项目群关联
     *
     * @param portfolioId 组合ID
     * @return 删除结果
     */
    boolean deleteByPortfolioId(String portfolioId);

    /**
     * 删除项目群的所有组合关联
     *
     * @param programId 项目群ID
     * @return 删除结果
     */
    boolean deleteByProgramId(String programId);
}
