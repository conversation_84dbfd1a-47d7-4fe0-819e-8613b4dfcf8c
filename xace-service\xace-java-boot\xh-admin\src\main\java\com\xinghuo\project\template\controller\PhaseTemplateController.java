package com.xinghuo.project.template.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.template.entity.PhaseTemplateEntity;
import com.xinghuo.project.template.model.PhaseTemplatePagination;
import com.xinghuo.project.template.service.PhaseTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 阶段模板管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "阶段模板管理", description = "阶段模板管理相关接口")
@RestController
@RequestMapping("/api/project/template/phaseTemplate")
public class PhaseTemplateController {

    @Resource
    private PhaseTemplateService phaseTemplateService;

    /**
     * 获取阶段模板列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取阶段模板列表")
    public ActionResult<PageListVO<PhaseTemplateEntity>> list(@RequestBody PhaseTemplatePagination pagination) {
        try {
            List<PhaseTemplateEntity> list = phaseTemplateService.getList(pagination);
            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(list, page);
        } catch (Exception e) {
            log.error("获取阶段模板列表失败", e);
            return ActionResult.fail("获取阶段模板列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据状态获取阶段模板列表
     */
    @GetMapping("/getListByStatus/{status}")
    @Operation(summary = "根据状态获取阶段模板列表")
    public ActionResult<List<PhaseTemplateEntity>> getListByStatus(
            @Parameter(description = "状态") @PathVariable Integer status) {
        try {
            List<PhaseTemplateEntity> list = phaseTemplateService.getListByStatus(status);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据状态获取阶段模板列表失败", e);
            return ActionResult.fail("获取阶段模板列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取阶段模板详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取阶段模板详情")
    public ActionResult<PhaseTemplateEntity> getInfo(
            @Parameter(description = "阶段模板ID") @PathVariable String id) {
        try {
            PhaseTemplateEntity entity = phaseTemplateService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("阶段模板不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("获取阶段模板详情失败", e);
            return ActionResult.fail("获取阶段模板详情失败：" + e.getMessage());
        }
    }

    /**
     * 根据编码获取阶段模板
     */
    @GetMapping("/getByCode/{code}")
    @Operation(summary = "根据编码获取阶段模板")
    public ActionResult<PhaseTemplateEntity> getByCode(
            @Parameter(description = "阶段编码") @PathVariable String code) {
        try {
            PhaseTemplateEntity entity = phaseTemplateService.getByCode(code);
            if (entity == null) {
                return ActionResult.fail("阶段模板不存在");
            }
            return ActionResult.success(entity);
        } catch (Exception e) {
            log.error("根据编码获取阶段模板失败", e);
            return ActionResult.fail("获取阶段模板失败：" + e.getMessage());
        }
    }

    /**
     * 创建阶段模板
     */
    @PostMapping("/create")
    @Operation(summary = "创建阶段模板")
    public ActionResult<String> create(@RequestBody @Valid PhaseTemplateEntity entity) {
        try {
            String id = phaseTemplateService.create(entity);
            return ActionResult.success("创建成功", id);
        } catch (Exception e) {
            log.error("创建阶段模板失败", e);
            return ActionResult.fail("创建阶段模板失败：" + e.getMessage());
        }
    }

    /**
     * 更新阶段模板
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新阶段模板")
    public ActionResult<String> update(
            @Parameter(description = "阶段模板ID") @PathVariable String id,
            @RequestBody @Valid PhaseTemplateEntity entity) {
        try {
            phaseTemplateService.update(id, entity);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新阶段模板失败", e);
            return ActionResult.fail("更新阶段模板失败：" + e.getMessage());
        }
    }

    /**
     * 删除阶段模板
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除阶段模板")
    public ActionResult<String> delete(
            @Parameter(description = "阶段模板ID") @PathVariable String id) {
        try {
            phaseTemplateService.delete(id);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除阶段模板失败", e);
            return ActionResult.fail("删除阶段模板失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除阶段模板
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除阶段模板")
    public ActionResult<String> batchDelete(@RequestBody List<String> ids) {
        try {
            phaseTemplateService.batchDelete(ids);
            return ActionResult.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除阶段模板失败", e);
            return ActionResult.fail("批量删除阶段模板失败：" + e.getMessage());
        }
    }

    /**
     * 更新阶段模板状态
     */
    @PutMapping("/updateStatus/{id}")
    @Operation(summary = "更新阶段模板状态")
    public ActionResult<String> updateStatus(
            @Parameter(description = "阶段模板ID") @PathVariable String id,
            @RequestParam Integer status) {
        try {
            phaseTemplateService.updateStatus(id, status);
            return ActionResult.success("状态更新成功");
        } catch (Exception e) {
            log.error("更新阶段模板状态失败", e);
            return ActionResult.fail("更新阶段模板状态失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新状态
     */
    @PutMapping("/batchUpdateStatus")
    @Operation(summary = "批量更新状态")
    public ActionResult<String> batchUpdateStatus(
            @RequestBody List<String> ids,
            @RequestParam Integer status) {
        try {
            phaseTemplateService.batchUpdateStatus(ids, status);
            return ActionResult.success("批量更新状态成功");
        } catch (Exception e) {
            log.error("批量更新阶段模板状态失败", e);
            return ActionResult.fail("批量更新状态失败：" + e.getMessage());
        }
    }

    /**
     * 启用阶段模板
     */
    @PutMapping("/enable/{id}")
    @Operation(summary = "启用阶段模板")
    public ActionResult<String> enable(
            @Parameter(description = "阶段模板ID") @PathVariable String id) {
        try {
            phaseTemplateService.enable(id);
            return ActionResult.success("启用成功");
        } catch (Exception e) {
            log.error("启用阶段模板失败", e);
            return ActionResult.fail("启用阶段模板失败：" + e.getMessage());
        }
    }

    /**
     * 禁用阶段模板
     */
    @PutMapping("/disable/{id}")
    @Operation(summary = "禁用阶段模板")
    public ActionResult<String> disable(
            @Parameter(description = "阶段模板ID") @PathVariable String id) {
        try {
            phaseTemplateService.disable(id);
            return ActionResult.success("禁用成功");
        } catch (Exception e) {
            log.error("禁用阶段模板失败", e);
            return ActionResult.fail("禁用阶段模板失败：" + e.getMessage());
        }
    }

    /**
     * 复制阶段模板
     */
    @PostMapping("/copy/{id}")
    @Operation(summary = "复制阶段模板")
    public ActionResult<String> copy(
            @Parameter(description = "阶段模板ID") @PathVariable String id,
            @RequestParam String newName) {
        try {
            String newId = phaseTemplateService.copy(id, newName);
            return ActionResult.success("复制成功", newId);
        } catch (Exception e) {
            log.error("复制阶段模板失败", e);
            return ActionResult.fail("复制阶段模板失败：" + e.getMessage());
        }
    }

    /**
     * 检查阶段编码是否存在
     */
    @GetMapping("/checkCodeExists")
    @Operation(summary = "检查阶段编码是否存在")
    public ActionResult<Boolean> checkCodeExists(
            @RequestParam String code,
            @RequestParam(required = false) String excludeId) {
        try {
            boolean exists = phaseTemplateService.isExistByCode(code, excludeId);
            return ActionResult.success(exists);
        } catch (Exception e) {
            log.error("检查阶段编码失败", e);
            return ActionResult.fail("检查阶段编码失败：" + e.getMessage());
        }
    }

    /**
     * 获取阶段模板选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取阶段模板选择列表")
    public ActionResult<List<PhaseTemplateEntity>> getSelectList(
            @RequestParam(required = false) String keyword) {
        try {
            List<PhaseTemplateEntity> list = phaseTemplateService.getSelectList(keyword);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("获取阶段模板选择列表失败", e);
            return ActionResult.fail("获取阶段模板选择列表失败：" + e.getMessage());
        }
    }

    /**
     * 生成阶段编码
     */
    @GetMapping("/generateCode")
    @Operation(summary = "生成阶段编码")
    public ActionResult<String> generateCode() {
        try {
            String code = phaseTemplateService.generateCode();
            return ActionResult.success(code);
        } catch (Exception e) {
            log.error("生成阶段编码失败", e);
            return ActionResult.fail("生成阶段编码失败：" + e.getMessage());
        }
    }
}
