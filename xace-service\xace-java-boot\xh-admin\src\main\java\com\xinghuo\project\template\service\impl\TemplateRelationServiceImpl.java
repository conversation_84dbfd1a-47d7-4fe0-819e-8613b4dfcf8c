package com.xinghuo.project.template.service.impl;

import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.template.dao.TemplateRelationMapper;
import com.xinghuo.project.template.entity.TemplateRelationEntity;
import com.xinghuo.project.template.service.TemplateRelationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用模板关联服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class TemplateRelationServiceImpl extends BaseServiceImpl<TemplateRelationMapper, TemplateRelationEntity> implements TemplateRelationService {

    @Resource
    private TemplateRelationMapper templateRelationMapper;

    @Override
    public List<String> getTargetTemplateIds(String sourceTemplateId, String sourceTemplateType, String targetTemplateType) {
        if (StrXhUtil.isEmpty(sourceTemplateId) || StrXhUtil.isEmpty(sourceTemplateType) || StrXhUtil.isEmpty(targetTemplateType)) {
            return new ArrayList<>();
        }
        return templateRelationMapper.selectTargetTemplateIds(sourceTemplateId, sourceTemplateType, targetTemplateType);
    }

    @Override
    public List<String> getSourceTemplateIds(String targetTemplateId, String targetTemplateType, String sourceTemplateType) {
        if (StrXhUtil.isEmpty(targetTemplateId) || StrXhUtil.isEmpty(targetTemplateType) || StrXhUtil.isEmpty(sourceTemplateType)) {
            return new ArrayList<>();
        }
        return templateRelationMapper.selectSourceTemplateIds(targetTemplateId, targetTemplateType, sourceTemplateType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTemplateRelations(String sourceTemplateId, String sourceTemplateType, 
                                       List<String> targetTemplateIds, String targetTemplateType) {
        if (StrXhUtil.isEmpty(sourceTemplateId) || StrXhUtil.isEmpty(sourceTemplateType) || StrXhUtil.isEmpty(targetTemplateType)) {
            log.warn("更新模板关联关系参数无效");
            throw new RuntimeException("参数无效");
        }

        // 删除原有关联关系
        templateRelationMapper.deleteBySourceTemplate(sourceTemplateId, sourceTemplateType);

        // 保存新的关联关系
        if (targetTemplateIds != null && !targetTemplateIds.isEmpty()) {
            List<TemplateRelationEntity> relations = new ArrayList<>();
            for (String targetTemplateId : targetTemplateIds) {
                TemplateRelationEntity relation = new TemplateRelationEntity();
                relation.setId(RandomUtil.snowId());
                relation.setSourceTemplateId(sourceTemplateId);
                relation.setSourceTemplateType(sourceTemplateType);
                relation.setTargetTemplateId(targetTemplateId);
                relation.setTargetTemplateType(targetTemplateType);
                relations.add(relation);
            }
            templateRelationMapper.batchInsert(relations);
        }

        log.info("更新模板关联关系成功, 源模板ID: {}, 源模板类型: {}, 目标模板数量: {}", 
                sourceTemplateId, sourceTemplateType, targetTemplateIds != null ? targetTemplateIds.size() : 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBySourceTemplate(String sourceTemplateId, String sourceTemplateType) {
        if (StrXhUtil.isEmpty(sourceTemplateId) || StrXhUtil.isEmpty(sourceTemplateType)) {
            log.warn("删除源模板关联关系参数无效");
            return;
        }

        int count = templateRelationMapper.deleteBySourceTemplate(sourceTemplateId, sourceTemplateType);
        log.info("删除源模板关联关系成功, 源模板ID: {}, 源模板类型: {}, 删除数量: {}", 
                sourceTemplateId, sourceTemplateType, count);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByTargetTemplate(String targetTemplateId, String targetTemplateType) {
        if (StrXhUtil.isEmpty(targetTemplateId) || StrXhUtil.isEmpty(targetTemplateType)) {
            log.warn("删除目标模板关联关系参数无效");
            return;
        }

        int count = templateRelationMapper.deleteByTargetTemplate(targetTemplateId, targetTemplateType);
        log.info("删除目标模板关联关系成功, 目标模板ID: {}, 目标模板类型: {}, 删除数量: {}", 
                targetTemplateId, targetTemplateType, count);
    }

    @Override
    public boolean isRelationExists(String sourceTemplateId, String sourceTemplateType, 
                                   String targetTemplateId, String targetTemplateType) {
        if (StrXhUtil.isEmpty(sourceTemplateId) || StrXhUtil.isEmpty(sourceTemplateType) ||
            StrXhUtil.isEmpty(targetTemplateId) || StrXhUtil.isEmpty(targetTemplateType)) {
            return false;
        }

        int count = templateRelationMapper.checkRelationExists(sourceTemplateId, sourceTemplateType, 
                                                              targetTemplateId, targetTemplateType);
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTemplateRelation(String sourceTemplateId, String sourceTemplateType, 
                                   String targetTemplateId, String targetTemplateType) {
        if (StrXhUtil.isEmpty(sourceTemplateId) || StrXhUtil.isEmpty(sourceTemplateType) ||
            StrXhUtil.isEmpty(targetTemplateId) || StrXhUtil.isEmpty(targetTemplateType)) {
            log.warn("添加模板关联关系参数无效");
            throw new RuntimeException("参数无效");
        }

        // 检查关联关系是否已存在
        if (isRelationExists(sourceTemplateId, sourceTemplateType, targetTemplateId, targetTemplateType)) {
            log.warn("模板关联关系已存在");
            return;
        }

        TemplateRelationEntity relation = new TemplateRelationEntity();
        relation.setId(RandomUtil.snowId());
        relation.setSourceTemplateId(sourceTemplateId);
        relation.setSourceTemplateType(sourceTemplateType);
        relation.setTargetTemplateId(targetTemplateId);
        relation.setTargetTemplateType(targetTemplateType);

        this.save(relation);
        log.info("添加模板关联关系成功, 源模板ID: {}, 目标模板ID: {}", sourceTemplateId, targetTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeTemplateRelation(String sourceTemplateId, String sourceTemplateType, 
                                      String targetTemplateId, String targetTemplateType) {
        if (StrXhUtil.isEmpty(sourceTemplateId) || StrXhUtil.isEmpty(sourceTemplateType) ||
            StrXhUtil.isEmpty(targetTemplateId) || StrXhUtil.isEmpty(targetTemplateType)) {
            log.warn("删除模板关联关系参数无效");
            throw new RuntimeException("参数无效");
        }

        int count = templateRelationMapper.deleteRelation(sourceTemplateId, sourceTemplateType, 
                                                         targetTemplateId, targetTemplateType);
        log.info("删除模板关联关系成功, 源模板ID: {}, 目标模板ID: {}, 删除数量: {}", 
                sourceTemplateId, targetTemplateId, count);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteBySourceTemplateIds(List<String> sourceTemplateIds, String sourceTemplateType) {
        if (sourceTemplateIds == null || sourceTemplateIds.isEmpty() || StrXhUtil.isEmpty(sourceTemplateType)) {
            log.warn("批量删除源模板关联关系参数无效");
            return;
        }

        int count = templateRelationMapper.batchDeleteBySourceTemplateIds(sourceTemplateIds, sourceTemplateType);
        log.info("批量删除源模板关联关系成功, 源模板类型: {}, 模板数量: {}, 删除数量: {}", 
                sourceTemplateType, sourceTemplateIds.size(), count);
    }

    @Override
    public List<TemplateRelationEntity> getBySourceTemplateType(String sourceTemplateType) {
        if (StrXhUtil.isEmpty(sourceTemplateType)) {
            return new ArrayList<>();
        }
        return templateRelationMapper.selectBySourceTemplateType(sourceTemplateType);
    }

    @Override
    public List<TemplateRelationEntity> getByTargetTemplateType(String targetTemplateType) {
        if (StrXhUtil.isEmpty(targetTemplateType)) {
            return new ArrayList<>();
        }
        return templateRelationMapper.selectByTargetTemplateType(targetTemplateType);
    }
}
