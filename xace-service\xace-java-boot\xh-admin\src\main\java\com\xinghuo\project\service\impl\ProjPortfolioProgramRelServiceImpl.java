package com.xinghuo.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.dao.ProjPortfolioProgramRelMapper;
import com.xinghuo.project.entity.ProjPortfolioProgramRelEntity;
import com.xinghuo.project.service.ProjPortfolioProgramRelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组合-项目群关联表服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class ProjPortfolioProgramRelServiceImpl extends BaseServiceImpl<ProjPortfolioProgramRelMapper, ProjPortfolioProgramRelEntity> implements ProjPortfolioProgramRelService {

    @Resource
    private ProjPortfolioProgramRelMapper projPortfolioProgramRelMapper;

    @Override
    public List<String> getProgramIdsByPortfolioId(String portfolioId) {
        if (StrXhUtil.isBlank(portfolioId)) {
            return new ArrayList<>();
        }
        List<ProjPortfolioProgramRelEntity> relations = projPortfolioProgramRelMapper.selectByPortfolioId(portfolioId);
        return relations.stream().map(ProjPortfolioProgramRelEntity::getProgramId).collect(Collectors.toList());
    }

    @Override
    public List<String> getPortfolioIdsByProgramId(String programId) {
        if (StrXhUtil.isBlank(programId)) {
            return new ArrayList<>();
        }
        List<ProjPortfolioProgramRelEntity> relations = projPortfolioProgramRelMapper.selectByProgramId(programId);
        return relations.stream().map(ProjPortfolioProgramRelEntity::getPortfolioId).collect(Collectors.toList());
    }

    @Override
    public boolean isRelationExists(String portfolioId, String programId) {
        if (StrXhUtil.isBlank(portfolioId) || StrXhUtil.isBlank(programId)) {
            return false;
        }
        int count = projPortfolioProgramRelMapper.checkRelationExists(portfolioId, programId);
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createRelation(String portfolioId, String programId) {
        if (StrXhUtil.isBlank(portfolioId) || StrXhUtil.isBlank(programId)) {
            log.warn("创建组合-项目群关联关系参数为空");
            return false;
        }

        try {
            // 检查关联关系是否已存在
            if (isRelationExists(portfolioId, programId)) {
                log.warn("组合-项目群关联关系已存在: portfolioId={}, programId={}", portfolioId, programId);
                return true;
            }

            ProjPortfolioProgramRelEntity entity = new ProjPortfolioProgramRelEntity();
            entity.setId(RandomUtil.snowId());
            entity.setPortfolioId(portfolioId);
            entity.setProgramId(programId);

            boolean result = this.save(entity);
            if (result) {
                log.info("创建组合-项目群关联关系成功: portfolioId={}, programId={}", portfolioId, programId);
            }
            return result;

        } catch (Exception e) {
            log.error("创建组合-项目群关联关系异常", e);
            throw new RuntimeException("创建组合-项目群关联关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRelation(String portfolioId, String programId) {
        if (StrXhUtil.isBlank(portfolioId) || StrXhUtil.isBlank(programId)) {
            log.warn("删除组合-项目群关联关系参数为空");
            return false;
        }

        try {
            QueryWrapper<ProjPortfolioProgramRelEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(ProjPortfolioProgramRelEntity::getPortfolioId, portfolioId)
                    .eq(ProjPortfolioProgramRelEntity::getProgramId, programId);

            boolean result = this.remove(queryWrapper);
            if (result) {
                log.info("删除组合-项目群关联关系成功: portfolioId={}, programId={}", portfolioId, programId);
            }
            return result;

        } catch (Exception e) {
            log.error("删除组合-项目群关联关系异常", e);
            throw new RuntimeException("删除组合-项目群关联关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setPortfolioProgramRelations(String portfolioId, List<String> programIds) {
        if (StrXhUtil.isBlank(portfolioId)) {
            log.warn("设置组合项目群关联关系，组合ID为空");
            return false;
        }

        try {
            // 先删除原有关联关系
            deleteByPortfolioId(portfolioId);

            // 如果项目群ID列表为空，则只删除不新增
            if (programIds == null || programIds.isEmpty()) {
                log.info("设置组合项目群关联关系完成，清空所有关联: portfolioId={}", portfolioId);
                return true;
            }

            // 批量创建新的关联关系
            List<ProjPortfolioProgramRelEntity> relations = new ArrayList<>();
            for (String programId : programIds) {
                if (StrXhUtil.isNotBlank(programId)) {
                    ProjPortfolioProgramRelEntity entity = new ProjPortfolioProgramRelEntity();
                    entity.setId(RandomUtil.snowId());
                    entity.setPortfolioId(portfolioId);
                    entity.setProgramId(programId);
                    relations.add(entity);
                }
            }

            if (!relations.isEmpty()) {
                boolean result = this.saveBatch(relations);
                if (result) {
                    log.info("设置组合项目群关联关系成功: portfolioId={}, programCount={}", portfolioId, relations.size());
                }
                return result;
            }

            return true;

        } catch (Exception e) {
            log.error("设置组合项目群关联关系异常", e);
            throw new RuntimeException("设置组合项目群关联关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setProgramPortfolioRelations(String programId, List<String> portfolioIds) {
        if (StrXhUtil.isBlank(programId)) {
            log.warn("设置项目群组合关联关系，项目群ID为空");
            return false;
        }

        try {
            // 先删除原有关联关系
            deleteByProgramId(programId);

            // 如果组合ID列表为空，则只删除不新增
            if (portfolioIds == null || portfolioIds.isEmpty()) {
                log.info("设置项目群组合关联关系完成，清空所有关联: programId={}", programId);
                return true;
            }

            // 批量创建新的关联关系
            List<ProjPortfolioProgramRelEntity> relations = new ArrayList<>();
            for (String portfolioId : portfolioIds) {
                if (StrXhUtil.isNotBlank(portfolioId)) {
                    ProjPortfolioProgramRelEntity entity = new ProjPortfolioProgramRelEntity();
                    entity.setId(RandomUtil.snowId());
                    entity.setPortfolioId(portfolioId);
                    entity.setProgramId(programId);
                    relations.add(entity);
                }
            }

            if (!relations.isEmpty()) {
                boolean result = this.saveBatch(relations);
                if (result) {
                    log.info("设置项目群组合关联关系成功: programId={}, portfolioCount={}", programId, relations.size());
                }
                return result;
            }

            return true;

        } catch (Exception e) {
            log.error("设置项目群组合关联关系异常", e);
            throw new RuntimeException("设置项目群组合关联关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByPortfolioId(String portfolioId) {
        if (StrXhUtil.isBlank(portfolioId)) {
            return false;
        }

        try {
            int count = projPortfolioProgramRelMapper.deleteByPortfolioId(portfolioId);
            log.info("删除组合的所有项目群关联成功: portfolioId={}, count={}", portfolioId, count);
            return true;

        } catch (Exception e) {
            log.error("删除组合的所有项目群关联异常", e);
            throw new RuntimeException("删除组合的所有项目群关联失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByProgramId(String programId) {
        if (StrXhUtil.isBlank(programId)) {
            return false;
        }

        try {
            int count = projPortfolioProgramRelMapper.deleteByProgramId(programId);
            log.info("删除项目群的所有组合关联成功: programId={}, count={}", programId, count);
            return true;

        } catch (Exception e) {
            log.error("删除项目群的所有组合关联异常", e);
            throw new RuntimeException("删除项目群的所有组合关联失败");
        }
    }
}
