package com.xinghuo.project.biz.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.biz.entity.PaymentContractEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 付款合同Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface PaymentContractMapper extends XHBaseMapper<PaymentContractEntity> {

    /**
     * 根据收款合同ID查询付款合同列表
     *
     * @param contractId 收款合同ID
     * @return 付款合同列表
     */
    List<PaymentContractEntity> selectByContractId(@Param("contractId") String contractId);

    /**
     * 根据供应商ID查询付款合同列表
     *
     * @param supplierId 供应商ID
     * @return 付款合同列表
     */
    List<PaymentContractEntity> selectBySupplierId(@Param("supplierId") String supplierId);

    /**
     * 更新付款合同状态
     *
     * @param id 付款合同ID
     * @param status 状态
     * @return 更新数量
     */
    int updateStatus(@Param("id") String id, @Param("status") String status);

    /**
     * 签订付款合同
     *
     * @param id 付款合同ID
     * @param cNo 合同编号
     * @param signDate 签订日期
     * @return 更新数量
     */
    int signContract(@Param("id") String id, @Param("cNo") String cNo, @Param("signDate") Date signDate);

    /**
     * 检查付款合同编号是否存在
     *
     * @param cNo 合同编号
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkCNoExists(@Param("cNo") String cNo, @Param("excludeId") String excludeId);

    /**
     * 获取付款合同选择列表
     *
     * @param keyword 关键字
     * @return 付款合同列表
     */
    List<PaymentContractEntity> selectForSelect(@Param("keyword") String keyword);
}
