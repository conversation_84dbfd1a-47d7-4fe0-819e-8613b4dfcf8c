<template>
  <div class="report-placeholder">
    <PageWrapper title="付款统计报表" contentBackground>
      <a-result
        status="info"
        title="功能开发中"
        sub-title="付款统计报表正在开发中，敬请期待..."
      >
        <template #extra>
          <a-button type="primary" @click="goBack">返回报表中心</a-button>
        </template>
      </a-result>
    </PageWrapper>
  </div>
</template>

<script lang="ts" setup>
  import { useRouter } from 'vue-router';
  import { PageWrapper } from '/@/components/Page';

  defineOptions({ name: 'ProjectReportPayable' });

  const router = useRouter();

  function goBack() {
    router.push('/project/report');
  }
</script>

<style lang="less" scoped>
.report-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
