package com.xinghuo.project.core.model.tag;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 标签更新表单
 */
@Data
@Schema(description = "标签更新表单")
public class TagUpForm {

    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不能为空")
    @Size(max = 50, message = "标签名称长度不能超过50个字符")
    @Schema(description = "标签名称")
    private String tagName;

    /**
     * 标签颜色 (例如: "#FF0000" 代表红色)
     */
    @Pattern(regexp = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", message = "颜色格式不正确，应为十六进制颜色值，如 #FF0000")
    @Schema(description = "标签颜色 (例如: \"#FF0000\" 代表红色)")
    private String tagColor;

    /**
     * 标签描述
     */
    @Size(max = 200, message = "标签描述长度不能超过200个字符")
    @Schema(description = "标签描述")
    private String description;

    /**
     * 标签范围 ('global': 所有用户可见, 'user_specific': 特定用户或组可见)
     */
    @NotBlank(message = "标签范围不能为空")
    @Pattern(regexp = "^(global|user_specific)$", message = "标签范围只能是 'global' 或 'user_specific'")
    @Schema(description = "标签范围 ('global': 所有用户可见, 'user_specific': 特定用户或组可见)")
    private String scope;
}
