package com.xinghuo.project.biz.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.biz.entity.CustomerContactEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户联系人Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface CustomerContactMapper extends XHBaseMapper<CustomerContactEntity> {

    /**
     * 根据客户ID查询联系人列表
     *
     * @param customerId 客户ID
     * @return 联系人列表
     */
    List<CustomerContactEntity> selectByCustomerId(@Param("customerId") String customerId);

    /**
     * 根据客户ID和关键字查询联系人列表
     *
     * @param customerId 客户ID
     * @param keyword 关键字
     * @return 联系人列表
     */
    List<CustomerContactEntity> selectByCustomerIdAndKeyword(@Param("customerId") String customerId, @Param("keyword") String keyword);

    /**
     * 更新联系人状态
     *
     * @param id 联系人ID
     * @param status 状态
     * @return 更新数量
     */
    int updateStatus(@Param("id") String id, @Param("status") Integer status);

    /**
     * 检查联系人是否存在
     *
     * @param customerId 客户ID
     * @param name 联系人姓名
     * @param phone 联系电话
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkContactExists(@Param("customerId") String customerId, @Param("name") String name, @Param("phone") String phone, @Param("excludeId") String excludeId);
}
