package com.xinghuo.project.biz.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.biz.entity.OpportunityEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 商机Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface OpportunityMapper extends XHBaseMapper<OpportunityEntity> {

    /**
     * 根据客户ID查询商机列表
     *
     * @param custId 客户ID
     * @return 商机列表
     */
    List<OpportunityEntity> selectByCustId(@Param("custId") String custId);

    /**
     * 根据项目负责人ID查询商机列表
     *
     * @param projectLeader 项目负责人ID
     * @return 商机列表
     */
    List<OpportunityEntity> selectByProjectLeader(@Param("projectLeader") String projectLeader);

    /**
     * 根据状态查询商机列表
     *
     * @param status 状态
     * @return 商机列表
     */
    List<OpportunityEntity> selectByStatus(@Param("status") String status);

    /**
     * 获取销售漏斗数据
     *
     * @param params 查询参数
     * @return 销售漏斗数据
     */
    List<Map<String, Object>> getSalesFunnelData(@Param("params") Map<String, Object> params);

    /**
     * 获取商机预测数据
     *
     * @param params 查询参数
     * @return 商机预测数据
     */
    List<Map<String, Object>> getBusinessForecastData(@Param("params") Map<String, Object> params);

    /**
     * 获取赢单/输单分析数据
     *
     * @param params 查询参数
     * @return 赢单/输单分析数据
     */
    List<Map<String, Object>> getWinLoseAnalysisData(@Param("params") Map<String, Object> params);
}
