package com.xinghuo.project.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.entity.ProjBaseEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目基础信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface ProjBaseMapper extends XHBaseMapper<ProjBaseEntity> {

    /**
     * 根据项目经理ID查询项目列表
     *
     * @param managerId 项目经理ID
     * @return 项目列表
     */
    List<ProjBaseEntity> selectByManagerId(@Param("managerId") String managerId);

    /**
     * 根据项目群ID查询项目列表
     *
     * @param programId 项目群ID
     * @return 项目列表
     */
    List<ProjBaseEntity> selectByProgramId(@Param("programId") String programId);

    /**
     * 根据项目类型ID查询项目列表
     *
     * @param typeId 项目类型ID
     * @return 项目列表
     */
    List<ProjBaseEntity> selectByTypeId(@Param("typeId") String typeId);

    /**
     * 根据项目状态ID查询项目列表
     *
     * @param statusId 项目状态ID
     * @return 项目列表
     */
    List<ProjBaseEntity> selectByStatusId(@Param("statusId") String statusId);

    /**
     * 根据健康状态查询项目列表
     *
     * @param healthStatus 健康状态
     * @return 项目列表
     */
    List<ProjBaseEntity> selectByHealthStatus(@Param("healthStatus") String healthStatus);

    /**
     * 检查项目编码是否存在
     *
     * @param code 项目编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkCodeExists(@Param("code") String code, @Param("excludeId") String excludeId);

    /**
     * 统计项目群下的项目数量
     *
     * @param programId 项目群ID
     * @return 项目数量
     */
    int countByProgramId(@Param("programId") String programId);
}
