package com.xinghuo.project.model.portfolio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 项目组合视图对象
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@Schema(description = "项目组合视图对象")
public class ProjPortfolioVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 组合编码
     */
    @Schema(description = "组合编码")
    private String code;

    /**
     * 组合名称
     */
    @Schema(description = "组合名称")
    private String name;

    /**
     * 组合类型ID
     */
    @Schema(description = "组合类型ID")
    private String typeId;

    /**
     * 组合类型名称
     */
    @Schema(description = "组合类型名称")
    private String typeName;

    /**
     * 组合负责人ID
     */
    @Schema(description = "组合负责人ID")
    private String ownerId;

    /**
     * 组合负责人姓名
     */
    @Schema(description = "组合负责人姓名")
    private String ownerName;

    /**
     * 组合描述
     */
    @Schema(description = "组合描述")
    private String description;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称")
    private String statusName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createdAt;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    private String createdBy;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名")
    private String createdByName;

    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间")
    private Date lastUpdatedAt;

    /**
     * 最后更新人ID
     */
    @Schema(description = "最后更新人ID")
    private String lastUpdatedBy;

    /**
     * 最后更新人姓名
     */
    @Schema(description = "最后更新人姓名")
    private String lastUpdatedByName;
}
