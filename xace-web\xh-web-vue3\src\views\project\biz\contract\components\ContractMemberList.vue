<template>
  <div class="contract-member-container">
    <a-card :bordered="false" class="member-card">
      <template #title>
        <div class="card-title">
          <TeamOutlined class="title-icon" />
          项目成员管理
        </div>
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd" v-if="hasManagePermission">
            <template #icon><PlusOutlined /></template>
            添加成员
          </a-button>
          <a-button @click="loadMemberList">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="memberList"
        :loading="loading"
        :pagination="false"
        row-key="id"
        size="middle"
        class="member-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'userName'">
            <div class="user-info">
              <div class="user-name">{{ record.userName }}</div>
              <div class="user-dept">{{ record.userDeptName }}</div>
            </div>
          </template>
          
          <template v-else-if="column.key === 'roleType'">
            <a-tag :color="getRoleColor(record.roleType)">
              {{ getRoleText(record.roleType) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'isPrimary'">
            <a-tag v-if="record.isPrimary === 1" color="gold">
              <template #icon><CrownOutlined /></template>
              主要负责人
            </a-tag>
            <span v-else>-</span>
          </template>
          
          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'success' : 'default'">
              {{ record.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)" v-if="hasManagePermission">
                编辑
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                @click="handleSetPrimary(record)" 
                v-if="hasManagePermission && record.isPrimary !== 1"
              >
                设为主负责人
              </a-button>
              <a-popconfirm
                title="确定要删除这个成员吗？"
                @confirm="handleDelete(record)"
                v-if="hasManagePermission"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑成员弹窗 -->
    <ContractMemberModal
      @register="registerModal"
      @reload="loadMemberList"
      :contractId="contractId"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted, watch } from 'vue';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    getContractMemberList,
    deleteContractMember,
    setPrimaryMember,
    ContractMemberModel,
    RoleTypeTextMap,
  } from '/@/api/project/contractMember';
  import ContractMemberModal from './ContractMemberModal.vue';
  import {
    TeamOutlined,
    PlusOutlined,
    ReloadOutlined,
    CrownOutlined,
  } from '@ant-design/icons-vue';

  const props = defineProps<{
    contractId: string;
    hasManagePermission?: boolean;
  }>();

  const { createMessage } = useMessage();
  const [registerModal, { openModal }] = useModal();

  const loading = ref(false);
  const memberList = ref<ContractMemberModel[]>([]);

  // 表格列定义
  const columns = [
    {
      title: '用户信息',
      key: 'userName',
      width: 200,
    },
    {
      title: '角色类型',
      key: 'roleType',
      width: 120,
    },
    {
      title: '主要负责人',
      key: 'isPrimary',
      width: 120,
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
    },
  ];

  // 是否有管理权限
  const hasManagePermission = computed(() => {
    return props.hasManagePermission ?? true;
  });

  // 获取角色颜色
  function getRoleColor(roleType: string) {
    const colorMap = {
      contract_admin: 'red',
      pmo: 'orange',
      project_manager: 'blue',
      team_member: 'green',
    };
    return colorMap[roleType] || 'default';
  }

  // 获取角色文本
  function getRoleText(roleType: string) {
    return RoleTypeTextMap[roleType] || roleType;
  }

  // 加载成员列表
  async function loadMemberList() {
    if (!props.contractId) return;
    
    loading.value = true;
    try {
      memberList.value = await getContractMemberList(props.contractId);
    } catch (error) {
      console.error('加载成员列表失败:', error);
      createMessage.error('加载成员列表失败');
    } finally {
      loading.value = false;
    }
  }

  // 添加成员
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      contractId: props.contractId,
    });
  }

  // 编辑成员
  function handleEdit(record: ContractMemberModel) {
    openModal(true, {
      isUpdate: true,
      record,
      contractId: props.contractId,
    });
  }

  // 设置主要负责人
  async function handleSetPrimary(record: ContractMemberModel) {
    try {
      await setPrimaryMember(props.contractId, record.userId);
      createMessage.success('设置主要负责人成功');
      await loadMemberList();
    } catch (error) {
      console.error('设置主要负责人失败:', error);
      createMessage.error('设置主要负责人失败');
    }
  }

  // 删除成员
  async function handleDelete(record: ContractMemberModel) {
    try {
      await deleteContractMember(record.id);
      createMessage.success('删除成员成功');
      await loadMemberList();
    } catch (error) {
      console.error('删除成员失败:', error);
      createMessage.error('删除成员失败');
    }
  }

  // 监听合同ID变化
  watch(
    () => props.contractId,
    (newId) => {
      if (newId) {
        loadMemberList();
      }
    },
    { immediate: true }
  );

  onMounted(() => {
    if (props.contractId) {
      loadMemberList();
    }
  });
</script>

<style lang="less" scoped>
  .contract-member-container {
    .member-card {
      .card-title {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .title-icon {
          color: #1890ff;
        }
      }
    }

    .member-table {
      .user-info {
        .user-name {
          font-weight: 500;
          color: #262626;
        }
        
        .user-dept {
          font-size: 12px;
          color: #8c8c8c;
          margin-top: 2px;
        }
      }
    }
  }
</style>
