package com.xinghuo.project.biz.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.biz.entity.SupplierEntity;
import com.xinghuo.project.biz.model.SupplierPagination;
import com.xinghuo.project.biz.service.SupplierService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "供应商管理", description = "供应商管理相关接口")
@RestController
@RequestMapping("/api/project/biz/supplier")
public class SupplierController {

    @Resource
    private SupplierService supplierService;

    /**
     * 获取供应商列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取供应商列表")
    public ActionResult<PageListVO<SupplierEntity>> list(@RequestBody SupplierPagination pagination) {
        List<SupplierEntity> list = supplierService.getList(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 获取供应商详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取供应商详情")
    public ActionResult<SupplierEntity> getInfo(
            @Parameter(description = "供应商ID") @PathVariable String id) {
        SupplierEntity entity = supplierService.getInfo(id);
        return ActionResult.success(entity);
    }

    /**
     * 创建供应商
     */
    @PostMapping("/create")
    @Operation(summary = "创建供应商")
    public ActionResult<String> create(@RequestBody @Valid SupplierEntity entity) {
        String id = supplierService.create(entity);
        return ActionResult.success("创建成功", id);
    }

    /**
     * 更新供应商
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新供应商")
    public ActionResult<String> update(
            @Parameter(description = "供应商ID") @PathVariable String id,
            @RequestBody @Valid SupplierEntity entity) {
        supplierService.update(id, entity);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除供应商
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除供应商")
    public ActionResult<String> delete(
            @Parameter(description = "供应商ID") @PathVariable String id) {
        supplierService.delete(id);
        return ActionResult.success("删除成功");
    }

    /**
     * 检查供应商名称是否存在
     */
    @GetMapping("/checkNameExists")
    @Operation(summary = "检查供应商名称是否存在")
    public ActionResult<Boolean> checkNameExists(
            @RequestParam String name,
            @RequestParam(required = false) String excludeId) {
        boolean exists = supplierService.isExistByName(name, excludeId);
        return ActionResult.success(exists);
    }

    /**
     * 获取供应商选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取供应商选择列表")
    public ActionResult<List<SupplierEntity>> getSelectList() {
        List<SupplierEntity> list = supplierService.getSelectList();
        return ActionResult.success(list);
    }
}
