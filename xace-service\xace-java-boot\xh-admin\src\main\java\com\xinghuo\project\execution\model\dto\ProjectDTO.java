package com.xinghuo.project.execution.model.dto;

import com.xinghuo.project.execution.entity.ProjectEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 项目DTO
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectDTO extends ProjectEntity {

    /**
     * 项目状态名称
     */
    private String statusName;

    /**
     * 工作流状态名称
     */
    private String wfStatusName;

    /**
     * 备选状态名称
     */
    private String alternativeStatusName;

    /**
     * 项目经理姓名
     */
    private String managerUserName;

    /**
     * 项目经理显示名称
     */
    private String managerDisplayName;

    /**
     * 项目发起人姓名
     */
    private String sponsorUserName;

    /**
     * 项目发起人显示名称
     */
    private String sponsorDisplayName;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门路径
     */
    private String deptPath;

    /**
     * 项目组合名称
     */
    private String portfolioName;

    /**
     * 项目模板名称
     */
    private String schemaName;

    /**
     * 日历模板名称
     */
    private String calendarTemplateName;

    /**
     * 父项目名称
     */
    private String parentProjectName;

    /**
     * 显示名称（项目名称 + 编码）
     */
    private String displayName;

    /**
     * 项目图标
     */
    private String icon;

    /**
     * 项目团队列表
     */
    private List<ProjectTeamDTO> projectTeamList;

    /**
     * 项目经理列表
     */
    private List<ProjectTeamDTO> managers;

    /**
     * 项目状态可更新的数据类型映射
     */
    private Map<String, List<String>> projectStatusCanUpdateDataMap;

    /**
     * 项目统计信息
     */
    private ProjectStatisticsDTO statistics;

    /**
     * 项目自定义字段映射
     */
    private Map<String, Object> customFields;

    /**
     * 项目标签列表
     */
    private List<String> tagList;

    /**
     * 是否收藏
     */
    private Boolean isFavorite;

    /**
     * 是否关注
     */
    private Boolean isFollowed;

    /**
     * 最近访问时间
     */
    private java.util.Date lastVisitTime;

    /**
     * 项目权限列表
     */
    private List<String> permissions;

    /**
     * 项目阶段信息
     */
    private List<ProjectPhaseDTO> phases;

    /**
     * 项目里程碑信息
     */
    private List<ProjectMilestoneDTO> milestones;

    /**
     * 项目风险信息
     */
    private List<ProjectRiskDTO> risks;

    /**
     * 项目问题信息
     */
    private List<ProjectIssueDTO> issues;

    /**
     * 项目变更信息
     */
    private List<ProjectChangeDTO> changes;

    /**
     * 项目文档信息
     */
    private List<ProjectDocumentDTO> documents;

    /**
     * 项目交付物信息
     */
    private List<ProjectDeliverableDTO> deliverables;

    /**
     * 项目质量信息
     */
    private ProjectQualityDTO quality;

    /**
     * 项目成本信息
     */
    private ProjectCostDTO cost;

    /**
     * 项目收入信息
     */
    private ProjectIncomeDTO income;

    /**
     * 项目资源信息
     */
    private List<ProjectResourceDTO> resources;

    /**
     * 项目沟通信息
     */
    private List<ProjectCommunicationDTO> communications;

    /**
     * 项目会议信息
     */
    private List<ProjectMeetingDTO> meetings;

    /**
     * 项目报告信息
     */
    private List<ProjectReportDTO> reports;

    /**
     * 项目审批信息
     */
    private List<ProjectApprovalDTO> approvals;

    /**
     * 项目集成信息
     */
    private Map<String, Object> integrationInfo;

    /**
     * 项目扩展信息
     */
    private Map<String, Object> extensionInfo;
}
