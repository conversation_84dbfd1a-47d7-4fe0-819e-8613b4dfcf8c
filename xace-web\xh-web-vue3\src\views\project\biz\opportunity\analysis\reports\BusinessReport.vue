<template>
  <div class="xh-content-wrapper">
    <div class="xh-content-wrapper-center">
      <div class="xh-content-wrapper-content">
        <PageWrapper title="商机报表" contentBackground contentClass="p-4">
          <a-card class="mb-4">
            <a-form layout="inline" :model="queryParams">
              <a-form-item label="年份">
                <a-select v-model:value="queryParams.year" style="width: 120px">
                  <a-select-option v-for="year in yearOptions" :key="year" :value="year">
                    {{ year }}年
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="月份" v-if="showMonthSelect">
                <a-select v-model:value="queryParams.month" style="width: 120px">
                  <a-select-option v-for="month in 12" :key="month" :value="month">
                    {{ month }}月
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="部门">
                <a-select v-model:value="queryParams.deptId" style="width: 150px">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="dept1">开发一部</a-select-option>
                  <a-select-option value="dept2">开发二部</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="负责人">
                <a-select v-model:value="queryParams.leaderId" style="width: 150px">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="user1">张三</a-select-option>
                  <a-select-option value="user2">李四</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item>
                <a-button type="primary" @click="loadReportData">查询</a-button>
                <a-button style="margin-left: 8px" @click="resetQuery">重置</a-button>
              </a-form-item>
            </a-form>
          </a-card>
          
          <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
            <a-tab-pane key="funnel" tab="销售漏斗">
              <a-spin :spinning="loading.funnel">
                <div class="chart-container">
                  <div ref="funnelChartRef" style="width: 100%; height: 400px;"></div>
                </div>
              </a-spin>
            </a-tab-pane>
            
            <a-tab-pane key="forecast" tab="商机预测">
              <a-spin :spinning="loading.forecast">
                <div class="chart-container">
                  <div ref="forecastChartRef" style="width: 100%; height: 400px;"></div>
                </div>
              </a-spin>
            </a-tab-pane>
            
            <a-tab-pane key="winlose" tab="赢单/输单分析">
              <a-spin :spinning="loading.winlose">
                <div class="chart-container">
                  <div ref="winloseChartRef" style="width: 100%; height: 400px;"></div>
                </div>
              </a-spin>
            </a-tab-pane>
          </a-tabs>
        </PageWrapper>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, watch, computed } from 'vue';
  import { PageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getSalesFunnelData, getBusinessForecastData, getWinLoseAnalysisData } from '/@/api/project/business';
  import * as echarts from 'echarts/core';
  import { BarChart, PieChart, FunnelChart, LineChart } from 'echarts/charts';
  import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components';
  import { CanvasRenderer } from 'echarts/renderers';

  // 注册 ECharts 组件
  echarts.use([
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
    BarChart,
    PieChart,
    FunnelChart,
    LineChart,
    CanvasRenderer,
  ]);

  const { createMessage } = useMessage();
  const activeTab = ref('funnel');
  const funnelChartRef = ref<HTMLElement | null>(null);
  const forecastChartRef = ref<HTMLElement | null>(null);
  const winloseChartRef = ref<HTMLElement | null>(null);
  
  // 图表实例
  let funnelChart: echarts.ECharts | null = null;
  let forecastChart: echarts.ECharts | null = null;
  let winloseChart: echarts.ECharts | null = null;
  
  // 加载状态
  const loading = reactive({
    funnel: false,
    forecast: false,
    winlose: false,
  });
  
  // 查询参数
  const queryParams = reactive({
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    deptId: '',
    leaderId: '',
  });
  
  // 年份选项
  const currentYear = new Date().getFullYear();
  const yearOptions = [currentYear - 1, currentYear, currentYear + 1];
  
  // 是否显示月份选择
  const showMonthSelect = computed(() => activeTab.value === 'funnel');
  
  // 监听标签页变化
  function handleTabChange(key: string) {
    loadReportData();
  }
  
  // 重置查询条件
  function resetQuery() {
    queryParams.year = new Date().getFullYear();
    queryParams.month = new Date().getMonth() + 1;
    queryParams.deptId = '';
    queryParams.leaderId = '';
    loadReportData();
  }
  
  // 加载报表数据
  async function loadReportData() {
    switch (activeTab.value) {
      case 'funnel':
        await loadFunnelData();
        break;
      case 'forecast':
        await loadForecastData();
        break;
      case 'winlose':
        await loadWinLoseData();
        break;
    }
  }
  
  // 加载销售漏斗数据
  async function loadFunnelData() {
    try {
      loading.funnel = true;
      
      const params = {
        year: queryParams.year,
        month: queryParams.month,
        deptId: queryParams.deptId || undefined,
        leaderId: queryParams.leaderId || undefined,
      };
      
      const data = await getSalesFunnelData(params);
      
      // 初始化漏斗图
      if (!funnelChart && funnelChartRef.value) {
        funnelChart = echarts.init(funnelChartRef.value);
      }
      
      // 示例数据，实际应根据API返回数据进行处理
      const funnelData = [
        { value: 100, name: '跟踪中' },
        { value: 80, name: '方案报价中' },
        { value: 60, name: '商务谈判中' },
        { value: 40, name: '已签' },
        { value: 20, name: '已废弃' },
      ];
      
      // 设置漏斗图配置
      funnelChart?.setOption({
        title: {
          text: '销售漏斗图',
          left: 'center',
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c}个 ({d}%)',
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: funnelData.map(item => item.name),
        },
        series: [
          {
            name: '商机状态',
            type: 'funnel',
            left: '10%',
            top: 60,
            bottom: 60,
            width: '80%',
            min: 0,
            max: 100,
            minSize: '0%',
            maxSize: '100%',
            sort: 'descending',
            gap: 2,
            label: {
              show: true,
              position: 'inside',
            },
            emphasis: {
              label: {
                fontSize: 20,
              },
            },
            data: funnelData,
          },
        ],
      });
    } catch (error) {
      console.error('加载销售漏斗数据失败:', error);
      createMessage.error('加载销售漏斗数据失败');
    } finally {
      loading.funnel = false;
    }
  }
  
  // 加载商机预测数据
  async function loadForecastData() {
    try {
      loading.forecast = true;
      
      const params = {
        year: queryParams.year,
        deptId: queryParams.deptId || undefined,
        leaderId: queryParams.leaderId || undefined,
      };
      
      const data = await getBusinessForecastData(params);
      
      // 初始化预测图
      if (!forecastChart && forecastChartRef.value) {
        forecastChart = echarts.init(forecastChartRef.value);
      }
      
      // 示例数据，实际应根据API返回数据进行处理
      const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
      const forecastData = [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65];
      const actualData = [8, 12, 18, 22, 28, 32, 0, 0, 0, 0, 0, 0]; // 假设前6个月有实际数据
      
      // 设置预测图配置
      forecastChart?.setOption({
        title: {
          text: `${queryParams.year}年商机预测`,
          left: 'center',
        },
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          data: ['预测金额', '实际金额'],
          bottom: 0,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: months,
        },
        yAxis: {
          type: 'value',
          name: '金额（万元）',
        },
        series: [
          {
            name: '预测金额',
            type: 'line',
            data: forecastData,
            smooth: true,
            lineStyle: {
              width: 3,
              shadowColor: 'rgba(0,0,0,0.3)',
              shadowBlur: 10,
              shadowOffsetY: 8,
            },
          },
          {
            name: '实际金额',
            type: 'bar',
            data: actualData,
          },
        ],
      });
    } catch (error) {
      console.error('加载商机预测数据失败:', error);
      createMessage.error('加载商机预测数据失败');
    } finally {
      loading.forecast = false;
    }
  }
  
  // 加载赢单/输单分析数据
  async function loadWinLoseData() {
    try {
      loading.winlose = true;
      
      const params = {
        year: queryParams.year,
        deptId: queryParams.deptId || undefined,
        leaderId: queryParams.leaderId || undefined,
      };
      
      const data = await getWinLoseAnalysisData(params);
      
      // 初始化赢单/输单分析图
      if (!winloseChart && winloseChartRef.value) {
        winloseChart = echarts.init(winloseChartRef.value);
      }
      
      // 示例数据，实际应根据API返回数据进行处理
      const winloseData = [
        { value: 60, name: '已签' },
        { value: 30, name: '已废弃' },
        { value: 10, name: '明年跟踪' },
      ];
      
      // 设置赢单/输单分析图配置
      winloseChart?.setOption({
        title: {
          text: '赢单/输单分析',
          left: 'center',
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: winloseData.map(item => item.name),
        },
        series: [
          {
            name: '商机状态',
            type: 'pie',
            radius: '55%',
            center: ['50%', '50%'],
            data: winloseData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      });
    } catch (error) {
      console.error('加载赢单/输单分析数据失败:', error);
      createMessage.error('加载赢单/输单分析数据失败');
    } finally {
      loading.winlose = false;
    }
  }
  
  // 监听窗口大小变化，重绘图表
  function handleResize() {
    funnelChart?.resize();
    forecastChart?.resize();
    winloseChart?.resize();
  }
  
  onMounted(() => {
    loadReportData();
    window.addEventListener('resize', handleResize);
  });
  
  // 组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    funnelChart?.dispose();
    forecastChart?.dispose();
    winloseChart?.dispose();
  });
</script>

<style lang="less" scoped>
.chart-container {
  background-color: #fff;
  padding: 16px;
  border-radius: 4px;
  margin-top: 16px;
}
</style>
