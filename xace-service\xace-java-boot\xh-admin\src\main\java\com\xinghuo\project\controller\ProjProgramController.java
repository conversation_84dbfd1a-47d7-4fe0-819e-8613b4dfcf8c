package com.xinghuo.project.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.entity.ProjProgramEntity;
import com.xinghuo.project.service.ProjProgramService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 项目群控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@RestController
@Tag(name = "项目群管理", description = "项目群管理相关接口")
@RequestMapping("/api/project/program")
public class ProjProgramController {

    @Resource
    private ProjProgramService projProgramService;

    /**
     * 获取项目群列表
     *
     * @param pagination 分页查询参数
     * @return 项目群列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取项目群列表")
    public ActionResult<PageListVO<ProjProgramEntity>> list(@RequestBody Object pagination) {
        List<ProjProgramEntity> list = projProgramService.getList(pagination);
        // 暂时直接返回Entity，后续可以创建VO类进行转换
        PaginationVO page = new PaginationVO();
        page.setCurrentPage(1);
        page.setPageSize(list.size());
        page.setTotal((long) list.size());
        return ActionResult.page(list, page);
    }

    /**
     * 根据ID获取项目群信息
     *
     * @param id 项目群ID
     * @return 项目群信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取项目群信息")
    public ActionResult<ProjProgramEntity> getInfo(@PathVariable String id) {
        if (StrXhUtil.isBlank(id)) {
            return ActionResult.fail("项目群ID不能为空");
        }
        ProjProgramEntity entity = projProgramService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("项目群不存在");
        }
        return ActionResult.success(entity);
    }

    /**
     * 创建项目群
     *
     * @param entity 项目群信息
     * @return 创建结果
     */
    @PostMapping
    @Operation(summary = "创建项目群")
    public ActionResult<String> create(@RequestBody @Valid ProjProgramEntity entity) {
        try {
            boolean result = projProgramService.saveInfo(entity);
            if (result) {
                return ActionResult.success("创建项目群成功", entity.getId());
            } else {
                return ActionResult.fail("创建项目群失败");
            }
        } catch (RuntimeException e) {
            log.error("创建项目群异常", e);
            return ActionResult.fail(e.getMessage());
        }
    }

    /**
     * 更新项目群
     *
     * @param id 项目群ID
     * @param entity 更新信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新项目群")
    public ActionResult<String> update(@PathVariable String id, @RequestBody @Valid ProjProgramEntity entity) {
        if (StrXhUtil.isBlank(id)) {
            return ActionResult.fail("项目群ID不能为空");
        }
        
        try {
            entity.setId(id);
            boolean result = projProgramService.updateInfo(entity);
            if (result) {
                return ActionResult.success("更新项目群成功");
            } else {
                return ActionResult.fail("更新项目群失败");
            }
        } catch (RuntimeException e) {
            log.error("更新项目群异常", e);
            return ActionResult.fail(e.getMessage());
        }
    }

    /**
     * 删除项目群
     *
     * @param id 项目群ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除项目群")
    public ActionResult<String> delete(@PathVariable String id) {
        if (StrXhUtil.isBlank(id)) {
            return ActionResult.fail("项目群ID不能为空");
        }
        
        try {
            boolean result = projProgramService.deleteById(id);
            if (result) {
                return ActionResult.success("删除项目群成功");
            } else {
                return ActionResult.fail("删除项目群失败");
            }
        } catch (RuntimeException e) {
            log.error("删除项目群异常", e);
            return ActionResult.fail(e.getMessage());
        }
    }

    /**
     * 根据项目群经理ID获取项目群列表
     *
     * @param managerId 项目群经理ID
     * @return 项目群列表
     */
    @GetMapping("/manager/{managerId}")
    @Operation(summary = "根据项目群经理ID获取项目群列表")
    public ActionResult<List<ProjProgramEntity>> getListByManagerId(@PathVariable String managerId) {
        if (StrXhUtil.isBlank(managerId)) {
            return ActionResult.fail("项目群经理ID不能为空");
        }
        List<ProjProgramEntity> list = projProgramService.getListByManagerId(managerId);
        return ActionResult.success(list);
    }

    /**
     * 根据项目群类型ID获取项目群列表
     *
     * @param typeId 项目群类型ID
     * @return 项目群列表
     */
    @GetMapping("/type/{typeId}")
    @Operation(summary = "根据项目群类型ID获取项目群列表")
    public ActionResult<List<ProjProgramEntity>> getListByTypeId(@PathVariable String typeId) {
        if (StrXhUtil.isBlank(typeId)) {
            return ActionResult.fail("项目群类型ID不能为空");
        }
        List<ProjProgramEntity> list = projProgramService.getListByTypeId(typeId);
        return ActionResult.success(list);
    }

    /**
     * 根据状态获取项目群列表
     *
     * @param status 状态
     * @return 项目群列表
     */
    @GetMapping("/status/{status}")
    @Operation(summary = "根据状态获取项目群列表")
    public ActionResult<List<ProjProgramEntity>> getListByStatus(@PathVariable String status) {
        if (StrXhUtil.isBlank(status)) {
            return ActionResult.fail("状态不能为空");
        }
        List<ProjProgramEntity> list = projProgramService.getListByStatus(status);
        return ActionResult.success(list);
    }

    /**
     * 检查项目群编码是否存在
     *
     * @param code 项目群编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    @GetMapping("/checkCode")
    @Operation(summary = "检查项目群编码是否存在")
    public ActionResult<Boolean> checkCodeExists(@RequestParam String code, @RequestParam(required = false) String excludeId) {
        if (StrXhUtil.isBlank(code)) {
            return ActionResult.fail("项目群编码不能为空");
        }
        boolean exists = projProgramService.isExistByCode(code, excludeId);
        return ActionResult.success(exists);
    }
}
