package com.xinghuo.project.model.portfolio;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目组合分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "项目组合分页查询参数")
public class ProjPortfolioPagination extends Pagination {

    /**
     * 组合编码
     */
    @Schema(description = "组合编码")
    private String code;

    /**
     * 组合名称
     */
    @Schema(description = "组合名称")
    private String name;

    /**
     * 组合类型ID
     */
    @Schema(description = "组合类型ID")
    private String typeId;

    /**
     * 组合负责人ID
     */
    @Schema(description = "组合负责人ID")
    private String ownerId;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 关键字搜索（名称或编码）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}
