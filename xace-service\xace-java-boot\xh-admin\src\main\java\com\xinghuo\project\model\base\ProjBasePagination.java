package com.xinghuo.project.model.base;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目基础信息分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "项目基础信息分页查询参数")
public class ProjBasePagination extends Pagination {

    /**
     * 项目编码
     */
    @Schema(description = "项目编码")
    private String code;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String name;

    /**
     * 项目类型ID
     */
    @Schema(description = "项目类型ID")
    private String typeId;

    /**
     * 项目经理ID
     */
    @Schema(description = "项目经理ID")
    private String managerId;

    /**
     * 所属项目群ID
     */
    @Schema(description = "所属项目群ID")
    private String programId;

    /**
     * 项目状态ID
     */
    @Schema(description = "项目状态ID")
    private String statusId;

    /**
     * 项目健康状态
     */
    @Schema(description = "项目健康状态")
    private String healthStatus;

    /**
     * 进度范围-最小值
     */
    @Schema(description = "进度范围-最小值")
    private Integer progressMin;

    /**
     * 进度范围-最大值
     */
    @Schema(description = "进度范围-最大值")
    private Integer progressMax;

    /**
     * 计划开始日期-开始
     */
    @Schema(description = "计划开始日期-开始")
    private Date planStartDateBegin;

    /**
     * 计划开始日期-结束
     */
    @Schema(description = "计划开始日期-结束")
    private Date planStartDateEnd;

    /**
     * 计划结束日期-开始
     */
    @Schema(description = "计划结束日期-开始")
    private Date planEndDateBegin;

    /**
     * 计划结束日期-结束
     */
    @Schema(description = "计划结束日期-结束")
    private Date planEndDateEnd;

    /**
     * 关键字搜索（名称或编码）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}
