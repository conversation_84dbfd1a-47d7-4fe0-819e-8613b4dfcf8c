package com.xinghuo.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目组合实体类
 * 对应数据库表：zz_proj_portfolio
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_portfolio")
public class ProjPortfolioEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 组合编码，可自动生成
     */
    @TableField("code")
    private String code;

    /**
     * 组合名称
     */
    @TableField("name")
    private String name;

    /**
     * 组合类型ID (关联字典表)
     */
    @TableField("type_id")
    private String typeId;

    /**
     * 组合负责人ID (关联用户表)
     */
    @TableField("owner_id")
    private String ownerId;

    /**
     * 组合描述
     */
    @TableField("description")
    private String description;

    /**
     * 状态 (如: active, archived)
     */
    @TableField("status")
    private String status;
}
