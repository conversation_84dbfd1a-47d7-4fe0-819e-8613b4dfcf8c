package com.xinghuo.project.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;

import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.entity.ProjPortfolioEntity;
import com.xinghuo.project.model.portfolio.ProjPortfolioPagination;
import com.xinghuo.project.service.ProjPortfolioService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 项目组合控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@RestController
@Tag(name = "项目组合管理", description = "项目组合管理相关接口")
@RequestMapping("/api/project/portfolio")
public class ProjPortfolioController {

    @Resource
    private ProjPortfolioService projPortfolioService;

    /**
     * 获取项目组合列表
     *
     * @param pagination 分页查询参数
     * @return 组合列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取项目组合列表")
    public ActionResult<PageListVO<ProjPortfolioEntity>> list(@RequestBody ProjPortfolioPagination pagination) {
        List<ProjPortfolioEntity> list = projPortfolioService.getList(pagination);
        // 暂时直接返回Entity，后续可以创建VO类进行转换
        PaginationVO page = new PaginationVO();
        page.setCurrentPage(1);
        page.setPageSize(list.size());
        page.setTotal((long) list.size());
        return ActionResult.page(list, page);
    }

    /**
     * 根据ID获取项目组合信息
     *
     * @param id 组合ID
     * @return 组合信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取项目组合信息")
    public ActionResult<ProjPortfolioEntity> getInfo(@PathVariable String id) {
        if (StrXhUtil.isBlank(id)) {
            return ActionResult.fail("组合ID不能为空");
        }
        ProjPortfolioEntity entity = projPortfolioService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("组合不存在");
        }
        return ActionResult.success(entity);
    }

    /**
     * 创建项目组合
     *
     * @param entity 组合信息
     * @return 创建结果
     */
    @PostMapping
    @Operation(summary = "创建项目组合")
    public ActionResult<String> create(@RequestBody @Valid ProjPortfolioEntity entity) {
        try {
            boolean result = projPortfolioService.saveInfo(entity);
            if (result) {
                return ActionResult.success("创建项目组合成功", entity.getId());
            } else {
                return ActionResult.fail("创建项目组合失败");
            }
        } catch (RuntimeException e) {
            log.error("创建项目组合异常", e);
            return ActionResult.fail(e.getMessage());
        }
    }

    /**
     * 更新项目组合
     *
     * @param id 组合ID
     * @param entity 更新信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新项目组合")
    public ActionResult<String> update(@PathVariable String id, @RequestBody @Valid ProjPortfolioEntity entity) {
        if (StrXhUtil.isBlank(id)) {
            return ActionResult.fail("组合ID不能为空");
        }
        
        try {
            entity.setId(id);
            boolean result = projPortfolioService.updateInfo(entity);
            if (result) {
                return ActionResult.success("更新项目组合成功");
            } else {
                return ActionResult.fail("更新项目组合失败");
            }
        } catch (RuntimeException e) {
            log.error("更新项目组合异常", e);
            return ActionResult.fail(e.getMessage());
        }
    }

    /**
     * 删除项目组合
     *
     * @param id 组合ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除项目组合")
    public ActionResult<String> delete(@PathVariable String id) {
        if (StrXhUtil.isBlank(id)) {
            return ActionResult.fail("组合ID不能为空");
        }
        
        try {
            boolean result = projPortfolioService.deleteById(id);
            if (result) {
                return ActionResult.success("删除项目组合成功");
            } else {
                return ActionResult.fail("删除项目组合失败");
            }
        } catch (RuntimeException e) {
            log.error("删除项目组合异常", e);
            return ActionResult.fail(e.getMessage());
        }
    }

    /**
     * 根据负责人ID获取组合列表
     *
     * @param ownerId 负责人ID
     * @return 组合列表
     */
    @GetMapping("/owner/{ownerId}")
    @Operation(summary = "根据负责人ID获取组合列表")
    public ActionResult<List<ProjPortfolioEntity>> getListByOwnerId(@PathVariable String ownerId) {
        if (StrXhUtil.isBlank(ownerId)) {
            return ActionResult.fail("负责人ID不能为空");
        }
        List<ProjPortfolioEntity> list = projPortfolioService.getListByOwnerId(ownerId);
        return ActionResult.success(list);
    }

    /**
     * 根据组合类型ID获取组合列表
     *
     * @param typeId 组合类型ID
     * @return 组合列表
     */
    @GetMapping("/type/{typeId}")
    @Operation(summary = "根据组合类型ID获取组合列表")
    public ActionResult<List<ProjPortfolioEntity>> getListByTypeId(@PathVariable String typeId) {
        if (StrXhUtil.isBlank(typeId)) {
            return ActionResult.fail("组合类型ID不能为空");
        }
        List<ProjPortfolioEntity> list = projPortfolioService.getListByTypeId(typeId);
        return ActionResult.success(list);
    }

    /**
     * 根据状态获取组合列表
     *
     * @param status 状态
     * @return 组合列表
     */
    @GetMapping("/status/{status}")
    @Operation(summary = "根据状态获取组合列表")
    public ActionResult<List<ProjPortfolioEntity>> getListByStatus(@PathVariable String status) {
        if (StrXhUtil.isBlank(status)) {
            return ActionResult.fail("状态不能为空");
        }
        List<ProjPortfolioEntity> list = projPortfolioService.getListByStatus(status);
        return ActionResult.success(list);
    }

    /**
     * 检查组合编码是否存在
     *
     * @param code 组合编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    @GetMapping("/checkCode")
    @Operation(summary = "检查组合编码是否存在")
    public ActionResult<Boolean> checkCodeExists(@RequestParam String code, @RequestParam(required = false) String excludeId) {
        if (StrXhUtil.isBlank(code)) {
            return ActionResult.fail("组合编码不能为空");
        }
        boolean exists = projPortfolioService.isExistByCode(code, excludeId);
        return ActionResult.success(exists);
    }
}
