package com.xinghuo.project.template.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 交付物计划模板明细表实体类
 * 对应数据库表：zz_proj_tpl_workproduct_plan_detail
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_tpl_workproduct_plan_detail")
public class WorkProductPlanTemplateDetailEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 所属模板主表ID
     */
    @TableField("workproduct_plan_tpl_id")
    private String workproductPlanTplId;

    /**
     * 关联的标准交付物库ID (如果从库添加)
     */
    @TableField("workproduct_library_id")
    private String workproductLibraryId;

    /**
     * 交付物名称 (可继承自库, 也可手动填写)
     */
    @TableField("name")
    private String name;

    /**
     * 交付物编码
     */
    @TableField("code")
    private String code;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 序号 (用于排序)
     */
    @TableField("seq_no")
    private Integer seqNo;

    /**
     * 关联的阶段模板ID (定义该交付物在哪个阶段产出)
     */
    @TableField("stage_template_id")
    private String stageTemplateId;

    /**
     * 关联的活动模板ID (更精细的位置)
     */
    @TableField("activity_template_id")
    private String activityTemplateId;

    /**
     * 交付物类型ID
     */
    @TableField("type_id")
    private String typeId;

    /**
     * 交付物子类型ID
     */
    @TableField("sub_type_id")
    private String subTypeId;

    /**
     * 责任角色ID
     */
    @TableField("response_role_id")
    private String responseRoleId;

    /**
     * 是否需要评审 (1:是, 0:否)
     */
    @TableField("need_review")
    private Integer needReview;

    /**
     * 是否是项目最终交付成果 (1:是, 0:否)
     */
    @TableField("is_deliverable")
    private Integer isDeliverable;

    /**
     * 是否可裁剪 (1:可, 0:不可)
     */
    @TableField("can_cut")
    private Integer canCut;
}
