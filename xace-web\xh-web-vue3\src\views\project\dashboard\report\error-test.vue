<template>
  <div class="error-test-page">
    <ErrorBoundary>
      <SafeRefreshHandler>
        <PageWrapper title="Vue错误修复测试" contentBackground>
          <a-card title="KeepAlive错误修复测试">
            <a-space direction="vertical" style="width: 100%" size="large">
              
              <!-- 测试说明 -->
              <a-alert
                message="测试说明"
                description="此页面用于测试Vue KeepAlive相关错误的修复效果。点击下方按钮进行各种测试。"
                type="info"
                show-icon
              />

              <!-- 路由跳转测试 -->
              <a-card title="路由跳转测试" size="small">
                <a-space wrap>
                  <a-button type="primary" @click="testSafeNavigation">
                    安全路由跳转
                  </a-button>
                  <a-button @click="testNormalNavigation">
                    普通路由跳转
                  </a-button>
                  <a-button @click="testBackNavigation">
                    返回上一页
                  </a-button>
                </a-space>
              </a-card>

              <!-- 页面刷新测试 -->
              <a-card title="页面刷新测试" size="small">
                <a-space wrap>
                  <a-button type="primary" @click="testSafeRefresh">
                    安全刷新
                  </a-button>
                  <a-button @click="testForceRefresh">
                    强制刷新
                  </a-button>
                  <a-button @click="testBrowserRefresh">
                    浏览器刷新
                  </a-button>
                </a-space>
              </a-card>

              <!-- 错误模拟测试 -->
              <a-card title="错误模拟测试" size="small">
                <a-space wrap>
                  <a-button danger @click="simulateKeepAliveError">
                    模拟KeepAlive错误
                  </a-button>
                  <a-button danger @click="simulateRouteError">
                    模拟路由错误
                  </a-button>
                  <a-button @click="resetErrorState">
                    重置错误状态
                  </a-button>
                </a-space>
              </a-card>

              <!-- 测试结果显示 -->
              <a-card title="测试结果" size="small">
                <div v-if="testResults.length === 0" class="no-results">
                  暂无测试结果
                </div>
                <a-timeline v-else>
                  <a-timeline-item
                    v-for="(result, index) in testResults"
                    :key="index"
                    :color="result.success ? 'green' : 'red'"
                  >
                    <div class="test-result-item">
                      <div class="result-title">{{ result.title }}</div>
                      <div class="result-time">{{ result.time }}</div>
                      <div class="result-message" :class="{ error: !result.success }">
                        {{ result.message }}
                      </div>
                    </div>
                  </a-timeline-item>
                </a-timeline>
              </a-card>

              <!-- 清理按钮 -->
              <div class="actions">
                <a-space>
                  <a-button @click="clearResults">
                    清空结果
                  </a-button>
                  <a-button type="primary" @click="runAllTests">
                    运行所有测试
                  </a-button>
                </a-space>
              </div>

            </a-space>
          </a-card>
        </PageWrapper>
      </SafeRefreshHandler>
    </ErrorBoundary>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { PageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import ErrorBoundary from './components/ErrorBoundary.vue';
  import SafeRefreshHandler from './components/SafeRefreshHandler.vue';
  import { safeNavigate, safeRefresh, handleRouteError } from './utils/routeGuard';

  defineOptions({ name: 'ErrorTestPage' });

  const router = useRouter();
  const { createMessage } = useMessage();

  interface TestResult {
    title: string;
    time: string;
    message: string;
    success: boolean;
  }

  const testResults = ref<TestResult[]>([]);

  // 添加测试结果
  function addTestResult(title: string, message: string, success: boolean = true) {
    testResults.value.unshift({
      title,
      time: new Date().toLocaleTimeString(),
      message,
      success
    });
  }

  // 安全路由跳转测试
  async function testSafeNavigation() {
    try {
      await safeNavigate(router, '/project/report/invoice');
      addTestResult('安全路由跳转', '成功跳转到发票报表页面');
    } catch (error) {
      addTestResult('安全路由跳转', `失败: ${error}`, false);
    }
  }

  // 普通路由跳转测试
  function testNormalNavigation() {
    try {
      router.push('/project/report/payment');
      addTestResult('普通路由跳转', '尝试跳转到收款报表页面');
    } catch (error) {
      addTestResult('普通路由跳转', `失败: ${error}`, false);
    }
  }

  // 返回上一页测试
  function testBackNavigation() {
    try {
      router.back();
      addTestResult('返回上一页', '执行返回操作');
    } catch (error) {
      addTestResult('返回上一页', `失败: ${error}`, false);
    }
  }

  // 安全刷新测试
  async function testSafeRefresh() {
    try {
      await safeRefresh(router);
      addTestResult('安全刷新', '页面安全刷新成功');
    } catch (error) {
      addTestResult('安全刷新', `失败: ${error}`, false);
    }
  }

  // 强制刷新测试
  function testForceRefresh() {
    try {
      const currentRoute = router.currentRoute.value;
      router.replace({
        path: currentRoute.path,
        query: { ...currentRoute.query, _force: Date.now() }
      });
      addTestResult('强制刷新', '执行强制刷新');
    } catch (error) {
      addTestResult('强制刷新', `失败: ${error}`, false);
    }
  }

  // 浏览器刷新测试
  function testBrowserRefresh() {
    addTestResult('浏览器刷新', '即将执行浏览器刷新...');
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  }

  // 模拟KeepAlive错误
  function simulateKeepAliveError() {
    try {
      // 创建一个模拟的KeepAlive错误
      const error = new Error("Cannot destructure property 'type' of 'vnode' as it is null.");
      handleRouteError(router, error);
      addTestResult('模拟KeepAlive错误', '已触发KeepAlive错误处理');
    } catch (error) {
      addTestResult('模拟KeepAlive错误', `处理失败: ${error}`, false);
    }
  }

  // 模拟路由错误
  function simulateRouteError() {
    try {
      // 尝试跳转到不存在的路由
      router.push('/project/report/nonexistent');
      addTestResult('模拟路由错误', '尝试跳转到不存在的路由');
    } catch (error) {
      addTestResult('模拟路由错误', `错误: ${error}`, false);
    }
  }

  // 重置错误状态
  function resetErrorState() {
    try {
      // 清理可能的错误状态
      router.replace('/project/report/error-test');
      addTestResult('重置错误状态', '已重置页面状态');
    } catch (error) {
      addTestResult('重置错误状态', `失败: ${error}`, false);
    }
  }

  // 清空测试结果
  function clearResults() {
    testResults.value = [];
    createMessage.success('已清空测试结果');
  }

  // 运行所有测试
  async function runAllTests() {
    clearResults();
    addTestResult('开始测试', '开始运行所有测试项目');
    
    // 依次运行各项测试
    const tests = [
      { name: '安全路由跳转', fn: testSafeNavigation },
      { name: '安全刷新', fn: testSafeRefresh },
      { name: '模拟KeepAlive错误', fn: simulateKeepAliveError },
    ];

    for (const test of tests) {
      try {
        await test.fn();
        await new Promise(resolve => setTimeout(resolve, 500)); // 等待500ms
      } catch (error) {
        addTestResult(test.name, `测试失败: ${error}`, false);
      }
    }

    addTestResult('测试完成', '所有测试项目已完成');
  }
</script>

<style lang="less" scoped>
.error-test-page {
  .no-results {
    color: #999;
    text-align: center;
    padding: 20px;
  }

  .test-result-item {
    .result-title {
      font-weight: 600;
      color: #2c3e50;
    }

    .result-time {
      font-size: 12px;
      color: #999;
      margin: 4px 0;
    }

    .result-message {
      color: #52c41a;
      
      &.error {
        color: #ff4d4f;
      }
    }
  }

  .actions {
    text-align: center;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
