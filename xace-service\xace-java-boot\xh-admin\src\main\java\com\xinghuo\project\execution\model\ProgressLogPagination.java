package com.xinghuo.project.execution.model;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 进展日志分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "进展日志分页查询参数")
public class ProgressLogPagination extends Pagination {

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String projectId;

    /**
     * 合同ID
     */
    @Schema(description = "合同ID")
    private String contractId;

    /**
     * 日志类型
     */
    @Schema(description = "日志类型")
    private String logType;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    private String creatorId;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始")
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束")
    private Date createTimeEnd;

    /**
     * 关键字搜索（日志内容）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}
