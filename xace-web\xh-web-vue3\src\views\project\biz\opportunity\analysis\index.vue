<template>
  <div class="business-analysis">
    <a-card :bordered="false" title="商机分析">
      <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="summary" tab="汇总报表">
          <business-summary-report />
        </a-tab-pane>
        <a-tab-pane key="charts" tab="图表分析">
          <business-analysis-charts />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import BusinessSummaryReport from './reports/BusinessSummaryReport.vue';
  import BusinessAnalysisCharts from './charts/BusinessAnalysisCharts.vue';

  const activeTab = ref('summary');
    defineOptions({ name: 'project-business-analysis' });

</script>

<style lang="less" scoped>
.business-analysis {
  padding: 16px;
}
</style>
