package com.xinghuo.project.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.core.dao.TagMapper;
import com.xinghuo.project.core.entity.TagEntity;
import com.xinghuo.project.core.model.tag.TagPagination;
import com.xinghuo.project.core.service.TagService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 标签服务实现类
 */
@Service
public class TagServiceImpl extends BaseServiceImpl<TagMapper, TagEntity> implements TagService {



    @Override
    public List<TagEntity> getList(TagPagination pagination) {
        QueryWrapper<TagEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<TagEntity> lambda = queryWrapper.lambda();
        
        // 根据标签名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getTagName())) {
            lambda.like(TagEntity::getTagName, pagination.getTagName());
        }
        
        // 根据标签范围精确查询
        if (StrXhUtil.isNotEmpty(pagination.getScope())) {
            lambda.eq(TagEntity::getScope, pagination.getScope());
        }
        
        // 根据关键字搜索标签名称或描述
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(TagEntity::getTagName, keyword)
                    .or()
                    .like(TagEntity::getDescription, keyword)
            );
        }
        
        // 排序
        lambda.orderByAsc(TagEntity::getTagName);
        // 分页
        return processDataType(queryWrapper,pagination);
    }

    @Override
    public TagEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    public void create(TagEntity entity) {
        entity.setId(RandomUtil.snowId());
        this.save(entity);
    }

    @Override
    public void update(String id, TagEntity entity) {
        entity.setId(id);
        this.updateById(entity);
    }

    @Override
    public void delete(String id) {
        this.removeById(id);
    }

    @Override
    public boolean isExistByTagName(String tagName, String id) {
        LambdaQueryWrapper<TagEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagEntity::getTagName, tagName);
        
        // 如果是更新操作，需要排除自身
        if (StrXhUtil.isNotEmpty(id)) {
            queryWrapper.ne(TagEntity::getId, id);
        }
        
        return this.count(queryWrapper) > 0;
    }
}
