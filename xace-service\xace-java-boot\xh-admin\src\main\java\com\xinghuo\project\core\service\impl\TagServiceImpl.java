package com.xinghuo.project.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.core.dao.TagMapper;
import com.xinghuo.project.core.entity.TagEntity;
import com.xinghuo.project.core.model.tag.TagPagination;
import com.xinghuo.project.core.service.TagService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 标签服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class TagServiceImpl extends BaseServiceImpl<TagMapper, TagEntity> implements TagService {

    @Resource
    private TagMapper tagMapper;



    @Override
    public List<TagEntity> getList(TagPagination pagination) {
        QueryWrapper<TagEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<TagEntity> lambda = queryWrapper.lambda();
        
        // 根据标签名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getTagName())) {
            lambda.like(TagEntity::getTagName, pagination.getTagName());
        }
        
        // 根据标签范围精确查询
        if (StrXhUtil.isNotEmpty(pagination.getScope())) {
            lambda.eq(TagEntity::getScope, pagination.getScope());
        }
        
        // 根据关键字搜索标签名称或描述
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(TagEntity::getTagName, keyword)
                    .or()
                    .like(TagEntity::getDescription, keyword)
            );
        }
        
        // 排序
        lambda.orderByAsc(TagEntity::getTagName);
        // 分页
        return processDataType(queryWrapper,pagination);
    }

    @Override
    public TagEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(TagEntity entity) {
        String id = RandomUtil.snowId();
        entity.setId(id);
        this.save(entity);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, TagEntity entity) {
        entity.setId(id);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        this.removeById(id);
    }

    @Override
    public boolean isExistByTagName(String tagName, String excludeId) {
        int count = tagMapper.checkTagNameExists(tagName, excludeId);
        return count > 0;
    }

    @Override
    public List<TagEntity> getSelectList(String keyword) {
        if (StrXhUtil.isNotEmpty(keyword)) {
            // 使用Mapper中的专门方法进行关键字查询
            return tagMapper.selectForSelect(keyword);
        } else {
            // 如果没有关键字，返回所有可用的标签
            QueryWrapper<TagEntity> queryWrapper = new QueryWrapper<>();
            LambdaQueryWrapper<TagEntity> lambda = queryWrapper.lambda();

            // 只返回全局可见的标签，按标签名称排序
            lambda.eq(TagEntity::getScope, "global");
            lambda.orderBy(true, true, TagEntity::getTagName);

            return this.list(queryWrapper);
        }
    }
}
