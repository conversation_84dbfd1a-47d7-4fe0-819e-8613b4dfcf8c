package com.xinghuo.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目-标签关联表实体类
 * 对应数据库表：zz_proj_tag_rel
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_tag_rel")
public class ProjTagRelEntity extends BaseEntityV2.BaseEntityV2<String> {

    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 标签ID (关联字典表)
     */
    @TableField("tag_id")
    private String tagId;
}
