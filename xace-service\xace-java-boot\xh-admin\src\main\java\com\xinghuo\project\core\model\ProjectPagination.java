package com.xinghuo.project.core.model;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "项目分页查询参数")
public class ProjectPagination extends Pagination {

    /**
     * 项目编码
     */
    @Schema(description = "项目编码")
    private String code;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String name;

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    private String projectType;

    /**
     * 项目状态
     */
    @Schema(description = "项目状态")
    private String status;

    /**
     * 项目健康度
     */
    @Schema(description = "项目健康度")
    private String health;

    /**
     * 项目经理ID
     */
    @Schema(description = "项目经理ID")
    private String managerId;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private String deptId;

    /**
     * 优先级
     */
    @Schema(description = "优先级")
    private String priority;

    /**
     * 项目开始日期开始
     */
    @Schema(description = "项目开始日期开始")
    private Date startDateStart;

    /**
     * 项目开始日期结束
     */
    @Schema(description = "项目开始日期结束")
    private Date startDateEnd;

    /**
     * 项目结束日期开始
     */
    @Schema(description = "项目结束日期开始")
    private Date endDateStart;

    /**
     * 项目结束日期结束
     */
    @Schema(description = "项目结束日期结束")
    private Date endDateEnd;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始")
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束")
    private Date createTimeEnd;

    /**
     * 是否归档
     */
    @Schema(description = "是否归档")
    private Boolean archived;

    /**
     * 关键字搜索（项目名称或编码）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}
