package com.xinghuo.project.biz.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.biz.entity.PaymentContractEntity;
import com.xinghuo.project.biz.model.PaymentContractPagination;
import com.xinghuo.project.biz.service.PaymentContractService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 付款合同管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "付款合同管理", description = "付款合同管理相关接口")
@RestController
@RequestMapping("/api/project/biz/paymentContract")
public class PaymentContractController {

    @Resource
    private PaymentContractService paymentContractService;

    /**
     * 获取付款合同列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取付款合同列表")
    public ActionResult<PageListVO<PaymentContractEntity>> list(@RequestBody PaymentContractPagination pagination) {
        List<PaymentContractEntity> list = paymentContractService.getList(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 根据收款合同ID获取付款合同列表
     */
    @GetMapping("/getListByContractId/{contractId}")
    @Operation(summary = "根据收款合同ID获取付款合同列表")
    public ActionResult<List<PaymentContractEntity>> getListByContractId(
            @Parameter(description = "收款合同ID") @PathVariable String contractId) {
        List<PaymentContractEntity> list = paymentContractService.getListByContractId(contractId);
        return ActionResult.success(list);
    }

    /**
     * 根据供应商ID获取付款合同列表
     */
    @GetMapping("/getListBySupplierId/{supplierId}")
    @Operation(summary = "根据供应商ID获取付款合同列表")
    public ActionResult<List<PaymentContractEntity>> getListBySupplierId(
            @Parameter(description = "供应商ID") @PathVariable String supplierId) {
        List<PaymentContractEntity> list = paymentContractService.getListBySupplierId(supplierId);
        return ActionResult.success(list);
    }

    /**
     * 获取付款合同详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取付款合同详情")
    public ActionResult<PaymentContractEntity> getInfo(
            @Parameter(description = "付款合同ID") @PathVariable String id) {
        PaymentContractEntity entity = paymentContractService.getInfo(id);
        return ActionResult.success(entity);
    }

    /**
     * 创建付款合同
     */
    @PostMapping("/create")
    @Operation(summary = "创建付款合同")
    public ActionResult<String> create(@RequestBody @Valid PaymentContractEntity entity) {
        String id = paymentContractService.create(entity);
        return ActionResult.success("创建成功", id);
    }

    /**
     * 更新付款合同
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新付款合同")
    public ActionResult<String> update(
            @Parameter(description = "付款合同ID") @PathVariable String id,
            @RequestBody @Valid PaymentContractEntity entity) {
        paymentContractService.update(id, entity);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除付款合同
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除付款合同")
    public ActionResult<String> delete(
            @Parameter(description = "付款合同ID") @PathVariable String id) {
        paymentContractService.delete(id);
        return ActionResult.success("删除成功");
    }

    /**
     * 更新付款合同状态
     */
    @PutMapping("/updateStatus/{id}")
    @Operation(summary = "更新付款合同状态")
    public ActionResult<String> updateStatus(
            @Parameter(description = "付款合同ID") @PathVariable String id,
            @RequestParam String status) {
        paymentContractService.updateStatus(id, status);
        return ActionResult.success("状态更新成功");
    }

    /**
     * 签订付款合同
     */
    @PutMapping("/sign/{id}")
    @Operation(summary = "签订付款合同")
    public ActionResult<String> sign(
            @Parameter(description = "付款合同ID") @PathVariable String id,
            @RequestParam String cNo,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date signDate) {
        paymentContractService.sign(id, cNo, signDate);
        return ActionResult.success("签订成功");
    }

    /**
     * 检查付款合同编号是否存在
     */
    @GetMapping("/checkCNoExists")
    @Operation(summary = "检查付款合同编号是否存在")
    public ActionResult<Boolean> checkCNoExists(
            @RequestParam String cNo,
            @RequestParam(required = false) String excludeId) {
        boolean exists = paymentContractService.isExistByCNo(cNo, excludeId);
        return ActionResult.success(exists);
    }

    /**
     * 获取付款合同选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取付款合同选择列表")
    public ActionResult<List<Map<String, Object>>> getSelectList(
            @RequestParam(required = false) String keyword) {
        List<PaymentContractEntity> list = paymentContractService.getSelectList(keyword);
        List<Map<String, Object>> selectorList = new ArrayList<>();
        
        for (PaymentContractEntity contract : list) {
            Map<String, Object> option = new HashMap<>();
            option.put("id", contract.getCid());
            option.put("fullName", contract.getCno() + " - " + contract.getName());
            option.put("cno", contract.getCno());
            option.put("name", contract.getName());
            selectorList.add(option);
        }
        
        return ActionResult.success(selectorList);
    }
}
