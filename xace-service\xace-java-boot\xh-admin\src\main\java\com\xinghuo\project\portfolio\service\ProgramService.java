package com.xinghuo.project.portfolio.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.portfolio.entity.ProgramEntity;
import com.xinghuo.project.portfolio.model.ProgramPagination;

import java.util.List;

/**
 * 项目群服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ProgramService extends BaseService<ProgramEntity> {

    /**
     * 分页查询项目群列表
     *
     * @param pagination 查询条件
     * @return 项目群列表
     */
    List<ProgramEntity> getList(ProgramPagination pagination);

    /**
     * 根据ID查询项目群信息
     *
     * @param id 项目群ID
     * @return 项目群信息
     */
    ProgramEntity getInfo(String id);

    /**
     * 创建项目群
     *
     * @param entity 项目群信息
     * @return 创建结果
     */
    boolean saveInfo(ProgramEntity entity);

    /**
     * 更新项目群
     *
     * @param entity 更新信息
     * @return 更新结果
     */
    boolean updateInfo(ProgramEntity entity);

    /**
     * 删除项目群
     *
     * @param id 项目群ID
     * @return 删除结果
     */
    boolean deleteById(String id);

    /**
     * 根据项目群经理ID查询项目群列表
     *
     * @param managerId 项目群经理ID
     * @return 项目群列表
     */
    List<ProgramEntity> getListByManagerId(String managerId);

    /**
     * 根据项目群类型ID查询项目群列表
     *
     * @param typeId 项目群类型ID
     * @return 项目群列表
     */
    List<ProgramEntity> getListByTypeId(String typeId);

    /**
     * 根据状态查询项目群列表
     *
     * @param status 状态
     * @return 项目群列表
     */
    List<ProgramEntity> getListByStatus(String status);

    /**
     * 检查项目群编码是否存在
     *
     * @param code 项目群编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByCode(String code, String excludeId);
}
