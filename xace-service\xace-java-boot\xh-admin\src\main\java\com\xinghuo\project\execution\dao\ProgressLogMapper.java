package com.xinghuo.project.execution.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.execution.entity.log.ProgressLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 进展日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface ProgressLogMapper extends XHBaseMapper<ProgressLogEntity> {

    /**
     * 根据项目ID查询进展日志列表
     *
     * @param projectId 项目ID
     * @return 进展日志列表
     */
    List<ProgressLogEntity> selectByProjectId(@Param("projectId") String projectId);

    /**
     * 根据合同ID查询进展日志列表
     *
     * @param contractId 合同ID
     * @return 进展日志列表
     */
    List<ProgressLogEntity> selectByContractId(@Param("contractId") String contractId);

    /**
     * 根据创建人ID查询进展日志列表
     *
     * @param creatorId 创建人ID
     * @return 进展日志列表
     */
    List<ProgressLogEntity> selectByCreatorId(@Param("creatorId") String creatorId);
}
