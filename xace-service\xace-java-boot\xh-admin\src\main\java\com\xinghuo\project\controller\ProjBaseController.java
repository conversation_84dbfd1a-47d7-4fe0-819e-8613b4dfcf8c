package com.xinghuo.project.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.entity.ProjBaseEntity;
import com.xinghuo.project.model.base.ProjBasePagination;
import com.xinghuo.project.service.ProjBaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 项目基础信息控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@RestController
@Tag(name = "项目管理", description = "项目基础信息管理相关接口")
@RequestMapping("/api/project/base")
public class ProjBaseController {

    @Resource
    private ProjBaseService projBaseService;

    /**
     * 获取项目列表
     *
     * @param pagination 分页查询参数
     * @return 项目列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取项目列表")
    public ActionResult<PageListVO<ProjBaseEntity>> list(@RequestBody ProjBasePagination pagination) {
        List<ProjBaseEntity> list = projBaseService.getList(pagination);
        // 暂时直接返回Entity，后续可以创建VO类进行转换
        PaginationVO page = new PaginationVO();
        page.setCurrentPage(1);
        page.setPageSize(list.size());
        page.setTotal((long) list.size());
        return ActionResult.page(list, page);
    }

    /**
     * 根据ID获取项目信息
     *
     * @param id 项目ID
     * @return 项目信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取项目信息")
    public ActionResult<ProjBaseEntity> getInfo(@PathVariable String id) {
        if (StrXhUtil.isBlank(id)) {
            return ActionResult.fail("项目ID不能为空");
        }
        ProjBaseEntity entity = projBaseService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("项目不存在");
        }
        return ActionResult.success(entity);
    }

    /**
     * 创建项目
     *
     * @param entity 项目信息
     * @return 创建结果
     */
    @PostMapping
    @Operation(summary = "创建项目")
    public ActionResult<String> create(@RequestBody @Valid ProjBaseEntity entity) {
        try {
            boolean result = projBaseService.saveInfo(entity);
            if (result) {
                return ActionResult.success("创建项目成功", entity.getId());
            } else {
                return ActionResult.fail("创建项目失败");
            }
        } catch (RuntimeException e) {
            log.error("创建项目异常", e);
            return ActionResult.fail(e.getMessage());
        }
    }

    /**
     * 更新项目
     *
     * @param id 项目ID
     * @param entity 更新信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新项目")
    public ActionResult<String> update(@PathVariable String id, @RequestBody @Valid ProjBaseEntity entity) {
        if (StrXhUtil.isBlank(id)) {
            return ActionResult.fail("项目ID不能为空");
        }
        
        try {
            entity.setId(id);
            boolean result = projBaseService.updateInfo(entity);
            if (result) {
                return ActionResult.success("更新项目成功");
            } else {
                return ActionResult.fail("更新项目失败");
            }
        } catch (RuntimeException e) {
            log.error("更新项目异常", e);
            return ActionResult.fail(e.getMessage());
        }
    }

    /**
     * 删除项目
     *
     * @param id 项目ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除项目")
    public ActionResult<String> delete(@PathVariable String id) {
        if (StrXhUtil.isBlank(id)) {
            return ActionResult.fail("项目ID不能为空");
        }
        
        try {
            boolean result = projBaseService.deleteById(id);
            if (result) {
                return ActionResult.success("删除项目成功");
            } else {
                return ActionResult.fail("删除项目失败");
            }
        } catch (RuntimeException e) {
            log.error("删除项目异常", e);
            return ActionResult.fail(e.getMessage());
        }
    }

    /**
     * 根据项目经理ID获取项目列表
     *
     * @param managerId 项目经理ID
     * @return 项目列表
     */
    @GetMapping("/manager/{managerId}")
    @Operation(summary = "根据项目经理ID获取项目列表")
    public ActionResult<List<ProjBaseEntity>> getListByManagerId(@PathVariable String managerId) {
        if (StrXhUtil.isBlank(managerId)) {
            return ActionResult.fail("项目经理ID不能为空");
        }
        List<ProjBaseEntity> list = projBaseService.getListByManagerId(managerId);
        return ActionResult.success(list);
    }

    /**
     * 根据项目群ID获取项目列表
     *
     * @param programId 项目群ID
     * @return 项目列表
     */
    @GetMapping("/program/{programId}")
    @Operation(summary = "根据项目群ID获取项目列表")
    public ActionResult<List<ProjBaseEntity>> getListByProgramId(@PathVariable String programId) {
        if (StrXhUtil.isBlank(programId)) {
            return ActionResult.fail("项目群ID不能为空");
        }
        List<ProjBaseEntity> list = projBaseService.getListByProgramId(programId);
        return ActionResult.success(list);
    }

    /**
     * 根据项目类型ID获取项目列表
     *
     * @param typeId 项目类型ID
     * @return 项目列表
     */
    @GetMapping("/type/{typeId}")
    @Operation(summary = "根据项目类型ID获取项目列表")
    public ActionResult<List<ProjBaseEntity>> getListByTypeId(@PathVariable String typeId) {
        if (StrXhUtil.isBlank(typeId)) {
            return ActionResult.fail("项目类型ID不能为空");
        }
        List<ProjBaseEntity> list = projBaseService.getListByTypeId(typeId);
        return ActionResult.success(list);
    }

    /**
     * 根据项目状态ID获取项目列表
     *
     * @param statusId 项目状态ID
     * @return 项目列表
     */
    @GetMapping("/status/{statusId}")
    @Operation(summary = "根据项目状态ID获取项目列表")
    public ActionResult<List<ProjBaseEntity>> getListByStatusId(@PathVariable String statusId) {
        if (StrXhUtil.isBlank(statusId)) {
            return ActionResult.fail("项目状态ID不能为空");
        }
        List<ProjBaseEntity> list = projBaseService.getListByStatusId(statusId);
        return ActionResult.success(list);
    }

    /**
     * 根据健康状态获取项目列表
     *
     * @param healthStatus 健康状态
     * @return 项目列表
     */
    @GetMapping("/health/{healthStatus}")
    @Operation(summary = "根据健康状态获取项目列表")
    public ActionResult<List<ProjBaseEntity>> getListByHealthStatus(@PathVariable String healthStatus) {
        if (StrXhUtil.isBlank(healthStatus)) {
            return ActionResult.fail("健康状态不能为空");
        }
        List<ProjBaseEntity> list = projBaseService.getListByHealthStatus(healthStatus);
        return ActionResult.success(list);
    }

    /**
     * 检查项目编码是否存在
     *
     * @param code 项目编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    @GetMapping("/checkCode")
    @Operation(summary = "检查项目编码是否存在")
    public ActionResult<Boolean> checkCodeExists(@RequestParam String code, @RequestParam(required = false) String excludeId) {
        if (StrXhUtil.isBlank(code)) {
            return ActionResult.fail("项目编码不能为空");
        }
        boolean exists = projBaseService.isExistByCode(code, excludeId);
        return ActionResult.success(exists);
    }

    /**
     * 统计项目群下的项目数量
     *
     * @param programId 项目群ID
     * @return 项目数量
     */
    @GetMapping("/count/program/{programId}")
    @Operation(summary = "统计项目群下的项目数量")
    public ActionResult<Integer> countByProgramId(@PathVariable String programId) {
        if (StrXhUtil.isBlank(programId)) {
            return ActionResult.fail("项目群ID不能为空");
        }
        int count = projBaseService.countByProgramId(programId);
        return ActionResult.success(count);
    }
}
