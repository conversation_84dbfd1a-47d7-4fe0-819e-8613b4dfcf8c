package com.xinghuo.project.template.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.template.entity.TemplateRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通用模板关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface TemplateRelationMapper extends XHBaseMapper<TemplateRelationEntity> {

    /**
     * 根据源模板ID和类型查询关联的目标模板ID列表
     *
     * @param sourceTemplateId 源模板ID
     * @param sourceTemplateType 源模板类型
     * @param targetTemplateType 目标模板类型
     * @return 目标模板ID列表
     */
    List<String> selectTargetTemplateIds(@Param("sourceTemplateId") String sourceTemplateId,
                                        @Param("sourceTemplateType") String sourceTemplateType,
                                        @Param("targetTemplateType") String targetTemplateType);

    /**
     * 根据目标模板ID和类型查询关联的源模板ID列表
     *
     * @param targetTemplateId 目标模板ID
     * @param targetTemplateType 目标模板类型
     * @param sourceTemplateType 源模板类型
     * @return 源模板ID列表
     */
    List<String> selectSourceTemplateIds(@Param("targetTemplateId") String targetTemplateId,
                                        @Param("targetTemplateType") String targetTemplateType,
                                        @Param("sourceTemplateType") String sourceTemplateType);

    /**
     * 根据源模板ID和类型删除所有关联关系
     *
     * @param sourceTemplateId 源模板ID
     * @param sourceTemplateType 源模板类型
     * @return 删除数量
     */
    int deleteBySourceTemplate(@Param("sourceTemplateId") String sourceTemplateId,
                              @Param("sourceTemplateType") String sourceTemplateType);

    /**
     * 根据目标模板ID和类型删除所有关联关系
     *
     * @param targetTemplateId 目标模板ID
     * @param targetTemplateType 目标模板类型
     * @return 删除数量
     */
    int deleteByTargetTemplate(@Param("targetTemplateId") String targetTemplateId,
                              @Param("targetTemplateType") String targetTemplateType);

    /**
     * 批量插入关联关系
     *
     * @param relations 关联关系列表
     * @return 插入数量
     */
    int batchInsert(@Param("relations") List<TemplateRelationEntity> relations);

    /**
     * 检查关联关系是否存在
     *
     * @param sourceTemplateId 源模板ID
     * @param sourceTemplateType 源模板类型
     * @param targetTemplateId 目标模板ID
     * @param targetTemplateType 目标模板类型
     * @return 数量
     */
    int checkRelationExists(@Param("sourceTemplateId") String sourceTemplateId,
                           @Param("sourceTemplateType") String sourceTemplateType,
                           @Param("targetTemplateId") String targetTemplateId,
                           @Param("targetTemplateType") String targetTemplateType);

    /**
     * 删除指定的关联关系
     *
     * @param sourceTemplateId 源模板ID
     * @param sourceTemplateType 源模板类型
     * @param targetTemplateId 目标模板ID
     * @param targetTemplateType 目标模板类型
     * @return 删除数量
     */
    int deleteRelation(@Param("sourceTemplateId") String sourceTemplateId,
                      @Param("sourceTemplateType") String sourceTemplateType,
                      @Param("targetTemplateId") String targetTemplateId,
                      @Param("targetTemplateType") String targetTemplateType);

    /**
     * 根据源模板类型查询所有关联关系
     *
     * @param sourceTemplateType 源模板类型
     * @return 关联关系列表
     */
    List<TemplateRelationEntity> selectBySourceTemplateType(@Param("sourceTemplateType") String sourceTemplateType);

    /**
     * 根据目标模板类型查询所有关联关系
     *
     * @param targetTemplateType 目标模板类型
     * @return 关联关系列表
     */
    List<TemplateRelationEntity> selectByTargetTemplateType(@Param("targetTemplateType") String targetTemplateType);

    /**
     * 批量删除关联关系
     *
     * @param sourceTemplateIds 源模板ID列表
     * @param sourceTemplateType 源模板类型
     * @return 删除数量
     */
    int batchDeleteBySourceTemplateIds(@Param("sourceTemplateIds") List<String> sourceTemplateIds,
                                      @Param("sourceTemplateType") String sourceTemplateType);
}
