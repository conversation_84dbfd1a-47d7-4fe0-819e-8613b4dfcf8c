package com.xinghuo.project.core.enums;

/**
 * 项目状态枚举
 */
public enum ProjectStatusEnum {
    
    NOT_STARTED("not_started", "未启动"),
    IN_PROGRESS("in_progress", "进行中"),
    COMPLETED("completed", "已完成"),
    CLOSED("closed", "已关闭"),
    CANCELLED("cancelled", "已取消"),
    ON_HOLD("on_hold", "已暂停"),
    DELAYED("delayed", "已延期");
    
    private final String code;
    private final String name;
    
    ProjectStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static ProjectStatusEnum getByCode(String code) {
        for (ProjectStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
