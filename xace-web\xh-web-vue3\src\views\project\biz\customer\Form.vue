<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import { createCustomer, updateCustomer, CustomerModel } from '/@/api/project/customer';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'reload']);
  const isUpdate = ref(false);
  const customerId = ref('');

  // 表单配置
  const formSchemas: FormSchema[] = [
    {
      field: 'custType',
      label: '客户类型',
      component: 'Select',
      componentProps: {
        options: [
          { label: '政府单位', value: 'government' },
          { label: '企业单位', value: 'enterprise' },
          { label: '事业单位', value: 'institution' },
          { label: '其他', value: 'other' },
        ],
        placeholder: '请选择客户类型',
      },
      rules: [{ required: true, trigger: 'change', message: '请选择客户类型' }],
    },
    {
      field: 'type',
      label: '类型',
      component: 'Input',
      componentProps: { placeholder: '请输入类型', maxlength: 50 },
    },
    {
      field: 'name',
      label: '客户名称',
      component: 'Input',
      componentProps: { placeholder: '请输入客户名称', maxlength: 100 },
      rules: [
        { required: true, trigger: 'blur', message: '请输入客户名称' },
        { max: 100, message: '客户名称最多为100个字符', trigger: 'blur' },
      ],
    },
    {
      field: 'leader',
      label: '负责人',
      component: 'Input',
      componentProps: { placeholder: '请输入负责人', maxlength: 200 },
    },
    {
      field: 'remark',
      label: '备注',
      component: 'InputTextArea',
      componentProps: { placeholder: '请输入备注', maxlength: 500, showCount: true, rows: 4 },
      rules: [{ max: 500, message: '备注最多为500个字符', trigger: 'blur' }],
    },
    {
      field: 'sortCode',
      label: '排序码',
      component: 'InputNumber',
      componentProps: { placeholder: '请输入排序码', min: 0 },
    },
  ];

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 80,
    schemas: formSchemas,
    showActionButtonGroup: false,
  });

  // 注册模态框
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });

    isUpdate.value = !!data?.isUpdate;
    if (unref(isUpdate)) {
      customerId.value = data.record.id;
      setFieldsValue({
        ...data.record,
      });
    }
  });

  // 获取标题
  const getTitle = computed(() => {
    return unref(isUpdate) ? '编辑客户单位' : '新增客户单位';
  });

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      if (unref(isUpdate)) {
        await updateCustomer(customerId.value, values);
        createMessage.success('更新成功');
      } else {
        await createCustomer(values);
        createMessage.success('新增成功');
      }

      closeModal();
      emit('reload');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
