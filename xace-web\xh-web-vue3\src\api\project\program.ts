import { defHttp } from '/@/utils/http/axios';
import { ListResult } from '/@/api/model/baseModel';

/**
 * 项目群管理API
 */

// API URL前缀
const API_PREFIX = '/api/project/portfolio/program';

/**
 * 项目群对象接口
 */
export interface ProgramModel {
  id: string;
  code: string;
  name: string;
  description?: string;
  typeId?: string;
  managerId?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  budget?: number;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * 项目群表单接口
 */
export interface ProgramFormModel {
  code: string;
  name: string;
  description?: string;
  typeId?: string;
  managerId?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  budget?: number;
}

/**
 * 项目群查询参数接口
 */
export interface ProgramQueryParams {
  code?: string;
  name?: string;
  typeId?: string;
  managerId?: string;
  status?: string;
  keyword?: string;
  pageSize?: number;
  currentPage?: number;
  sort?: string;
  sidx?: string;
}

/**
 * 获取项目群列表
 * @param params 查询参数
 * @returns 项目群列表
 */
export const getProgramList = (params?: ProgramQueryParams) => {
  return defHttp.post<ListResult<ProgramModel>>({
    url: `${API_PREFIX}/getList`,
    data: params,
  });
};

/**
 * 根据项目群经理ID获取项目群列表
 * @param managerId 项目群经理ID
 * @returns 项目群列表
 */
export const getProgramListByManagerId = (managerId: string) => {
  return defHttp.get<ProgramModel[]>({
    url: `${API_PREFIX}/getListByManagerId/${managerId}`,
  });
};

/**
 * 根据项目群类型ID获取项目群列表
 * @param typeId 项目群类型ID
 * @returns 项目群列表
 */
export const getProgramListByTypeId = (typeId: string) => {
  return defHttp.get<ProgramModel[]>({
    url: `${API_PREFIX}/getListByTypeId/${typeId}`,
  });
};

/**
 * 根据状态获取项目群列表
 * @param status 状态
 * @returns 项目群列表
 */
export const getProgramListByStatus = (status: string) => {
  return defHttp.get<ProgramModel[]>({
    url: `${API_PREFIX}/getListByStatus/${status}`,
  });
};

/**
 * 获取项目群详情
 * @param id 项目群ID
 * @returns 项目群详情
 */
export const getProgramInfo = (id: string) => {
  return defHttp.get<ProgramModel>({
    url: `${API_PREFIX}/getInfo/${id}`,
  });
};

/**
 * 创建项目群
 * @param params 项目群创建参数
 * @returns 操作结果
 */
export const createProgram = (params: ProgramFormModel) => {
  return defHttp.post<string>({
    url: `${API_PREFIX}/create`,
    data: params,
  });
};

/**
 * 更新项目群
 * @param id 项目群ID
 * @param params 项目群更新参数
 * @returns 操作结果
 */
export const updateProgram = (id: string, params: ProgramFormModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/update/${id}`,
    data: params,
  });
};

/**
 * 删除项目群
 * @param id 项目群ID
 * @returns 操作结果
 */
export const deleteProgram = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/delete/${id}`,
  });
};

/**
 * 检查项目群编码是否存在
 * @param code 项目群编码
 * @param excludeId 排除的ID
 * @returns 是否存在
 */
export const checkProgramCodeExists = (code: string, excludeId?: string) => {
  return defHttp.get<boolean>({
    url: `${API_PREFIX}/checkCodeExists`,
    params: {
      code,
      excludeId,
    },
  });
};
