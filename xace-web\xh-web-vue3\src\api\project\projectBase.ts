import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetList = '/api/project/core/project/getList',
  GetListByProjectType = '/api/project/core/project/getListByProjectType',
  GetListByStatus = '/api/project/core/project/getListByStatus',
  GetListByManagerId = '/api/project/core/project/getListByManagerId',
  GetListByDeptId = '/api/project/core/project/getListByDeptId',
  GetInfo = '/api/project/core/project/getInfo',
  Create = '/api/project/core/project/create',
  Update = '/api/project/core/project/update',
  Delete = '/api/project/core/project/delete',
  UpdateStatus = '/api/project/core/project/updateStatus',
  UpdateHealth = '/api/project/core/project/updateHealth',
  CheckCodeExists = '/api/project/core/project/checkCodeExists',
  GetProjectStatistics = '/api/project/core/project/getProjectStatistics',
  GetProjectHealthStatistics = '/api/project/core/project/getProjectHealthStatistics',
  Archive = '/api/project/core/project/archive',
  Activate = '/api/project/core/project/activate',
  GetSelectList = '/api/project/core/project/getSelectList',
}

/**
 * 项目基础信息接口
 */

// 获取项目列表
export function getProjectList(params: any) {
  return defHttp.post({
    url: Api.GetList,
    data: params,
  });
}

// 根据项目类型获取项目列表
export function getProjectListByType(projectType: string) {
  return defHttp.get({
    url: `${Api.GetListByProjectType}/${projectType}`,
  });
}

// 根据项目状态获取项目列表
export function getProjectListByStatus(status: string) {
  return defHttp.get({
    url: `${Api.GetListByStatus}/${status}`,
  });
}

// 根据项目经理ID获取项目列表
export function getProjectListByManagerId(managerId: string) {
  return defHttp.get({
    url: `${Api.GetListByManagerId}/${managerId}`,
  });
}

// 根据部门ID获取项目列表
export function getProjectListByDeptId(deptId: string) {
  return defHttp.get({
    url: `${Api.GetListByDeptId}/${deptId}`,
  });
}

// 获取项目详情
export function getProjectInfo(id: string) {
  return defHttp.get({
    url: `${Api.GetInfo}/${id}`,
  });
}

// 创建项目
export function createProject(params: any) {
  return defHttp.post({
    url: Api.Create,
    data: params,
  });
}

// 更新项目
export function updateProject(id: string, params: any) {
  return defHttp.put({
    url: `${Api.Update}/${id}`,
    data: params,
  });
}

// 删除项目
export function deleteProject(id: string) {
  return defHttp.delete({
    url: `${Api.Delete}/${id}`,
  });
}

// 更新项目状态
export function updateProjectStatus(id: string, status: string) {
  return defHttp.put({
    url: `${Api.UpdateStatus}/${id}`,
    params: { status },
  });
}

// 更新项目健康度
export function updateProjectHealth(id: string, health: string) {
  return defHttp.put({
    url: `${Api.UpdateHealth}/${id}`,
    params: { health },
  });
}

// 检查项目编码是否存在
export function checkProjectCodeExists(code: string, excludeId?: string) {
  return defHttp.get({
    url: Api.CheckCodeExists,
    params: { code, excludeId },
  });
}

// 获取项目统计数据
export function getProjectStatistics(params: any) {
  return defHttp.post({
    url: Api.GetProjectStatistics,
    data: params,
  });
}

// 获取项目健康度统计
export function getProjectHealthStatistics(params: any) {
  return defHttp.post({
    url: Api.GetProjectHealthStatistics,
    data: params,
  });
}

// 项目归档
export function archiveProject(id: string) {
  return defHttp.put({
    url: `${Api.Archive}/${id}`,
  });
}

// 项目激活
export function activateProject(id: string) {
  return defHttp.put({
    url: `${Api.Activate}/${id}`,
  });
}

// 获取项目选择列表
export function getProjectSelectList(params?: { keyword?: string; status?: string }) {
  return defHttp.get({
    url: Api.GetSelectList,
    params,
  });
}
