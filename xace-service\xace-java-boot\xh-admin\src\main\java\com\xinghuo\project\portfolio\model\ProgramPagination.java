package com.xinghuo.project.portfolio.model;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目群分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "项目群分页查询参数")
public class ProgramPagination extends Pagination {

    /**
     * 项目群编码
     */
    @Schema(description = "项目群编码")
    private String code;

    /**
     * 项目群名称
     */
    @Schema(description = "项目群名称")
    private String name;

    /**
     * 项目群类型ID
     */
    @Schema(description = "项目群类型ID")
    private String typeId;

    /**
     * 项目群经理ID
     */
    @Schema(description = "项目群经理ID")
    private String managerId;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 关键字搜索（名称或编码）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}
