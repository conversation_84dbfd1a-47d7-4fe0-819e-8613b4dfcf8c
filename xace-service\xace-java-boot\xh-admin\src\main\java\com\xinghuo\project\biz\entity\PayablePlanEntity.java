package com.xinghuo.project.biz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目采购合同付款计划实体类
 * 对应数据库表：zz_proj_paycontract_money
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode
@TableName("zz_proj_paycontract_money")
public class PayablePlanEntity {

    /**
     * 付款ID
     */
    @TableId("cm_id")
    private String cmId;

    /**
     * 采购合同ID
     */
    @TableField("pc_id")
    private String pcId;

    /**
     * 付款条件
     */
    @TableField("fktj")
    private String fktj;

    /**
     * 比例
     */
    @TableField("ratio")
    private String ratio;

    /**
     * 付款金额
     */
    @TableField("cm_money")
    private BigDecimal cmMoney;

    /**
     * 付款负责人
     */
    @TableField("own_id")
    private String ownId;

    /**
     * 支付状态
     */
    @TableField("pay_status")
    private String payStatus;

    /**
     * 预付日期
     */
    @TableField("yufu_date")
    private Date yufuDate;

    /**
     * 付款日期
     */
    @TableField("fukuan_date")
    private Date fukuanDate;

    /**
     * 最后备注
     */
    @TableField("last_note")
    private String lastNote;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 部门ID
     */
    @TableField("dept_id")
    private String deptId;

    /**
     * 创建用户
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最后修改人
     */
    @TableField("last_modified_user_id")
    private String lastModifiedUserId;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 一部金额
     */
    @TableField("yb_amount")
    private BigDecimal ybAmount;

    /**
     * 二部金额
     */
    @TableField("eb_amount")
    private BigDecimal ebAmount;

    /**
     * 其他金额
     */
    @TableField("other_amount")
    private BigDecimal otherAmount;

    /**
     * 租户ID
     */
    @TableField("f_tenantid")
    private String tenantId;

    /**
     * 流程ID
     */
    @TableField("f_flowid")
    private String flowId;
}
