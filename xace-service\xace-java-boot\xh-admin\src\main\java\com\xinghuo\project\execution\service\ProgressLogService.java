package com.xinghuo.project.execution.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.execution.entity.log.ProgressLogEntity;
import com.xinghuo.project.execution.model.ProgressLogPagination;

import java.util.List;

/**
 * 进展日志服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ProgressLogService extends BaseService<ProgressLogEntity> {

    /**
     * 分页查询进展日志列表
     *
     * @param pagination 查询条件
     * @return 进展日志列表
     */
    List<ProgressLogEntity> getList(ProgressLogPagination pagination);

    /**
     * 根据项目ID查询进展日志列表
     *
     * @param projectId 项目ID
     * @return 进展日志列表
     */
    List<ProgressLogEntity> getListByProjectId(String projectId);

    /**
     * 根据合同ID查询进展日志列表
     *
     * @param contractId 合同ID
     * @return 进展日志列表
     */
    List<ProgressLogEntity> getListByContractId(String contractId);

    /**
     * 根据创建人ID查询进展日志列表
     *
     * @param creatorId 创建人ID
     * @return 进展日志列表
     */
    List<ProgressLogEntity> getListByCreatorId(String creatorId);

    /**
     * 根据ID查询进展日志信息
     *
     * @param id 进展日志ID
     * @return 进展日志信息
     */
    ProgressLogEntity getInfo(String id);

    /**
     * 创建进展日志
     *
     * @param entity 进展日志信息
     * @return 进展日志ID
     */
    String create(ProgressLogEntity entity);

    /**
     * 更新进展日志
     *
     * @param id 进展日志ID
     * @param entity 更新信息
     */
    void update(String id, ProgressLogEntity entity);

    /**
     * 删除进展日志
     *
     * @param id 进展日志ID
     */
    void delete(String id);
}
